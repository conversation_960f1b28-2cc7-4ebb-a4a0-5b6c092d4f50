from enum import Enum
from typing import Optional, Union, List, Dict, Any

from modules.web.web_searcher.abc_searcher import AbstractSearcher, SafeSearchLevel, FreshnessOption, ResultType


class SearchDepthOptions(str, Enum):
    BASIC = "basic"
    ADVANCED = "advanced"


class TopicOptions(str, Enum):
    GENERAL = "general"
    NEWS = "news"


class TimeRangeOptions(str, Enum):
    DAY = "day"
    WEEK = "week"
    MONTH = "month"
    YEAR = "year"
    D = "d"
    W = "w"
    M = "m"
    Y = "y"


class AnswerOptions(str, Enum):
    BASIC = "basic"
    ADVANCED = "advanced"


class TavilyWebSearcher(AbstractSearcher):
    """
    Implementation of AbstractSearcher for Tavily Search API.
    """

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the Tavily WebSearcher with an API key.

        Args:
            api_key: Tavily API key
        """
        api_key = "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"
        super().__init__(api_key)

        # Import here to avoid circular imports
        try:
            from tavily import AsyncTavilyClient
            self.client = AsyncTavilyClient(api_key=api_key)
        except ImportError:
            raise ImportError(
                "Tavily SDK is required for TavilyWebSearcher. "
                "Install it with: pip install tavily-python"
            )

    async def search(
            self,
            query: str,
            count: Optional[int] = 5,
            offset: Optional[int] = 0,
            safe_search: Optional[SafeSearchLevel] = SafeSearchLevel.MODERATE,
            language: Optional[str] = "en",
            country: Optional[str] = "US",
            freshness: Optional[Union[FreshnessOption, str]] = None,
            timeout: Optional[float] = 60.0,
            search_depth: Optional[Union[SearchDepthOptions, str]] = SearchDepthOptions.BASIC,
            topic: Optional[Union[TopicOptions, str]] = TopicOptions.GENERAL,
            time_range: Optional[Union[TimeRangeOptions, str]] = None,
            days: Optional[int] = 7,
            chunks_per_source: Optional[int] = 3,
            include_images: Optional[bool] = False,
            include_image_descriptions: Optional[bool] = False,
            include_answer: Optional[Union[bool, AnswerOptions, str]] = False,
            include_raw_content: Optional[bool] = False,
            include_domains: Optional[List[str]] = None,
            exclude_domains: Optional[List[str]] = None,
    ) -> List[Dict[str, Any]]:
        """
        Perform a web search using Tavily API.

        Args:
            query: The search query term
            count: Number of search results to return (max_results in Tavily API, max 20)
            offset: Number of results to skip (not directly supported by Tavily)
            safe_search: Not directly supported by Tavily
            language: Not directly supported by Tavily
            country: Not directly supported by Tavily
            freshness: Maps to time_range in Tavily
            timeout: Request timeout in seconds
            search_depth: Depth of search ("basic" or "advanced")
            topic: Search category ("general" or "news")
            time_range: Time range for search results
            days: Days back from current date (only for news topic)
            chunks_per_source: Number of content chunks per source (1-3)
            include_images: Whether to include images in the response
            include_image_descriptions: Whether to include image descriptions
            include_answer: Include an answer generated by LLM
            include_raw_content: Include cleaned and parsed HTML content
            include_domains: Domains to include in the search
            exclude_domains: Domains to exclude from the search

        Returns:
            List of search result dictionaries
        """
        if not self.api_key:
            raise ValueError("Tavily API key is required.")

        if not query:
            raise ValueError("Search query cannot be empty.")

        # Process parameters to match Tavily API expectations
        params = {
            "query": query,
            "max_results": min(count, 10)  # Tavily max is 10
        }

        # Handle search_depth parameter
        if search_depth:
            params["search_depth"] = (
                search_depth.value
                if isinstance(search_depth, Enum)
                else search_depth
            )

        # Handle topic parameter
        if topic:
            params["topic"] = (
                topic.value
                if isinstance(topic, Enum)
                else topic
            )

        # Handle time_range (maps to freshness in common params)
        if freshness and not time_range:
            # Map FreshnessOption to TimeRangeOptions
            freshness_map = {
                FreshnessOption.DAY: TimeRangeOptions.DAY,
                FreshnessOption.WEEK: TimeRangeOptions.WEEK,
                FreshnessOption.MONTH: TimeRangeOptions.MONTH,
                FreshnessOption.YEAR: TimeRangeOptions.YEAR,
            }
            if isinstance(freshness, FreshnessOption) and freshness in freshness_map:
                params["time_range"] = freshness_map[freshness].value
            else:
                params["time_range"] = freshness
        elif time_range:
            params["time_range"] = (
                time_range.value
                if isinstance(time_range, Enum)
                else time_range
            )

        # Handle days parameter (for news topic)
        if days is not None and topic in (TopicOptions.NEWS, "news"):
            params["days"] = days

        # Handle chunks_per_source parameter
        if chunks_per_source is not None:
            params["chunks_per_source"] = max(1, min(chunks_per_source, 3))  # Between 1-3

        # Handle boolean and complex parameters
        if include_images is not None:
            params["include_images"] = include_images

        if include_image_descriptions is not None:
            params["include_image_descriptions"] = include_image_descriptions

        if include_answer is not None:
            if isinstance(include_answer, bool):
                params["include_answer"] = include_answer
            else:
                params["include_answer"] = (
                    include_answer.value
                    if isinstance(include_answer, Enum)
                    else include_answer
                )

        if include_raw_content is not None:
            params["include_raw_content"] = include_raw_content

        if include_domains:
            params["include_domains"] = include_domains

        if exclude_domains:
            params["exclude_domains"] = exclude_domains

        # Handle timeout
        if timeout is not None:
            params["timeout"] = timeout

        try:
            # Use the Tavily AsyncTavilyClient to perform the search
            result = await self.client.search(**params)
            return self._process_response(result)
        except Exception as e:
            raise Exception(f"Error during Tavily search: {str(e)}")

    def _process_response(self, response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Process the Tavily API response into a standardized format.

        Args:
            response: The raw API response

        Returns:
            Processed list of search results
        """
        results: List[Dict[str, Any]] = []

        # Process results from Tavily response
        if "results" in response:
            for item in response["results"]:
                # Create base result with common fields
                result = {
                    "type": ResultType.WEBPAGE,
                    "title": item.get("title", ""),
                    "url": item.get("url", ""),
                    "snippet": item.get("content", ""),
                    "score": item.get("score", 0.0)
                }

                # Add raw_content if available
                if "raw_content" in item:
                    result["raw_content"] = item["raw_content"]

                # Add published_date if available (news topic)
                if "published_date" in item:
                    result["date_published"] = item["published_date"]

                results.append(result)

        # Process images if included
        if "images" in response:
            for img in response["images"]:
                # Check if it's a simple URL or an ImageResult object
                if isinstance(img, str):
                    image_result = {
                        "type": ResultType.IMAGE,
                        "title": "Image result",
                        "url": img
                    }
                else:
                    # Assume it's an ImageResult with url and description
                    image_result = {
                        "type": ResultType.IMAGE,
                        "title": img.get("description", "Image result"),
                        "url": img.get("url", ""),
                        "description": img.get("description", "")
                    }

                results.append(image_result)

        # If answer is included, add it as a special result at the top
        if "answer" in response and response["answer"]:
            results.insert(0, {
                "type": ResultType.INFOBOX,
                "title": "Tavily Answer",
                "description": response["answer"],
                "url": None
            })

        return results
