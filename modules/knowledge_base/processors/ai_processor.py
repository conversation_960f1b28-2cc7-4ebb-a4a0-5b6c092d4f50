"""
Pure Mistral OCR processor for optimal document structure extraction.
Combines Mistral's superior OCR with base processor agricultural keyword matching.
"""
import asyncio
import json
from typing import List, Dict, Any, Optional
from uuid import UUID
import httpx
import base64

from pydantic import BaseModel

from config import CONFIG
from modules.knowledge_base.processors.base_processor import BaseDocumentProcessor, ProcessingResult
from utils.logger import logger


class AIAnalysisResult(BaseModel):
    """Pydantic model for AI-enhanced document analysis response."""
    title: str = "Document"
    summary: str = "Agricultural document analysis"
    full_content: str = ""  # Complete extracted text content for chunking
    language: str = "en"
    document_category: str = "agricultural"
    crop_categories: List[str] = []
    topics: List[str] = []
    geographic_scope: List[str] = []
    seasonal_relevance: List[str] = []
    key_insights: List[str] = []
    visible_text: str = ""  # Additional visible text (signs, labels, etc.)
    pages_info: List[Dict[str, Any]] = []  # Page-level information and structure
    structured_data: Dict[str, Any] = {}  # Tables, charts, structured elements
    spatial_context: Dict[str, Any] = {}  # Layout and spatial relationships
    confidence_score: float = 0.8
    content_type: str = "text"


class AIProcessor(BaseDocumentProcessor):
    """
    Pure Mistral OCR processor for optimal document structure extraction.
    Combines Mistral's superior OCR with base processor agricultural keyword matching.
    """
    
    def __init__(self):
        super().__init__()
        # Support Mistral OCR compatible file types
        self.supported_extensions = {
            # Documents (Mistral OCR primary)
            '.pdf', '.docx', '.doc', '.pptx',
            # Images (Mistral OCR)
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp', '.avif',
            # Text formats (fallback to base processor)
            '.txt', '.md', '.rtf', '.csv', '.json', '.xml', '.html', '.htm'
        }
        
        # Mistral OCR configuration
        self.mistral_api_key = CONFIG.MISTRAL_API_KEY
        self.mistral_base_url = "https://api.mistral.ai/v1"
        
        if not self.mistral_api_key:
            logger.warning("MISTRAL_API_KEY not found. Mistral OCR will not be available.")
    
    async def process(self, file_content: bytes, document_id: UUID) -> ProcessingResult:
        """
        Process document using Mistral OCR + base processor agricultural metadata.
        
        Args:
            file_content: Raw file content as bytes
            document_id: Document ID for reference
            
        Returns:
            ProcessingResult optimized for chunking with perfect semantic boundaries
        """
        try:
            if not self.mistral_api_key:
                logger.error("Mistral API key not configured")
                return ProcessingResult(
                    success=False,
                    error="Mistral API key not configured"
                )
            
            # Determine media type
            media_type = self._determine_media_type(file_content)
            
            # Check if Mistral OCR supports this format
            if self._is_mistral_supported_format(media_type):
                # Use Mistral OCR for structure extraction
                mistral_result = await self._extract_with_mistral_ocr(file_content, media_type)
            else:
                # Fallback to text extraction for unsupported formats
                mistral_result = await self._fallback_text_extraction(file_content, media_type)
            
            # Extract agricultural metadata using base processor methods
            agricultural_metadata = await self._extract_agricultural_metadata(mistral_result['full_text'])
            
            # Create content in PDF processor format for optimal chunking
            optimized_content = self._create_chunker_optimized_content(mistral_result)
            
            # Build processing result with same interface as before
            result = ProcessingResult(
                success=True,
                content=optimized_content,
                title=mistral_result.get('title') or self._extract_title(mistral_result['full_text']),
                language=self._detect_language(mistral_result['full_text']) or 'en',
                total_pages=len(mistral_result.get('pages', [])),
                crop_categories=agricultural_metadata.get('crop_categories', []),
                topics=agricultural_metadata.get('topics', []),
                geographic_scope=agricultural_metadata.get('geographic_scope', []),
                seasonal_relevance=agricultural_metadata.get('seasonal_relevance', []),
                metadata={
                    'ai_enhanced': True,
                    'processing_method': 'mistral_ocr_agricultural',
                    'mistral_structure_preserved': True,
                    'pages_info': mistral_result.get('pages', []),
                    'structured_data': mistral_result.get('structured_data', {}),
                    'extraction_completeness': 'comprehensive',
                    'media_type': media_type,
                    'file_size_bytes': len(file_content),
                    'semantic_boundaries_preserved': True
                }
            )
            
            logger.info(f"Mistral OCR processor successfully analyzed document {document_id}")
            return result
            
        except Exception as e:
            logger.error(f"Mistral OCR processor error for document {document_id}: {e}")
            return ProcessingResult(
                success=False,
                error=f"Mistral OCR processing failed: {str(e)}"
            )
    
    def _determine_media_type(self, file_content: bytes) -> str:
        """Determine media type from file content magic bytes."""
        # Check for common file signatures
        if file_content.startswith(b'\xff\xd8\xff'):
            return 'image/jpeg'
        elif file_content.startswith(b'\x89PNG'):
            return 'image/png'
        elif file_content.startswith(b'GIF8'):
            return 'image/gif'
        elif file_content.startswith(b'RIFF') and b'WEBP' in file_content[:12]:
            return 'image/webp'
        elif file_content.startswith(b'\x00\x00\x00\x20ftypavif') or file_content.startswith(b'\x00\x00\x00\x18ftypavif'):
            return 'image/avif'
        elif file_content.startswith(b'BM'):
            return 'image/bmp'
        elif file_content.startswith(b'II*\x00') or file_content.startswith(b'MM\x00*'):
            return 'image/tiff'
        elif file_content.startswith(b'%PDF'):
            return 'application/pdf'
        elif file_content.startswith(b'PK\x03\x04'):
            # Could be DOCX, XLSX, or ZIP - check for Office signature
            try:
                if b'word/' in file_content[:1024]:
                    return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                elif b'ppt/' in file_content[:1024]:
                    return 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
                else:
                    return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            except:
                return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        elif file_content.startswith(b'\xd0\xcf\x11\xe0'):
            # Legacy Office format
            return 'application/msword'
        elif file_content.startswith(b'<html') or file_content.startswith(b'<!DOCTYPE'):
            return 'text/html'
        elif file_content.startswith(b'{') or file_content.startswith(b'['):
            return 'application/json'
        else:
            # Try to decode as text
            try:
                file_content.decode('utf-8')
                return 'text/plain'
            except:
                return 'application/octet-stream'
    
    async def _estimate_page_count(self, file_content: bytes) -> Optional[int]:
        """Estimate page count based on content and file type."""
        media_type = self._determine_media_type(file_content)
        
        if media_type.startswith('image/'):
            return 1
        elif media_type == 'application/pdf':
            # For PDF, try to count pages from content
            try:
                content_str = file_content.decode('latin-1', errors='ignore')
                # Simple PDF page count estimation
                page_count = content_str.count('/Type/Page')
                return max(1, page_count) if page_count > 0 else None
            except:
                return None
        elif media_type.startswith('text/'):
            try:
                # Estimate pages for text content
                text = file_content.decode('utf-8')
                # Rough estimation: 500 words per page, ~5 chars per word
                chars_per_page = 2500
                estimated_pages = max(1, len(text) // chars_per_page)
                return estimated_pages
            except:
                return None
        else:
            # For other formats, assume single page/unit
            return 1
    
    
    
    
    def _format_structured_data(self, structured_data: Dict[str, Any]) -> str:
        """Format structured data (tables, charts, forms) for text content."""
        formatted_parts = []
        
        # Format tables
        if 'tables' in structured_data and structured_data['tables']:
            for i, table in enumerate(structured_data['tables']):
                if isinstance(table, dict):
                    table_text = f"Table {i+1}:"
                    if 'caption' in table:
                        table_text += f" {table['caption']}"
                    if 'data' in table:
                        table_text += f"\n{table['data']}"
                    formatted_parts.append(table_text)
                elif isinstance(table, str):
                    formatted_parts.append(f"Table {i+1}: {table}")
        
        # Format charts
        if 'charts' in structured_data and structured_data['charts']:
            for i, chart in enumerate(structured_data['charts']):
                if isinstance(chart, dict):
                    chart_text = f"Chart {i+1}:"
                    if 'title' in chart:
                        chart_text += f" {chart['title']}"
                    if 'description' in chart:
                        chart_text += f"\n{chart['description']}"
                    formatted_parts.append(chart_text)
                elif isinstance(chart, str):
                    formatted_parts.append(f"Chart {i+1}: {chart}")
        
        # Format forms
        if 'forms' in structured_data and structured_data['forms']:
            for i, form in enumerate(structured_data['forms']):
                if isinstance(form, dict):
                    form_text = f"Form {i+1}:"
                    if 'fields' in form:
                        form_text += f"\nFields: {', '.join(form['fields'])}"
                    formatted_parts.append(form_text)
                elif isinstance(form, str):
                    formatted_parts.append(f"Form {i+1}: {form}")
        
        return "\n".join(formatted_parts)
    
    def _format_spatial_context(self, spatial_context: Dict[str, Any]) -> str:
        """Format spatial and layout context for text content."""
        formatted_parts = []
        
        if 'structure' in spatial_context and spatial_context['structure']:
            formatted_parts.append(f"Structure: {spatial_context['structure']}")
        
        if 'layout_type' in spatial_context and spatial_context['layout_type']:
            formatted_parts.append(f"Layout Type: {spatial_context['layout_type']}")
        
        if 'element_positions' in spatial_context and spatial_context['element_positions']:
            positions = spatial_context['element_positions']
            if isinstance(positions, list) and positions:
                formatted_parts.append(f"Element Layout: {len(positions)} positioned elements")
        
        return "\n".join(formatted_parts)
    
    def _is_mistral_supported_format(self, media_type: str) -> bool:
        """Check if media type is supported by Mistral OCR."""
        mistral_supported = {
            'application/pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',  # DOCX
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',  # PPTX
            'application/msword',  # DOC
            'image/jpeg',
            'image/jpg',  # Alternative JPEG MIME type
            'image/png',
            'image/gif',
            'image/bmp',
            'image/tiff',
            'image/webp',
            'image/avif'  # AVIF format support
        }
        return media_type in mistral_supported
    
    async def _extract_with_mistral_ocr(self, file_content: bytes, media_type: str) -> Dict[str, Any]:
        """Extract content using Mistral OCR API."""
        try:
            async with httpx.AsyncClient(timeout=120.0) as client:
                # Encode file content as base64
                file_b64 = base64.b64encode(file_content).decode('utf-8')
                
                # Determine the correct document type and data URL format
                if media_type.startswith('image/'):
                    # For images, use image_url type
                    document_payload = {
                        "type": "image_url",
                        "image_url": f"data:{media_type};base64,{file_b64}"
                    }
                else:
                    # For documents (PDF, DOCX, etc.), use document_url type
                    document_payload = {
                        "type": "document_url",
                        "document_url": f"data:{media_type};base64,{file_b64}"
                    }
                
                payload = {
                    "model": "mistral-ocr-latest",
                    "document": document_payload,
                    "include_image_base64": False  # Don't include images to save bandwidth
                }
                
                headers = {
                    "Authorization": f"Bearer {self.mistral_api_key}",
                    "Content-Type": "application/json"
                }
                
                # Call Mistral OCR API
                response = await client.post(
                    f"{self.mistral_base_url}/ocr",
                    headers=headers,
                    json=payload
                )
                
                if response.status_code != 200:
                    logger.error(f"Mistral OCR API error: {response.status_code} - {response.text}")
                    raise Exception(f"Mistral OCR API error: {response.status_code} - {response.text}")
                
                result = response.json()
                logger.info(f"Mistral OCR API response received with {len(result.get('pages', []))} pages")
                return self._parse_mistral_response(result)
                
        except Exception as e:
            logger.error(f"Mistral OCR extraction failed: {e}")
            # Fallback to basic text extraction
            return await self._fallback_text_extraction(file_content, media_type)
    
    def _parse_mistral_response(self, mistral_response: Dict[str, Any]) -> Dict[str, Any]:
        """Parse Mistral OCR response into structured format."""
        
        # Mistral OCR returns pages array with markdown content
        mistral_pages = mistral_response.get('pages', [])
        pages = []
        full_text_parts = []
        all_content = ""
        
        # Process each page from Mistral response
        for i, mistral_page in enumerate(mistral_pages):
            page_num = mistral_page.get('index', i) + 1 if 'index' in mistral_page else i + 1
            markdown_content = mistral_page.get('markdown', '')
            
            if markdown_content.strip():
                # Clean markdown content (remove image references for text extraction)
                cleaned_content = self._clean_markdown_content(markdown_content)
                
                pages.append({
                    'page_number': page_num,
                    'content': cleaned_content,
                    'raw_markdown': markdown_content,  # Keep original for reference
                    'source': 'mistral_ocr',
                    'dimensions': mistral_page.get('dimensions', {}),
                    'images': mistral_page.get('images', []),
                    'bbox_annotations': mistral_page.get('bbox_annotations', []),
                    'document_annotations': mistral_page.get('document_annotations', [])
                })
                full_text_parts.append(cleaned_content)
                all_content += markdown_content + "\n\n"
        
        # If no pages found, create fallback
        if not pages:
            logger.warning("No pages found in Mistral OCR response, creating fallback")
            fallback_content = str(mistral_response.get('error', 'No content extracted'))
            pages.append({
                'page_number': 1,
                'content': fallback_content,
                'source': 'mistral_ocr_fallback'
            })
            full_text_parts.append(fallback_content)
            all_content = fallback_content
        
        # Extract title from first page content
        title = self._extract_title_from_content(all_content)
        
        # Parse structured data (tables, etc.) from all content
        structured_data = self._extract_structured_data_from_content(all_content)
        
        return {
            'full_text': '\n\n'.join(full_text_parts),
            'pages': pages,
            'title': title,
            'structured_data': structured_data,
            'usage_info': mistral_response.get('usage_info', {}),
            'model': mistral_response.get('model', 'mistral-ocr-latest'),
            'document_annotations': mistral_response.get('document_annotations', {})
        }
    
    def _split_content_by_pages(self, content: str) -> List[str]:
        """Split content by natural page boundaries."""
        # Look for page indicators
        import re
        
        # Common page break patterns
        page_patterns = [
            r'\n\s*---+\s*\n',  # Horizontal rules
            r'\n\s*Page\s+\d+\s*\n',  # Page N
            r'\n\s*\d+\s*\n',  # Standalone numbers (potential page numbers)
        ]
        
        # Try each pattern
        for pattern in page_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                sections = re.split(pattern, content, flags=re.IGNORECASE)
                if len(sections) > 1:
                    return [section.strip() for section in sections if section.strip()]
        
        # If no clear page breaks, split by large gaps or return as single page
        large_gap_pattern = r'\n\s*\n\s*\n\s*\n'  # 3+ newlines
        sections = re.split(large_gap_pattern, content)
        if len(sections) > 3:  # Only split if we get reasonable number of sections
            return [section.strip() for section in sections if section.strip()]
        
        return [content]  # Return as single page
    
    def _extract_title_from_content(self, content: str) -> Optional[str]:
        """Extract title from Mistral OCR content."""
        lines = content.split('\n')
        
        for line in lines[:10]:  # Check first 10 lines
            line = line.strip()
            
            # Skip empty lines
            if not line:
                continue
                
            # Markdown header
            if line.startswith('#'):
                return line.lstrip('#').strip()
            
            # Title-like characteristics
            if (len(line) > 10 and len(line) < 100 and 
                line[0].isupper() and not line.endswith('.') and
                not any(word in line.lower() for word in ['page', 'figure', 'table'])):
                return line
        
        return None
    
    def _extract_structured_data_from_content(self, content: str) -> Dict[str, Any]:
        """Extract structured data (tables, etc.) from Mistral content."""
        structured_data = {
            'tables': [],
            'charts': [],
            'forms': []
        }
        
        # Look for markdown tables
        lines = content.split('\n')
        in_table = False
        current_table = []
        
        for line in lines:
            # Markdown table detection
            if '|' in line and line.count('|') >= 2:
                in_table = True
                current_table.append(line.strip())
            elif in_table and line.strip() == '':
                # End of table
                if current_table:
                    structured_data['tables'].append({
                        'markdown': '\n'.join(current_table),
                        'rows': len(current_table)
                    })
                    current_table = []
                in_table = False
            elif in_table:
                current_table.append(line.strip())
        
        # Add final table if exists
        if current_table:
            structured_data['tables'].append({
                'markdown': '\n'.join(current_table),
                'rows': len(current_table)
            })
        
        return structured_data
    
    def _clean_markdown_content(self, markdown_content: str) -> str:
        """Clean markdown content from Mistral OCR for better text extraction."""
        import re
        
        # Remove image references but keep alt text if meaningful
        content = re.sub(r'!\[([^]]+)]\([^)]+\)', r'\1', markdown_content)
        content = re.sub(r'!\[]\([^)]+\)', '', content)
        
        # Remove standalone image filenames
        content = re.sub(r'^\s*[a-zA-Z0-9\-_]+\.(jpeg|jpg|png|gif|webp|avif|bmp|tiff)\s*$', '', content, flags=re.MULTILINE)
        
        # Convert markdown headers to plain text (preserve structure)
        content = re.sub(r'^#{1,6}\s*(.+)$', r'\1', content, flags=re.MULTILINE)
        
        # Convert markdown lists to plain text
        content = re.sub(r'^\s*[-*+]\s*', '• ', content, flags=re.MULTILINE)
        content = re.sub(r'^\s*\d+\.\s*', '', content, flags=re.MULTILINE)
        
        # Remove markdown links but keep text
        content = re.sub(r'\[([^]]+)]\([^)]+\)', r'\1', content)
        
        # Remove markdown formatting (bold, italic)
        content = re.sub(r'\*\*([^*]+)\*\*', r'\1', content)  # Bold
        content = re.sub(r'\*([^*]+)\*', r'\1', content)  # Italic
        content = re.sub(r'`([^`]+)`', r'\1', content)  # Code
        
        # Remove figure references and captions
        content = re.sub(r'Fig(ure)?\s+\d+(\.\d+)?:\s*', '', content, flags=re.IGNORECASE)
        content = re.sub(r'Table\s+\d+(\.\d+)?:\s*', '', content, flags=re.IGNORECASE)
        
        # Clean up excessive whitespace
        content = re.sub(r'\n\s*\n\s*\n+', '\n\n', content)
        content = re.sub(r'[ \t]+', ' ', content)  # Normalize spaces but preserve newlines
        content = content.strip()
        
        return content
    
    async def _fallback_text_extraction(self, file_content: bytes, media_type: str) -> Dict[str, Any]:
        """Fallback text extraction when Mistral OCR is not available or fails."""
        
        try:
            if media_type.startswith('text/') or media_type.startswith('application/json') or media_type.startswith('text/html'):
                content_text = file_content.decode('utf-8')
            else:
                content_text = f"Binary content of type {media_type} - {len(file_content)} bytes"
        except UnicodeDecodeError:
            content_text = f"Binary content of type {media_type} - {len(file_content)} bytes (decode failed)"
        
        return {
            'full_text': content_text,
            'pages': [{
                'page_number': 1,
                'content': content_text,
                'source': 'fallback'
            }],
            'title': self._extract_title(content_text) if hasattr(self, '_extract_title') else 'Document',
            'structured_data': {'tables': [], 'charts': [], 'forms': []}
        }
    
    def _create_chunker_optimized_content(self, mistral_result: Dict[str, Any]) -> str:
        """Create content optimized for chunking - PDF processor format."""
        content_parts = []
        
        # Format pages with simple markers that work with existing chunker
        for page in mistral_result.get('pages', []):
            page_num = page['page_number']
            page_content = page['content']
            
            if page_content.strip():
                # Use EXACT same format as PDF processor for compatibility
                content_parts.append(f"--- Page {page_num} ---")
                content_parts.append(page_content)
        
        # Add structured data as separate sections if available
        structured_data = mistral_result.get('structured_data', {})
        if structured_data.get('tables'):
            content_parts.append("--- Tables ---")
            for i, table in enumerate(structured_data['tables']):
                table_content = table.get('markdown', str(table))
                content_parts.append(f"Table {i+1}:\n{table_content}")
        
        return "\n\n".join(content_parts)