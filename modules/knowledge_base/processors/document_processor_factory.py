"""
Document processor factory for creating appropriate processors based on document type.
Includes AI processor routing for small files that meet size and page constraints.
"""
from typing import Dict, Type, Optional

from models.knowledge_base import DocumentType
from modules.knowledge_base.processors.base_processor import BaseDocumentProcessor
from utils.logger import logger


class DocumentProcessorFactory:
    """Factory for creating document processors based on document type."""

    def __init__(self):
        self._processors: Dict[DocumentType, Type[BaseDocumentProcessor]] = {}
        self._ai_processor_class = None
        self._register_processors()

        # AI processor constraints
        self.ai_max_file_size = 5 * 1024 * 1024  # 5MB
        self.ai_max_pages = 15

    def _register_processors(self):
        """Register all available processors."""
        # Import processors lazily to avoid circular imports
        from modules.knowledge_base.processors.pdf_processor import PDFProcessor
        from modules.knowledge_base.processors.text_processor import TextProcessor
        from modules.knowledge_base.processors.image_processor import ImageProcessor
        from modules.knowledge_base.processors.csv_processor import CSVProcessor
        from modules.knowledge_base.processors.json_processor import JSONProcessor
        from modules.knowledge_base.processors.html_processor import HTMLProcessor
        from modules.knowledge_base.processors.ai_processor import AIProcessor

        self._processors = {
            DocumentType.PDF: PDFProcessor,
            DocumentType.TXT: TextProcessor,
            DocumentType.IMAGE: ImageProcessor,
            DocumentType.CSV: CSVProcessor,
            DocumentType.JSON: JSONProcessor,
            DocumentType.HTML: HTMLProcessor,
            DocumentType.DOCX: TextProcessor,  # Fallback to text processor
            DocumentType.OTHER: TextProcessor  # Fallback to text processor
        }

        # Store AI processor class for intelligent routing
        self._ai_processor_class = AIProcessor

    def get_processor(self, document_type: DocumentType) -> BaseDocumentProcessor:
        """
        Get appropriate processor for document type.
        
        Args:
            document_type: Type of document to process

        Returns:
            Document processor instance
            
        Raises:
            ValueError: If document type is not supported
        """
        if document_type not in self._processors:
            raise ValueError(f"No processor available for document type: {document_type}")

        # Use standard processor
        processor_class = self._processors[document_type]
        return processor_class()

    def get_ai_processor(self) -> BaseDocumentProcessor:
        return self._ai_processor_class()

    def should_use_ai_processor(self, file_content: bytes, document_type: DocumentType) -> bool:
        """
        Determine if file should be processed by AI processor based on size and page constraints.
        
        Args:
            file_content: Raw file content
            document_type: Type of document
            
        Returns:
            True if file should use AI processor, False otherwise
        """
        if not self._ai_processor_class:
            return False

        # Check file size constraint
        if len(file_content) > self.ai_max_file_size:
            logger.debug(f"File too large for AI processor: {len(file_content)} bytes > {self.ai_max_file_size}")
            return False

        # Estimate page count for supported formats
        try:
            page_count = self._estimate_page_count(file_content, document_type)
            if page_count and page_count > self.ai_max_pages:
                logger.debug(f"File has too many pages for AI processor: {page_count} > {self.ai_max_pages}")
                return False
        except Exception as e:
            logger.debug(f"Could not estimate page count: {e}")
            # If we can't estimate, allow AI processing for small files
            pass

        # Check if AI processor supports this document type
        ai_processor = self._ai_processor_class()
        file_extension = self._get_extension_for_document_type(document_type)

        if file_extension and file_extension in ai_processor.supported_extensions:
            logger.debug(f"File qualifies for AI processing: size={len(file_content)}, type={document_type}")
            return True

        return False

    def _estimate_page_count(self, file_content: bytes, document_type: DocumentType) -> Optional[int]:
        """Estimate page count based on content and document type."""
        if document_type == DocumentType.IMAGE:
            return 1
        elif document_type == DocumentType.PDF:
            try:
                content_str = file_content.decode('latin-1', errors='ignore')
                page_count = content_str.count('/Type/Page')
                return max(1, page_count) if page_count > 0 else None
            except:
                return None
        elif document_type in [DocumentType.TXT, DocumentType.HTML]:
            try:
                text = file_content.decode('utf-8')
                # Rough estimation: 500 words per page, ~5 chars per word
                chars_per_page = 2500
                estimated_pages = max(1, len(text) // chars_per_page)
                return estimated_pages
            except:
                return None
        else:
            # For other formats (CSV, JSON, DOCX), assume single logical page/unit
            return 1

    def _get_extension_for_document_type(self, document_type: DocumentType) -> Optional[str]:
        """Get a representative file extension for document type."""
        extension_mapping = {
            DocumentType.PDF: '.pdf',
            DocumentType.TXT: '.txt',
            DocumentType.IMAGE: '.jpg',
            DocumentType.CSV: '.csv',
            DocumentType.JSON: '.json',
            DocumentType.HTML: '.html',
            DocumentType.DOCX: '.docx',
            DocumentType.OTHER: '.txt'
        }
        return extension_mapping.get(document_type)

    def register_processor(self, document_type: DocumentType, processor_class: Type[BaseDocumentProcessor]):
        """Register a custom processor for a document type."""
        self._processors[document_type] = processor_class

    def get_supported_types(self) -> list[DocumentType]:
        """Get list of supported document types."""
        return list(self._processors.keys())
