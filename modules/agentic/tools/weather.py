import re
from datetime import datetime, timedelta
from typing import Dict, Any

from modules.external.weather_api import WeatherAPI
from schemas.weather import ForecastDay, WeatherAPIException
from utils.logger import logger


def _validate_location(location: str) -> str:
    """Validate and sanitize location parameter."""
    if not location or not location.strip():
        raise ValueError("Location cannot be empty")

    location = location.strip()
    if len(location) > 100:
        raise ValueError("Location name too long (max 100 characters)")

    return location


def _validate_days(days: int, max_days: int = 10) -> int:
    """Validate days parameter for forecast functions."""
    if not isinstance(days, int):
        try:
            days = int(days)
        except (ValueError, TypeError):
            raise ValueError("Days must be a valid integer")

    if days < 1 or days > max_days:
        raise ValueError(f"Days must be between 1 and {max_days}")

    return days


def _validate_date(date: str) -> str:
    """Validate date format and ensure it's within acceptable range."""
    if not date or not date.strip():
        raise ValueError("Date cannot be empty")

    date = date.strip()

    # Check format YYYY-MM-DD
    if not re.match(r'^\d{4}-\d{2}-\d{2}$', date):
        raise ValueError("Date must be in YYYY-MM-DD format")

    try:
        parsed_date = datetime.strptime(date, '%Y-%m-%d')
    except ValueError:
        raise ValueError("Invalid date format. Use YYYY-MM-DD")

    # Check if date is within last year
    one_year_ago = datetime.now() - timedelta(days=365)
    if parsed_date < one_year_ago:
        raise ValueError("Date cannot be more than 1 year in the past")

    if parsed_date > datetime.now():
        raise ValueError("Date cannot be in the future for historical data")

    return date


async def get_current_agricultural_weather(location: str) -> Dict[str, Any]:
    """
    Get current weather conditions for agricultural decision-making.

    This tool provides real-time weather data including temperature, humidity,
    precipitation, wind, and UV index - crucial for day-to-day agricultural operations
    like irrigation scheduling, field work planning, and crop protection.

    Args:
        location: City name, ZIP code, or GPS coordinates (lat,lon)

    Returns:
        Dict containing current weather conditions relevant for agriculture
    """
    logger.debug(f"Tool: get_current_agricultural_weather called with location: {location}")
    try:
        location = _validate_location(location)
        api = WeatherAPI()
        data = await api.get_current_weather(location, aqi=True)

        return {
            "location": f"{data['location']['name']}, {data['location']['region']}, {data['location']['country']}",
            "local_time": data['location']['localtime'],
            "temp_c": data['current']['temp_c'],
            "condition": data['current']['condition']['text'],
            "wind_kph": data['current']['wind_kph'],
            "wind_direction": data['current']['wind_dir'],
            "pressure_mb": data['current']['pressure_mb'],
            "precipitation_mm": data['current']['precip_mm'],
            "humidity": data['current']['humidity'],
            "cloud_cover": data['current']['cloud'],
            "feels_like_c": data['current']['feelslike_c'],
            "uv_index": data['current']['uv'],
            "air_quality": data['current'].get('air_quality', {}).get('us-epa-index', None),
            "_citation": {
                "source": "WeatherAPI.com",
                "url": "https://www.weatherapi.com/",
                "retrieved_at": data['location']['localtime']
            }
        }
    except ValueError as e:
        logger.error(f"Validation error in get_current_agricultural_weather: {str(e)}")
        return {
            "error": str(e),
            "error_type": "validation_error",
            "message": "Please check your input parameters and try again."
        }
    except WeatherAPIException as e:
        logger.error(f"Weather API error in get_current_agricultural_weather: {str(e)}")
        return {
            "error": "Weather service temporarily unavailable",
            "error_type": "api_error",
            "message": "Unable to retrieve weather data. Please try again later."
        }
    except Exception as e:
        logger.error(f"Unexpected error in get_current_agricultural_weather: {str(e)}")
        return {
            "error": "An unexpected error occurred",
            "error_type": "system_error",
            "message": "Please try again or contact support if the problem persists."
        }


async def get_agricultural_forecast(location: str, days: int = 3):
    """
    Get weather forecast for agricultural planning.

    This tool provides future weather predictions essential for planning agricultural
    activities like planting, harvesting, pesticide application, and irrigation.
    The forecast helps farmers anticipate and prepare for upcoming weather conditions
    that might affect crop health and field operations.

    Args:
        location: City name, ZIP code, or GPS coordinates
        days: Number of forecast days between 1 and 10

    Returns:
        List of daily forecasts with key agricultural weather metrics
    """

    logger.debug(f"Tool: get_agricultural_forecast called with location: {location} and days: {days}")
    try:
        location = _validate_location(location)
        days = _validate_days(days)
        api = WeatherAPI()
        data = await api.get_forecast(location, days=days)

        forecast_days = []
        for day in data['forecast']['forecastday']:
            forecast_days.append(ForecastDay(
                date=day['date'],
                max_temp_c=day['day']['maxtemp_c'],
                min_temp_c=day['day']['mintemp_c'],
                avg_temp_c=day['day']['avgtemp_c'],
                max_wind_kph=day['day']['maxwind_kph'],
                total_precip_mm=day['day']['totalprecip_mm'],
                avg_humidity=day['day']['avghumidity'],
                chance_of_rain=day['day']['daily_chance_of_rain'],
                uv=day['day']['uv'],
                condition=day['day']['condition']['text']
            ))

        return {"data": forecast_days, "_citation": {
            "source": "WeatherAPI.com",
            "url": "https://www.weatherapi.com/",
        }}

    except ValueError as e:
        logger.error(f"Validation error in get_agricultural_forecast: {str(e)}")
        return {
            "error": str(e),
            "error_type": "validation_error",
            "message": "Please check your input parameters and try again."
        }
    except WeatherAPIException as e:
        logger.error(f"Weather API error in get_agricultural_forecast: {str(e)}")
        return {
            "error": "Weather service temporarily unavailable",
            "error_type": "api_error",
            "message": "Unable to retrieve forecast data. Please try again later."
        }
    except Exception as e:
        logger.error(f"Unexpected error in get_agricultural_forecast: {str(e)}")
        return {
            "error": "An unexpected error occurred",
            "error_type": "system_error",
            "message": "Please try again or contact support if the problem persists."
        }


async def get_precipitation_forecast(location: str, days: int = 7) -> Dict[str, Any]:
    """
    Get precipitation forecast for irrigation planning.

    This specialized tool focuses on precipitation forecasts which are critical for
    irrigation planning, drought management, flood prevention, and scheduling
    moisture-sensitive farm operations like planting, fertilizing, and harvesting.

    Args:
        location: City name, ZIP code, or GPS coordinates
        days: Number of forecast days between 1 and 10

    Returns:
        Dict mapping dates to precipitation amounts in mm
    """

    logger.debug(f"Tool: get_precipitation_forecast called with location: {location} and days: {days}")
    try:
        location = _validate_location(location)
        days = _validate_days(days)
        api = WeatherAPI()
        data = await api.get_forecast(location, days=days)

        precipitation = {}
        for day in data['forecast']['forecastday']:
            precipitation[day['date']] = day['day']['totalprecip_mm']

        precipitation["_citation"] = {
            "source": "WeatherAPI.com",
            "url": "https://www.weatherapi.com/",
        }
        return precipitation

    except ValueError as e:
        logger.error(f"Validation error in get_precipitation_forecast: {str(e)}")
        return {
            "error": str(e),
            "error_type": "validation_error",
            "message": "Please check your input parameters and try again."
        }
    except WeatherAPIException as e:
        logger.error(f"Weather API error in get_precipitation_forecast: {str(e)}")
        return {
            "error": "Weather service temporarily unavailable",
            "error_type": "api_error",
            "message": "Unable to retrieve precipitation data. Please try again later."
        }
    except Exception as e:
        logger.error(f"Unexpected error in get_precipitation_forecast: {str(e)}")
        return {
            "error": "An unexpected error occurred",
            "error_type": "system_error",
            "message": "Please try again or contact support if the problem persists."
        }


async def get_agricultural_historical_weather(location: str, date: str) -> Dict[str, Any]:
    """
    Get historical weather data for agricultural analysis.

    This tool provides access to past weather data essential for analyzing crop
    performance, understanding pest/disease outbreaks, calibrating agricultural models,
    and making data-driven decisions based on historical weather patterns.

    Args:
        location: City name, ZIP code, or GPS coordinates
        date: Date in YYYY-MM-DD format, up to 1 year in the past

    Returns:
        Dict containing historical weather data relevant for agriculture
    """

    logger.debug(f"Tool: get_agricultural_historical_weather called with location: {location} and date: {date}")
    try:
        location = _validate_location(location)
        date = _validate_date(date)
        api = WeatherAPI()
        data = await api.get_historical_weather(location, date)

        day_data = data['forecast']['forecastday'][0]['day']

        return {
            "location": f"{data['location']['name']}, {data['location']['region']}, {data['location']['country']}",
            "date": date,
            "max_temp_c": day_data['maxtemp_c'],
            "min_temp_c": day_data['mintemp_c'],
            "avg_temp_c": day_data['avgtemp_c'],
            "max_wind_kph": day_data['maxwind_kph'],
            "total_precip_mm": day_data['totalprecip_mm'],
            "avg_humidity": day_data['avghumidity'],
            "uv": day_data['uv'],
            "condition": day_data['condition']['text'],
            "_citation": {
                "source": "WeatherAPI.com",
                "url": "https://www.weatherapi.com/",
            }
        }

    except ValueError as e:
        logger.error(f"Validation error in get_agricultural_historical_weather: {str(e)}")
        return {
            "error": str(e),
            "error_type": "validation_error",
            "message": "Please check your input parameters and try again."
        }
    except WeatherAPIException as e:
        logger.error(f"Weather API error in get_agricultural_historical_weather: {str(e)}")
        return {
            "error": "Weather service temporarily unavailable",
            "error_type": "api_error",
            "message": "Unable to retrieve historical data. Please try again later."
        }
    except Exception as e:
        logger.error(f"Unexpected error in get_agricultural_historical_weather: {str(e)}")
        return {
            "error": "An unexpected error occurred",
            "error_type": "system_error",
            "message": "Please try again or contact support if the problem persists."
        }


async def check_frost_risk(location: str, days: int = 7) -> Dict[str, Any]:
    """
    Check for potential frost risk in the forecast period.

    This specialized agricultural tool identifies days with potential frost risk, which
    is critical for protecting sensitive crops, planning frost mitigation strategies,
    and making informed decisions about planting timing and crop selection.

    Args:
        location: City name, ZIP code, or GPS coordinates
        days: Number of forecast days between 1 and 10

    Returns:
        Dict mapping dates to frost risk assessment including minimum temperature and risk level
    """

    logger.debug(f"Tool: check_frost_risk called with location: {location} and days: {days}")
    try:
        location = _validate_location(location)
        days = _validate_days(days)
        api = WeatherAPI()
        data = await api.get_forecast(location, days=days, alerts=True)

        frost_risk = {}
        for day in data['forecast']['forecastday']:
            # Get minimum temperature for the day
            min_temp = day['day']['mintemp_c']

            # Get hourly temperature to check for frost during growing hours
            hourly_data = day['hour']
            night_mins = []
            for hour_data in hourly_data:
                hour = int(hour_data['time'].split()[1].split(':')[0])
                # Consider night hours (typically when frost damage occurs)
                if hour < 8 or hour >= 18:
                    night_mins.append(hour_data['temp_c'])

            night_min_temp = min(night_mins) if night_mins else min_temp

            # Determine frost risk
            risk_level = "None"
            if night_min_temp <= 0:
                risk_level = "High"
            elif night_min_temp <= 2:
                risk_level = "Moderate"
            elif night_min_temp <= 4:
                risk_level = "Low"

            frost_risk[day['date']] = {
                "min_temp_c": min_temp,
                "night_min_temp_c": night_min_temp,
                "risk_level": risk_level
            }
        frost_risk["_citation"] = {
            "source": "WeatherAPI.com",
            "url": "https://www.weatherapi.com/",
        }
        return frost_risk

    except ValueError as e:
        logger.error(f"Validation error in check_frost_risk: {str(e)}")
        return {
            "error": str(e),
            "error_type": "validation_error",
            "message": "Please check your input parameters and try again."
        }
    except WeatherAPIException as e:
        logger.error(f"Weather API error in check_frost_risk: {str(e)}")
        return {
            "error": "Weather service temporarily unavailable",
            "error_type": "api_error",
            "message": "Unable to retrieve frost risk data. Please try again later."
        }
    except Exception as e:
        logger.error(f"Unexpected error in check_frost_risk: {str(e)}")
        return {
            "error": "An unexpected error occurred",
            "error_type": "system_error",
            "message": "Please try again or contact support if the problem persists."
        }


async def get_growing_condition_assessment(location: str, days: int = 3) -> Dict[str, Any]:
    """
    Assess overall growing conditions for the upcoming days.

    This comprehensive tool evaluates multiple weather factors that affect crop growth
    and development, providing an overall assessment of growing conditions. It helps
    farmers plan optimal times for planting, fertilizing, and other growth-sensitive
    agricultural activities.

    Args:
        location: City name, ZIP code, or GPS coordinates
        days: Number of forecast days between 1 and 10

    Returns:
        Dict mapping dates to growing condition assessments
    """

    logger.debug(f"Tool: get_growing_condition_assessment called with location: {location} and days: {days}")
    try:
        location = _validate_location(location)
        days = _validate_days(days)
        api = WeatherAPI()
        data = await api.get_forecast(location, days=days)

        growing_conditions = {}
        for day in data['forecast']['forecastday']:
            date = day['date']
            day_data = day['day']

            # Calculate GDD (Growing Degree Days) using base 10°C
            base_temp = 10.0
            avg_temp = day_data['avgtemp_c']
            gdd = max(0, avg_temp - base_temp)

            # Assess temperature conditions
            temp_assessment = "Favorable"
            if day_data['maxtemp_c'] > 35 or day_data['mintemp_c'] < 5:
                temp_assessment = "Stressful"
            elif day_data['maxtemp_c'] > 30 or day_data['mintemp_c'] < 10:
                temp_assessment = "Marginal"

            # Assess moisture conditions
            moisture_assessment = "Adequate"
            if day_data['totalprecip_mm'] > 25:
                moisture_assessment = "Excessive"
            elif day_data['totalprecip_mm'] > 10:
                moisture_assessment = "High"
            elif day_data['totalprecip_mm'] < 0.5 and day_data['avghumidity'] < 40:
                moisture_assessment = "Dry"

            # Overall growing condition
            if temp_assessment == "Stressful" or moisture_assessment == "Excessive":
                overall = "Poor"
            elif temp_assessment == "Marginal" or moisture_assessment == "Dry":
                overall = "Fair"
            else:
                overall = "Good"

            growing_conditions[date] = {
                "temperature": {
                    "min_c": day_data['mintemp_c'],
                    "max_c": day_data['maxtemp_c'],
                    "avg_c": day_data['avgtemp_c'],
                    "assessment": temp_assessment
                },
                "moisture": {
                    "precipitation_mm": day_data['totalprecip_mm'],
                    "humidity": day_data['avghumidity'],
                    "assessment": moisture_assessment
                },
                "growing_degree_days": gdd,
                "wind_kph": day_data['maxwind_kph'],
                "uv_index": day_data['uv'],
                "overall_conditions": overall
            }
        growing_conditions["_citation"] = {
            "source": "WeatherAPI.com",
            "url": "https://www.weatherapi.com/",
        }
        return growing_conditions

    except ValueError as e:
        logger.error(f"Validation error in get_growing_condition_assessment: {str(e)}")
        return {
            "error": str(e),
            "error_type": "validation_error",
            "message": "Please check your input parameters and try again."
        }
    except WeatherAPIException as e:
        logger.error(f"Weather API error in get_growing_condition_assessment: {str(e)}")
        return {
            "error": "Weather service temporarily unavailable",
            "error_type": "api_error",
            "message": "Unable to retrieve growing condition data. Please try again later."
        }
    except Exception as e:
        logger.error(f"Unexpected error in get_growing_condition_assessment: {str(e)}")
        return {
            "error": "An unexpected error occurred",
            "error_type": "system_error",
            "message": "Please try again or contact support if the problem persists."
        }
