from fastapi import APIRouter
from minio import Minio

from config import CONFIG
from modules.automation.automation_service import AutomationService
from modules.automation.tools.purchase_order import PurchaseOrder
from schemas import BaseModel

router = APIRouter(prefix="/automations", tags=["AI Automations"])


class PurchaseOrderRequest(BaseModel):
    file_name: str = None
    advisor_note: str = None


@router.post("/purchase-order", response_model=PurchaseOrder)
async def generate_purchase_order(
        data: PurchaseOrderRequest
):
    if data.file_name:
        client = Minio(
            CONFIG.MINIO_ENDPOINT,
            access_key=CONFIG.MINIO_ACCESS_KEY_ID,
            secret_key=CONFIG.MINIO_SECRET_ACCESS_KEY,
        )

        # Get data of an object.
        response = None
        try:
            response = client.get_object(CONFIG.MINIO_EPHY_BUCKET, data.file_name)
            # Read data from response with fallback encoding
            raw_data = response.data
            text_content = None

            try:
                text_content = raw_data.decode('utf-8')
            except UnicodeDecodeError:
                # Try common encodings for agricultural/French text
                for encoding in ['latin-1', 'cp1252', 'iso-8859-1']:
                    try:
                        text_content = raw_data.decode(encoding)
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    # Last resort: decode with error handling
                    text_content = raw_data.decode('utf-8', errors='replace')

            advisor_note = text_content

        finally:
            if response:
                response.close()
                response.release_conn()
    else:
        advisor_note = data.advisor_note
    order = await AutomationService().generate_purchase_order(advisor_note)
    return order
