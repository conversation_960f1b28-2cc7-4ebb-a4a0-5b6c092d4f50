"""
Enhanced text normalization for E-Phy agricultural database matching.
Handles accents, hyphens, spaces, and database-specific patterns.
"""

import unicodedata
import re
from typing import List, Set, Tuple


class EnhancedTextNormalizer:
    """Comprehensive text normalization matching database patterns."""
    
    @staticmethod
    def normalize_for_search(text: str) -> str:
        """Apply all normalization patterns found in database."""
        if not text:
            return ""
            
        # Remove accents (critical missing piece in original)
        text = unicodedata.normalize('NFD', text)
        text = ''.join(c for c in text if unicodedata.category(c) != 'Mn')
        
        # Apply database-specific patterns
        text = text.lower().strip()
        text = text.replace('-', ' ')  # Handle hyphens like database  
        text = text.replace('  ', ' ')  # Normalize multiple spaces
        text = re.sub(r'\s+', ' ', text)  # Clean whitespace
        
        return text
    
    @staticmethod  
    def normalize_target_name(text: str) -> str:
        """Target-specific normalization including ou→ious pattern."""
        text = EnhancedTextNormalizer.normalize_for_search(text)
        
        # Handle database ou→ious pattern for targets (mildiou → mildious)
        text = text.replace('ou', 'ious')
        
        # Handle parenthetical patterns found in database
        text = re.sub(r'\(s\)', '', text)  # Remove (s) patterns
        text = re.sub(r'\(\d+\)', '', text)  # Remove (1), (2) patterns
        text = re.sub(r'\([^)]*\)', '', text)  # Remove other parenthetical content
        
        return text.strip()
    
    @staticmethod
    def normalize_crop_name(text: str) -> str:
        """Crop-specific normalization."""
        text = EnhancedTextNormalizer.normalize_for_search(text)
        
        # Handle specific crop name patterns
        text = re.sub(r'\s*-\s*', ' ', text)  # Normalize hyphens with spaces
        text = re.sub(r'\s+', ' ', text)  # Clean multiple spaces
        
        return text.strip()
    
    @staticmethod
    def extract_search_tokens(text: str) -> Set[str]:
        """Extract meaningful search tokens for partial matching."""
        normalized = EnhancedTextNormalizer.normalize_for_search(text)
        tokens = set(normalized.split())
        
        # Add partial tokens for compound words
        extended_tokens = set(tokens)
        for token in tokens:
            if len(token) > 6:  # For longer words, add prefixes
                extended_tokens.add(token[:4])
                extended_tokens.add(token[:6])
        
        # Remove very short tokens that aren't meaningful
        meaningful_tokens = {t for t in extended_tokens if len(t) >= 3}
        
        return meaningful_tokens
    
    @staticmethod
    def calculate_token_similarity(text1: str, text2: str) -> float:
        """Calculate token-based similarity between two texts."""
        tokens1 = EnhancedTextNormalizer.extract_search_tokens(text1)
        tokens2 = EnhancedTextNormalizer.extract_search_tokens(text2)
        
        if not tokens1 or not tokens2:
            return 0.0
        
        intersection = tokens1 & tokens2
        union = tokens1 | tokens2
        
        return len(intersection) / len(union) if union else 0.0
    
    @staticmethod
    def normalize_synonym_list(synonyms: str) -> List[str]:
        """Normalize and clean synonym lists from database."""
        if not synonyms:
            return []
        
        # Split by comma and normalize each synonym
        raw_synonyms = [s.strip() for s in synonyms.split(',')]
        normalized_synonyms = []
        
        for synonym in raw_synonyms:
            if synonym:  # Skip empty strings
                normalized = EnhancedTextNormalizer.normalize_for_search(synonym)
                if len(normalized) >= 2:  # Skip very short synonyms
                    normalized_synonyms.append(normalized)
        
        return normalized_synonyms
    
    @staticmethod
    def is_meaningful_match(user_input: str, matched_text: str, min_length: int = 3) -> bool:
        """Check if a match is meaningful (not just a tiny substring)."""
        user_norm = EnhancedTextNormalizer.normalize_for_search(user_input)
        match_norm = EnhancedTextNormalizer.normalize_for_search(matched_text)
        
        # Both should be meaningful length
        if len(user_norm) < min_length or len(match_norm) < min_length:
            return False
        
        # Check for reasonable overlap
        if user_norm in match_norm or match_norm in user_norm:
            return True
        
        # Check token overlap
        return EnhancedTextNormalizer.calculate_token_similarity(user_input, matched_text) >= 0.3


class FuzzyMatcher:
    """Fuzzy matching capabilities for agricultural terms."""
    
    @staticmethod
    def calculate_similarity_score(user_input: str, db_value: str, synonyms: str = "") -> float:
        """Calculate comprehensive similarity score between user input and database value."""
        
        user_norm = EnhancedTextNormalizer.normalize_for_search(user_input)
        db_norm = EnhancedTextNormalizer.normalize_for_search(db_value)
        
        if not user_norm or not db_norm:
            return 0.0
        
        # Direct string similarity using longest common subsequence
        direct_sim = FuzzyMatcher._lcs_similarity(user_norm, db_norm)
        
        # Token-based similarity for compound terms
        token_sim = EnhancedTextNormalizer.calculate_token_similarity(user_input, db_value)
        
        # Synonym similarity if available
        synonym_sim = 0.0
        if synonyms:
            synonym_list = EnhancedTextNormalizer.normalize_synonym_list(synonyms)
            for synonym in synonym_list:
                current_sim = max(
                    FuzzyMatcher._lcs_similarity(user_norm, synonym),
                    EnhancedTextNormalizer.calculate_token_similarity(user_input, synonym)
                )
                synonym_sim = max(synonym_sim, current_sim)
        
        # Weighted combination - prioritize synonym matches
        scores = [direct_sim, token_sim * 0.8, synonym_sim * 0.9]
        return max(scores)
    
    @staticmethod
    def _lcs_similarity(s1: str, s2: str) -> float:
        """Calculate similarity based on longest common subsequence."""
        if not s1 or not s2:
            return 0.0
        
        # Simple LCS implementation
        m, n = len(s1), len(s2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if s1[i-1] == s2[j-1]:
                    dp[i][j] = dp[i-1][j-1] + 1
                else:
                    dp[i][j] = max(dp[i-1][j], dp[i][j-1])
        
        lcs_length = dp[m][n]
        return lcs_length / max(m, n)
    
    @staticmethod
    def find_best_matches(
        user_input: str, 
        candidates: List[Tuple[str, str]], 
        min_similarity: float = 0.6,
        max_results: int = 5
    ) -> List[Tuple[str, str, float]]:
        """
        Find best matches from a list of candidates.
        
        Args:
            user_input: User's search term
            candidates: List of (name, synonyms) tuples
            min_similarity: Minimum similarity threshold
            max_results: Maximum number of results to return
            
        Returns:
            List of (name, synonyms, similarity_score) tuples, sorted by score
        """
        scored_matches = []
        
        for name, synonyms in candidates:
            score = FuzzyMatcher.calculate_similarity_score(user_input, name, synonyms)
            
            if score >= min_similarity:
                scored_matches.append((name, synonyms, score))
        
        # Sort by score descending and limit results
        scored_matches.sort(key=lambda x: x[2], reverse=True)
        return scored_matches[:max_results]


class SemanticMatcher:
    """Handle semantic/conceptual matching beyond exact strings."""
    
    CROP_CATEGORIES = {
        "cereals": ["blé", "orge", "avoine", "seigle", "triticale", "riz"],
        "grains": ["blé", "orge", "avoine", "maïs", "riz", "millet"],
        "stone fruits": ["pêcher", "abricotier", "prunier", "cerisier"],
        "citrus": ["citronnier", "oranger", "mandarinier", "pamplemousse"],
        "root vegetables": ["pomme de terre", "carotte", "betterave", "radis", "navet"],
        "leafy greens": ["salade", "épinard", "chou", "laitue"],
        "vine crops": ["vigne", "concombre", "melon", "courgette"],
        "legumes": ["haricot", "pois", "lentille", "fève"],
        "nuts": ["noyer", "noisetier", "amandier"],
        "berries": ["fraisier", "framboisier", "groseillier"],
        "herbs": ["basilic", "persil", "thym", "romarin"],
        "flowers": ["rose", "tulipe", "œillet"],
    }
    
    PEST_CATEGORIES = {
        "sucking insects": ["pucerons", "aleurodes", "thrips", "cochenilles"],
        "chewing insects": ["chenilles", "coléoptères", "sauterelles", "criquets"],
        "boring insects": ["foreurs", "scolytes", "pyrale"],
        "fungal diseases": ["mildiou", "oïdium", "rouille", "anthracnose", "fusariose"],
        "bacterial diseases": ["bactériose", "feu bactérien", "galle"],
        "viral diseases": ["virus", "mosaïque", "jaunisse"],
        "soilborne diseases": ["fusariose", "rhizoctonia", "pythium", "verticilliose"],
        "leaf diseases": ["tavelure", "septoriose", "tache foliaire"],
        "root pests": ["nématodes", "ver blanc", "taupin"],
        "flying pests": ["mouches", "mineuses", "pucerons ailés"],
        "crawling pests": ["limaces", "escargots", "chenilles"],
        "mites": ["acariens", "tétranyques", "ériophyes"],
    }
    
    @classmethod
    def find_semantic_matches(cls, user_input: str, search_type: str) -> List[str]:
        """Find semantic matches for conceptual terms."""
        user_lower = user_input.lower()
        matches = []
        
        categories = cls.CROP_CATEGORIES if search_type == "crop" else cls.PEST_CATEGORIES
        
        for category, items in categories.items():
            # Check if user input matches category name or its words
            category_words = category.split()
            user_words = user_lower.split()
            
            # Full category match
            if category in user_lower:
                matches.extend(items)
            # Partial word matches
            elif any(word in user_lower for word in category_words):
                matches.extend(items)
            # Individual word matches
            elif any(user_word in category for user_word in user_words):
                matches.extend(items)
        
        return list(set(matches))  # Remove duplicates
    
    @classmethod
    def expand_crop_query(cls, crop_name: str) -> List[str]:
        """Expand crop query with related terms."""
        expanded = [crop_name]
        
        # Add semantic matches
        semantic_matches = cls.find_semantic_matches(crop_name, "crop")
        expanded.extend(semantic_matches)
        
        # Add common variations
        crop_lower = crop_name.lower()
        if "potato" in crop_lower or "pomme de terre" in crop_lower:
            expanded.extend(["pomme de terre", "patate", "potato"])
        elif "wheat" in crop_lower or "blé" in crop_lower:
            expanded.extend(["blé", "wheat", "froment"])
        elif "corn" in crop_lower or "maïs" in crop_lower:
            expanded.extend(["maïs", "corn", "maize"])
        
        return list(set(expanded))
    
    @classmethod
    def expand_pest_query(cls, pest_name: str) -> List[str]:
        """Expand pest query with related terms."""
        expanded = [pest_name]
        
        # Add semantic matches
        semantic_matches = cls.find_semantic_matches(pest_name, "pest")
        expanded.extend(semantic_matches)
        
        return list(set(expanded))