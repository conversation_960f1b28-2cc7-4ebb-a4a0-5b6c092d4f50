"""
Knowledge Base ORM models for internal document management and RAG system.
Supports documents with different access levels (public, private, enterprise).
Includes comprehensive chunking, embedding, and metadata tracking.
"""

from datetime import datetime
from enum import Enum
from uuid import uuid4

from pgvector.sqlalchemy import Vector
from sqlalchemy import (
    Column, String, DateTime, Integer, Text, JSON,
    ForeignKey, Index, UniqueConstraint, CheckConstraint,
    BigInteger, Float
)
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.orm import relationship

from models import Base


class AccessLevel(str, Enum):
    """Document access levels"""
    PUBLIC = "public"  # Available to all users
    PRIVATE = "private"  # Available to specific user only
    ENTERPRISE = "enterprise"  # Available to enterprise users only


class DocumentType(str, Enum):
    """Supported document types"""
    PDF = "pdf"
    DOCX = "docx"
    TXT = "txt"
    IMAGE = "image"
    CSV = "csv"
    JSON = "json"
    HTML = "html"
    OTHER = "other"


class ProcessingStatus(str, Enum):
    """Document processing status"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRY = "retry"


class Document(Base):
    """
    Core document model storing file metadata and processing information.
    Each document can have multiple chunks with embeddings.
    """
    __tablename__ = "documents"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)

    # File information
    filename = Column(String(500), nullable=False, unique=True)  # MinIO object name (primary identifier)
    display_name = Column(String(255), nullable=True, index=True)  # Optional display name for UI
    file_size = Column(BigInteger, nullable=False)
    content_type = Column(String(100), nullable=False)
    document_type = Column(String(20), nullable=False, index=True)

    # Access control - from payload, not managed here
    access_level = Column(String(20), nullable=False, default=AccessLevel.PRIVATE, index=True)
    user_id = Column(String(100), nullable=True, index=True)  # User ID from external system
    enterprise_id = Column(String(100), nullable=True, index=True)  # Enterprise ID from external system

    # Processing status
    processing_status = Column(String(20), nullable=False, default=ProcessingStatus.PENDING, index=True)
    retry_count = Column(Integer, default=0)
    error_message = Column(Text, nullable=True)

    # Content metadata
    title = Column(String(500), nullable=True, index=True)
    language = Column(String(10), nullable=True, index=True)
    total_pages = Column(Integer, nullable=True)
    total_chunks = Column(Integer, default=0)

    # Agricultural domain metadata
    crop_categories = Column(ARRAY(String), nullable=True, index=True)
    topics = Column(ARRAY(String), nullable=True, index=True)
    geographic_scope = Column(ARRAY(String), nullable=True)
    seasonal_relevance = Column(ARRAY(String), nullable=True)

    # System metadata
    extra_metadata = Column(JSON, nullable=True)  # Additional flexible metadata
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    chunks = relationship("DocumentChunk", back_populates="document", cascade="all, delete-orphan")

    # Indexes for common queries
    __table_args__ = (
        Index("idx_doc_access_status", "access_level", "processing_status"),
        Index("idx_doc_user_enterprise", "user_id", "enterprise_id"),
        Index("idx_doc_type_created", "document_type", "created_at"),
        Index("idx_doc_crops_topics", "crop_categories", "topics"),
        CheckConstraint("retry_count >= 0", name="chk_retry_count_positive"),
        CheckConstraint("file_size > 0", name="chk_file_size_positive"),
    )


class DocumentChunk(Base):
    """
    Document chunks with embeddings for RAG retrieval.
    Each chunk belongs to a document and contains processed text content.
    """
    __tablename__ = "document_chunks"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)

    # Foreign key to document
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id", ondelete="CASCADE"),
                         nullable=False, index=True)

    # Chunk identification
    chunk_index = Column(Integer, nullable=False)  # 0-based index within document
    chunk_hash = Column(String(64), nullable=False, index=True)  # Content hash for deduplication

    # Content information
    content = Column(Text, nullable=False)
    content_preview = Column(String(200), nullable=False)  # First 200 chars for preview
    content_length = Column(Integer, nullable=False)

    # Source location in document
    page_number = Column(Integer, nullable=True, index=True)  # Page number if applicable
    start_position = Column(Integer, nullable=True)  # Character start position
    end_position = Column(Integer, nullable=True)  # Character end position

    # Contextual information (Anthropic contextual retrieval)
    context_summary = Column(Text, nullable=True)  # AI-generated context for chunk
    contextual_content = Column(Text, nullable=True)  # Original content with prepended context for embedding

    # Embedding information
    embedding = Column(Vector(1536), nullable=True)  # OpenAI ada-002 default dimension
    embedding_model = Column(String(100), nullable=True)
    embedding_created_at = Column(DateTime, nullable=True)

    # Chunk metadata
    chunk_type = Column(String(50), nullable=True)  # header, paragraph, table, list, etc.
    importance_score = Column(Float, nullable=True)  # 0.0-1.0 importance ranking

    # Agricultural domain tags
    detected_crops = Column(ARRAY(String), nullable=True)
    detected_topics = Column(ARRAY(String), nullable=True)
    technical_terms = Column(ARRAY(String), nullable=True)

    # System metadata
    chunk_metadata = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    document = relationship("Document", back_populates="chunks")

    # Indexes for efficient retrieval
    __table_args__ = (
        Index("idx_chunk_doc_index", "document_id", "chunk_index"),
        Index("idx_chunk_hash", "chunk_hash"),
        Index("idx_chunk_page", "page_number"),
        Index("idx_chunk_crops", "detected_crops"),
        Index("idx_chunk_topics", "detected_topics"),
        # Note: Vector similarity search index should be created manually using pgvector
        # CREATE INDEX CONCURRENTLY idx_chunk_embedding ON document_chunks USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
        UniqueConstraint("document_id", "chunk_index", name="uq_doc_chunk_index"),
        CheckConstraint("chunk_index >= 0", name="chk_chunk_index_positive"),
        CheckConstraint("content_length > 0", name="chk_content_length_positive"),
        CheckConstraint("importance_score IS NULL OR (importance_score >= 0.0 AND importance_score <= 1.0)",
                        name="chk_importance_score_range"),
    )


class SearchStats(Base):
    """
    Track search queries and performance for system optimization.
    Helps improve retrieval quality and identify knowledge gaps.
    """
    __tablename__ = "search_stats"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)

    # Query information
    query_text = Column(Text, nullable=False)
    query_hash = Column(String(64), nullable=False, index=True)  # For deduplication
    query_type = Column(String(50), nullable=True)  # diagnostic, procedural, regulatory, etc.

    # User context (from payload)
    user_id = Column(String(100), nullable=True, index=True)
    enterprise_id = Column(String(100), nullable=True, index=True)

    # Search parameters
    search_filters = Column(JSON, nullable=True)
    access_level_used = Column(String(20), nullable=True)

    # Results information
    total_results_found = Column(Integer, default=0)
    results_returned = Column(Integer, default=0)
    retrieval_time_ms = Column(Integer, nullable=True)

    # System metadata
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)

    # Indexes for analytics
    __table_args__ = (
        Index("idx_search_hash_created", "query_hash", "created_at"),
        Index("idx_search_user_created", "user_id", "created_at"),
        Index("idx_search_enterprise_created", "enterprise_id", "created_at"),
        Index("idx_search_type_created", "query_type", "created_at"),
        CheckConstraint("total_results_found >= 0", name="chk_total_results_positive"),
        CheckConstraint("results_returned >= 0", name="chk_results_returned_positive"),
        CheckConstraint("retrieval_time_ms IS NULL OR retrieval_time_ms > 0", name="chk_retrieval_time_positive"),
    )
