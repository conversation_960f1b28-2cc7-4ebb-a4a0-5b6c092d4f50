"""
Comprehensive E-Phy Agricultural Tools API Routes.
Provides access to all agricultural intelligence tools for AI agents and applications.
"""
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field

from models.dbm import DatabaseManager
from modules.agentic.tools.e_phy.alternative_products import find_alternative_products
from modules.agentic.tools.e_phy.application_guidelines import get_application_guidelines
from modules.agentic.tools.e_phy.crop_protection_advisor import find_crop_protection_solutions
from modules.agentic.tools.e_phy.integrated_management import create_integrated_management_plan
from modules.agentic.tools.e_phy.product_authorization import get_product_authorization_info
from modules.agentic.tools.e_phy.product_details import get_product_comprehensive_info
# Import all E-Phy tools
from modules.agentic.tools.e_phy.product_search import search_agricultural_products
from modules.agentic.tools.e_phy.regulatory_compliance import check_regulatory_compliance
from modules.agentic.tools.e_phy.substance_intelligence import analyze_active_substances

router = APIRouter(tags=["Agricultural Tools"], prefix="/api/agricultural-tools")


# Pydantic schemas for request validation
class ProductSearchRequest(BaseModel):
    query: str = Field(..., description="Product name, partial name, or description")
    function_category: Optional[List[str]] = Field(None, description="Filter by function categories")
    authorization_status: str = Field("authorized_only", description="Authorization status filter")
    product_type: str = Field("PPP", description="Product type filter")
    include_alternatives: bool = Field(True, description="Include alternative suggestions")
    limit: int = Field(20, description="Maximum number of results")
    sort_by: str = Field("relevance", description="Sort results by")


class AlternativeProductsRequest(BaseModel):
    reference_product_id: int = Field(..., description="ID of the reference product")
    crop_name: Optional[str] = Field(None, description="Specific crop context")
    target_pest: Optional[str] = Field(None, description="Specific pest/disease target")
    resistance_management: bool = Field(True, description="Include resistance management")
    same_active_substance: bool = Field(False, description="Include same active substance")
    organic_alternatives: bool = Field(False, description="Focus on organic alternatives")
    availability_status: str = Field("available", description="Availability status filter")
    limit: int = Field(15, description="Maximum number of alternatives")


class ApplicationGuidelinesRequest(BaseModel):
    product_id: int = Field(..., description="Product ID")
    crop_name: str = Field(..., description="Target crop name")
    target_pest: Optional[str] = Field(None, description="Specific pest/disease")
    application_stage: Optional[str] = Field(None, description="Growth stage context")
    field_size: Optional[float] = Field(None, description="Field size in hectares")
    equipment_type: Optional[str] = Field(None, description="Application equipment")
    weather_conditions: Optional[Dict[str, Any]] = Field(None, description="Weather context")


class CropProtectionRequest(BaseModel):
    crop_name: str = Field(..., description="Target crop name")
    target_pest: Optional[str] = Field(None, description="Specific pest/disease")
    target_type: Optional[str] = Field(None, description="Type of target")
    urgency_level: str = Field("normal", description="Urgency level")
    organic_only: bool = Field(False, description="Organic products only")
    include_similar_crops: bool = Field(True, description="Include similar crop solutions")
    limit: int = Field(15, description="Maximum number of solutions")
    min_similarity: float = Field(0.6, description="Minimum similarity threshold")


class IntegratedManagementRequest(BaseModel):
    crop_name: str = Field(..., description="Target crop")
    target_pests: List[str] = Field(..., description="List of target pests/diseases")
    season_length: int = Field(120, description="Season length in days")
    resistance_risk: str = Field("medium", description="Resistance risk level")
    organic_preference: bool = Field(False, description="Prefer organic solutions")
    budget_constraint: Optional[str] = Field(None, description="Budget limitations")
    application_windows: Optional[List[Dict[str, Any]]] = Field(None, description="Timing constraints")
    field_size_ha: Optional[float] = Field(None, description="Field size in hectares")


class ProductDetailsRequest(BaseModel):
    product_identifier: str = Field(..., description="Product ID, name, or registration number")
    include_composition: bool = Field(True, description="Include chemical composition")
    include_hazards: bool = Field(True, description="Include safety hazard information")
    include_conditions: bool = Field(True, description="Include usage conditions")


class RegulatoryComplianceRequest(BaseModel):
    product_ids: List[int] = Field(..., description="List of product IDs to check")
    intended_use: Dict[str, Any] = Field(..., description="Use case details")
    region: str = Field("france", description="Regulatory region")
    user_type: str = Field("professional", description="User type")
    check_date: Optional[str] = Field(None, description="Specific date for compliance")
    include_warnings: bool = Field(True, description="Include warnings")
    include_alternatives: bool = Field(True, description="Include alternatives")
    strict_matching: bool = Field(False, description="Use strict matching")
    ai_context: Optional[Dict[str, Any]] = Field(None, description="AI agent context")


class SubstanceIntelligenceRequest(BaseModel):
    substance_query: str = Field(..., description="Substance name, CAS number, or partial match")
    include_variants: bool = Field(True, description="Include chemical variants")
    focus_on_authorized: bool = Field(True, description="Focus on authorized products")
    include_alternatives: bool = Field(True, description="Include alternative substances")
    risk_assessment_level: str = Field("standard", description="Risk assessment level")
    intended_use_context: Optional[Dict[str, str]] = Field(None, description="Crop/target context")
    include_withdrawn_analysis: bool = Field(False, description="Include withdrawn products")


class ProductAuthorizationRequest(BaseModel):
    product_query: str = Field(..., description="Product name or registration number")
    crop_query: Optional[str] = Field(None, description="Specific crop name")
    include_usage_details: bool = Field(True, description="Include usage details")
    limit_crops: int = Field(50, description="Maximum number of crops")
    only_current_authorizations: bool = Field(True, description="Current authorizations only")


# API Routes

@router.post("/product-search")
async def product_search_route(request: ProductSearchRequest) -> Dict[str, Any]:
    """
    Search for agricultural products with intelligent ranking and alternatives.
    
    This endpoint provides comprehensive product discovery with:
    - Fuzzy matching and multilingual support
    - Quality scoring and data completeness indicators
    - Alternative product suggestions
    - Authorization status filtering
    """
    db_manager = DatabaseManager()

    try:
        async with db_manager.session_scope() as session:
            result = await search_agricultural_products(
                session=session,
                query=request.query,
                function_category=request.function_category,
                authorization_status=request.authorization_status,
                product_type=request.product_type,
                include_alternatives=request.include_alternatives,
                limit=request.limit,
                sort_by=request.sort_by
            )

            # Convert dataclass to dict for JSON serialization
            return {
                "results": [
                    {
                        "product_id": r.product_id,
                        "product_name": r.product_name,
                        "registration_number": r.registration_number,
                        "function_category": r.function_category,
                        "authorization_status": r.authorization_status,
                        "is_currently_authorized": r.is_currently_authorized,
                        "holder": r.holder,
                        "data_quality_score": r.data_quality_score,
                        "alternative_names": r.alternative_names,
                        "key_substances": r.key_substances,
                        "usage_summary": r.usage_summary,
                        "search_confidence": r.search_confidence,
                        "match_reasons": r.match_reasons
                    } for r in result.results
                ],
                "total_found": result.total_found,
                "search_confidence": result.search_confidence,
                "query_suggestions": result.query_suggestions,
                "filters_applied": result.filters_applied,
                "success": True
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Product search failed: {str(e)}")


@router.post("/alternative-products")
async def alternative_products_route(request: AlternativeProductsRequest) -> Dict[str, Any]:
    """
    Find alternative products for resistance management and substitution.
    
    Provides AI-optimized alternative product discovery with:
    - Resistance management analysis
    - Data quality indicators
    - Availability status checking
    - Efficacy and safety comparisons
    """
    db_manager = DatabaseManager()

    try:
        async with db_manager.session_scope() as session:
            result = await find_alternative_products(
                session=session,
                reference_product_id=request.reference_product_id,
                crop_name=request.crop_name,
                target_pest=request.target_pest,
                resistance_management=request.resistance_management,
                same_active_substance=request.same_active_substance,
                organic_alternatives=request.organic_alternatives,
                availability_status=request.availability_status,
                limit=request.limit
            )

            # Convert response to dict
            return {
                "reference_product": {
                    "product_id": result.reference_product.product_id,
                    "product_name": result.reference_product.product_name,
                    "active_substances": result.reference_product.active_substances,
                    "function_category": result.reference_product.function_category
                },
                "alternatives": [
                    {
                        "product_id": alt.product_id,
                        "product_name": alt.product_name,
                        "registration_number": alt.registration_number,
                        "active_substances": alt.active_substances,
                        "function_category": alt.function_category,
                        "similarity_score": alt.similarity_score,
                        "resistance_management_value": alt.resistance_management_value,
                        "relative_efficacy": alt.relative_efficacy,
                        "application_differences": alt.application_differences,
                        "availability_status": alt.availability_status,
                        "user_restrictions": alt.user_restrictions,
                        "cost_implications": alt.cost_implications,
                        "data_completeness_score": alt.data_completeness_score,
                        "confidence_level": alt.confidence_level,
                        "ai_recommendation_priority": alt.ai_recommendation_priority,
                        "substance_mode_of_action": alt.substance_mode_of_action,
                        "environmental_risk_level": alt.environmental_risk_level,
                        "usage_complexity": alt.usage_complexity,
                        "data_quality_indicators": alt.data_quality_indicators,
                        "fallback_reasoning": alt.fallback_reasoning
                    } for alt in result.alternatives
                ],
                "resistance_strategy": {
                    "strategy_type": result.resistance_strategy.strategy_type,
                    "recommended_sequence": result.resistance_strategy.recommended_sequence,
                    "timing_suggestions": result.resistance_strategy.timing_suggestions,
                    "rotation_period": result.resistance_strategy.rotation_period
                },
                "search_metadata": result.search_metadata,
                "success": True
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Alternative products search failed: {str(e)}")


@router.post("/application-guidelines")
async def application_guidelines_route(request: ApplicationGuidelinesRequest) -> Dict[str, Any]:
    """
    Get comprehensive application guidelines for product-crop combinations.
    
    Provides detailed application instructions including:
    - Dosage calculations and recommendations
    - Application timing and restrictions
    - Safety requirements and equipment needs
    - Weather and environmental considerations
    """
    db_manager = DatabaseManager()

    try:
        async with db_manager.session_scope() as session:
            result = await get_application_guidelines(
                session=session,
                product_id=request.product_id,
                crop_name=request.crop_name,
                target_pest=request.target_pest,
                application_stage=request.application_stage,
                field_size=request.field_size,
                equipment_type=request.equipment_type,
                weather_conditions=request.weather_conditions
            )

            # Convert result to dict (implementation depends on the actual response structure)
            return {"result": result, "success": True}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Application guidelines failed: {str(e)}")


@router.post("/crop-protection")
async def crop_protection_route(request: CropProtectionRequest) -> Dict[str, Any]:
    """
    Find crop protection solutions with enhanced fuzzy matching.
    
    Provides intelligent crop protection recommendations with:
    - Fuzzy crop and pest matching
    - Semantic understanding of agricultural terms
    - Urgency-based prioritization
    - Organic and sustainable options
    """
    db_manager = DatabaseManager()

    try:
        async with db_manager.session_scope() as session:
            result = await find_crop_protection_solutions(
                session=session,
                crop_name=request.crop_name,
                target_pest=request.target_pest,
                target_type=request.target_type,
                urgency_level=request.urgency_level,
                organic_only=request.organic_only,
                include_similar_crops=request.include_similar_crops,
                limit=request.limit,
                min_similarity=request.min_similarity
            )

            # Convert response to dict (this was already implemented in the original route)
            response_dict = {
                "crop_match": {
                    "matched_crop": result.crop_match.matched_crop,
                    "crop_id": result.crop_match.crop_id,
                    "crop_category": result.crop_match.crop_category,
                    "search_confidence": result.crop_match.search_confidence,
                    "original_query": result.crop_match.original_query,
                    "synonyms_used": result.crop_match.synonyms_used
                } if result.crop_match else None,

                "target_match": {
                    "matched_target": result.target_match.matched_target,
                    "target_id": result.target_match.target_id,
                    "target_type": result.target_match.target_type,
                    "search_confidence": result.target_match.search_confidence,
                    "original_query": result.target_match.original_query,
                    "translated_terms": result.target_match.translated_terms
                } if result.target_match else None,

                "recommended_products": [
                    {
                        "product_id": product.product_id,
                        "product_name": product.product_name,
                        "registration_number": product.registration_number,
                        "active_substances": product.active_substances,
                        "function_category": product.function_category,
                        "application_dose_min": product.application_dose_min,
                        "application_dose_max": product.application_dose_max,
                        "dose_unit": product.dose_unit,
                        "harvest_interval": product.harvest_interval,
                        "max_applications": product.max_applications,
                        "effectiveness_score": product.effectiveness_score,
                        "safety_level": product.safety_level,
                        "data_quality": product.data_quality,
                        "usage_notes": product.usage_notes,
                        "special_conditions": product.special_conditions
                    } for product in result.recommended_products
                ],

                "alternative_approaches": result.alternative_approaches,
                "preventive_measures": result.preventive_measures,
                "resistance_management": result.resistance_management,
                "safety_warnings": result.safety_warnings,
                "data_limitations": result.data_limitations,
                "search_strategy_used": result.search_strategy_used
            }

            return {"result": response_dict, "success": True}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Crop protection search failed: {str(e)}")


@router.post("/integrated-management")
async def integrated_management_route(request: IntegratedManagementRequest) -> Dict[str, Any]:
    """
    Create integrated pest management (IPM) plans.
    
    Provides comprehensive IPM strategy including:
    - Multi-product treatment sequences
    - Resistance management protocols
    - Tank-mix compatibility analysis
    - Seasonal application planning
    """
    db_manager = DatabaseManager()

    try:
        async with db_manager.session_scope() as session:
            result = await create_integrated_management_plan(
                session=session,
                crop_name=request.crop_name,
                target_pests=request.target_pests,
                season_length=request.season_length,
                resistance_risk=request.resistance_risk,
                organic_preference=request.organic_preference,
                budget_constraint=request.budget_constraint,
                application_windows=request.application_windows,
                field_size_ha=request.field_size_ha
            )

            return {"result": result, "success": True}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Integrated management planning failed: {str(e)}")


@router.post("/product-details")
async def product_details_route(request: ProductDetailsRequest) -> Dict[str, Any]:
    """
    Get comprehensive product information and details.
    
    Provides complete product information including:
    - Chemical composition and active substances
    - Safety hazard information and classifications
    - Usage conditions and regulatory restrictions
    - Authorization status and holder information
    """
    db_manager = DatabaseManager()

    try:
        async with db_manager.session_scope() as session:
            result = await get_product_comprehensive_info(
                session=session,
                product_identifier=request.product_identifier,
                include_composition=request.include_composition,
                include_hazards=request.include_hazards,
                include_conditions=request.include_conditions
            )

            return {"result": result, "success": True}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Product details retrieval failed: {str(e)}")


@router.post("/regulatory-compliance")
async def regulatory_compliance_route(request: RegulatoryComplianceRequest) -> Dict[str, Any]:
    """
    Check regulatory compliance for product usage.
    
    Provides comprehensive compliance verification including:
    - Authorization status validation
    - Usage restriction analysis
    - Legal compliance assessment
    - Alternative product suggestions
    """
    db_manager = DatabaseManager()

    try:
        async with db_manager.session_scope() as session:
            result = await check_regulatory_compliance(
                session=session,
                product_ids=request.product_ids,
                intended_use=request.intended_use,
                region=request.region,
                user_type=request.user_type,
                check_date=request.check_date,
                include_warnings=request.include_warnings,
                include_alternatives=request.include_alternatives,
                strict_matching=request.strict_matching,
                ai_context=request.ai_context
            )

            return {"result": result, "success": True}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Regulatory compliance check failed: {str(e)}")


@router.post("/substance-intelligence")
async def substance_intelligence_route(request: SubstanceIntelligenceRequest) -> Dict[str, Any]:
    """
    Analyze active substances with comprehensive intelligence.
    
    Provides detailed substance analysis including:
    - Chemical and biological agent information
    - Resistance risk assessment and management
    - Safety and environmental profiles
    - Market availability and alternatives
    - AI-optimized decision support
    """
    db_manager = DatabaseManager()

    try:
        async with db_manager.session_scope() as session:
            result = await analyze_active_substances(
                session=session,
                substance_query=request.substance_query,
                include_variants=request.include_variants,
                focus_on_authorized=request.focus_on_authorized,
                include_alternatives=request.include_alternatives,
                risk_assessment_level=request.risk_assessment_level,
                intended_use_context=request.intended_use_context,
                include_withdrawn_analysis=request.include_withdrawn_analysis
            )

            # Convert response to dict for JSON serialization
            return {
                "substance_profile": result.substance_profile,
                "current_availability": result.current_availability,
                "authorized_products": [
                    {
                        "product_id": p.product_id,
                        "product_name": p.product_name,
                        "concentration": p.concentration,
                        "concentration_unit": p.concentration_unit,
                        "formulation_type": p.formulation_type,
                        "authorization_status": p.authorization_status,
                        "is_primary_substance": p.is_primary_substance,
                        "market_share_estimate": p.market_share_estimate
                    } for p in result.authorized_products
                ],
                "withdrawn_products": [
                    {
                        "product_id": p.product_id,
                        "product_name": p.product_name,
                        "authorization_status": p.authorization_status
                    } for p in result.withdrawn_products
                ],
                "use_patterns": [
                    {
                        "crop_name": p.crop_name,
                        "target_type": p.target_type,
                        "usage_frequency": p.usage_frequency,
                        "seasonal_pattern": p.seasonal_pattern,
                        "typical_dose_range": p.typical_dose_range,
                        "application_timing": p.application_timing,
                        "effectiveness_rating": p.effectiveness_rating
                    } for p in result.use_patterns
                ],
                "application_guidance": result.application_guidance,
                "safety_profile": {
                    "toxicity_classification": result.safety_profile.toxicity_classification,
                    "environmental_fate": result.safety_profile.environmental_fate,
                    "exposure_pathways": result.safety_profile.exposure_pathways,
                    "protective_measures": result.safety_profile.protective_measures,
                    "restricted_uses": result.safety_profile.restricted_uses,
                    "withdrawal_considerations": result.safety_profile.withdrawal_considerations
                },
                "regulatory_status": {
                    "eu_approval_status": result.regulatory_status.eu_approval_status,
                    "approval_expiry_date": result.regulatory_status.approval_expiry_date,
                    "review_status": result.regulatory_status.review_status,
                    "restrictions_summary": result.regulatory_status.restrictions_summary,
                    "regional_variations": result.regulatory_status.regional_variations,
                    "pending_changes": result.regulatory_status.pending_changes,
                    "compliance_requirements": result.regulatory_status.compliance_requirements
                },
                "compliance_alerts": result.compliance_alerts,
                "chemical_intelligence": {
                    "mode_of_action_group": result.chemical_intelligence.mode_of_action_group,
                    "chemical_family": result.chemical_intelligence.chemical_family,
                    "molecular_target": result.chemical_intelligence.molecular_target,
                    "selectivity_profile": result.chemical_intelligence.selectivity_profile,
                    "persistence_class": result.chemical_intelligence.persistence_class,
                    "bioaccumulation_potential": result.chemical_intelligence.bioaccumulation_potential,
                    "degradation_pathways": result.chemical_intelligence.degradation_pathways
                },
                "resistance_information": {
                    "resistance_risk_level": result.resistance_information.resistance_risk_level,
                    "documented_resistance": result.resistance_information.documented_resistance,
                    "resistance_mechanisms": result.resistance_information.resistance_mechanisms,
                    "cross_resistance_groups": result.resistance_information.cross_resistance_groups,
                    "management_strategies": result.resistance_information.management_strategies,
                    "monitoring_protocols": result.resistance_information.monitoring_protocols,
                    "alternative_substances": result.resistance_information.alternative_substances
                },
                "suitability_assessment": result.suitability_assessment,
                "recommendations": result.recommendations,
                "alternatives": result.alternatives,
                "variants": [
                    {
                        "variant_name": v.variant_name,
                        "cas_number": v.cas_number,
                        "chemical_type": v.chemical_type,
                        "formulation_types": v.formulation_types,
                        "concentration_range": v.concentration_range,
                        "stability_notes": v.stability_notes
                    } for v in result.variants
                ],
                "related_substances": result.related_substances,
                "data_quality": result.data_quality,
                "search_metadata": result.search_metadata,
                "analysis_timestamp": result.analysis_timestamp,
                "success": True
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Substance intelligence analysis failed: {str(e)}")


@router.post("/product-authorization")
async def product_authorization_route(request: ProductAuthorizationRequest) -> Dict[str, Any]:
    """
    Get product authorization information for crops.
    
    Provides detailed authorization status including:
    - Crop-specific authorization details
    - Usage patterns and restrictions
    - Authorization history and status
    - Data quality assessments
    """
    db_manager = DatabaseManager()

    try:
        async with db_manager.session_scope() as session:
            result = await get_product_authorization_info(
                session=session,
                product_query=request.product_query,
                crop_query=request.crop_query,
                include_usage_details=request.include_usage_details,
                limit_crops=request.limit_crops,
                only_current_authorizations=request.only_current_authorizations
            )

            # Convert response to dict (this was already implemented in the original route)
            response_dict = {
                "query_type": result.query_type,
                "product_found": result.product_found,
                "crop_found": result.crop_found,
                "product_id": result.product_id,
                "product_name": result.product_name,
                "registration_number": result.registration_number,
                "is_currently_authorized": result.is_currently_authorized,
                "authorization_status": result.authorization_status,
                "function_category": result.function_category,
                "crop_authorizations": [
                    {
                        "crop_id": auth.crop_id,
                        "crop_name": auth.crop_name,
                        "crop_category": auth.crop_category,
                        "is_authorized": auth.is_authorized,
                        "usage_count": auth.usage_count,
                        "authorized_targets": auth.authorized_targets,
                        "usage_details": [
                            {
                                "usage_id": usage.usage_id,
                                "target_name": usage.target_name,
                                "target_type": usage.target_type,
                                "min_dose": usage.min_dose,
                                "max_dose": usage.max_dose,
                                "dose_unit": usage.dose_unit,
                                "harvest_interval_days": usage.harvest_interval_days,
                                "max_applications": usage.max_applications,
                                "application_comments": usage.application_comments,
                                "data_completeness": usage.data_completeness
                            } for usage in auth.usage_details
                        ],
                        "data_quality_score": auth.data_quality_score
                    } for auth in result.crop_authorizations
                ],
                "total_authorized_crops": result.total_authorized_crops,
                "total_authorized_uses": result.total_authorized_uses,
                "primary_function": result.primary_function,
                "most_common_targets": result.most_common_targets,
                "crop_categories_covered": result.crop_categories_covered,
                "search_confidence": result.search_confidence,
                "overall_data_quality": result.overall_data_quality,
                "suggestions": result.suggestions,
                "warnings": result.warnings
            }

            return {"result": response_dict, "success": True}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Product authorization query failed: {str(e)}")


# Health check endpoint
@router.get("/health")
async def health_check():
    """Health check endpoint for the agricultural tools API."""
    return {"status": "healthy", "service": "Agricultural Tools API", "version": "1.0.0"}


# List all available tools
@router.get("/tools")
async def list_available_tools():
    """List all available agricultural tools and their descriptions."""
    return {
        "available_tools": [
            {
                "endpoint": "/product-search",
                "name": "Product Search",
                "description": "Search for agricultural products with intelligent ranking and alternatives"
            },
            {
                "endpoint": "/alternative-products",
                "name": "Alternative Products",
                "description": "Find alternative products for resistance management and substitution"
            },
            {
                "endpoint": "/application-guidelines",
                "name": "Application Guidelines",
                "description": "Get comprehensive application guidelines for product-crop combinations"
            },
            {
                "endpoint": "/crop-protection",
                "name": "Crop Protection",
                "description": "Find crop protection solutions with enhanced fuzzy matching"
            },
            {
                "endpoint": "/integrated-management",
                "name": "Integrated Management",
                "description": "Create integrated pest management (IPM) plans"
            },
            {
                "endpoint": "/product-details",
                "name": "Product Details",
                "description": "Get comprehensive product information and details"
            },
            {
                "endpoint": "/regulatory-compliance",
                "name": "Regulatory Compliance",
                "description": "Check regulatory compliance for product usage"
            },
            {
                "endpoint": "/substance-intelligence",
                "name": "Substance Intelligence",
                "description": "Analyze active substances with comprehensive intelligence"
            },
            {
                "endpoint": "/product-authorization",
                "name": "Product Authorization",
                "description": "Get product authorization information for crops"
            }
        ],
        "total_tools": 9
    }
