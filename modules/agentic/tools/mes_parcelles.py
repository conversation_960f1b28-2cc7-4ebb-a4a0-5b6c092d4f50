"""
MesParcelles Farm Data Tools for Agricultural AI Agent

This module provides comprehensive tools for analyzing farm data from MesParcelles,
enabling the AI agent to efficiently access and analyze farm information for personalized
agricultural recommendations.
"""

from typing import Optional, Dict, Any, Literal

from modules.agentic.tools.personal_data import fetch_farm_data
from utils.logger import logger

# Type definitions for better LLM understanding
DetailLevel = Literal["summary", "detailed", "parcels_only"]
ParcelScope = Literal["all", "single", "by_crop", "by_location"]
SortOption = Literal["area", "activity", "crop", "location", "name"]
TimeScope = Literal["recent", "this_season", "date_range", "specific_date"]
ActivityFocus = Literal["all", "fertilization", "planting", "seeding", "treatment", "harvest"]
GroupingOption = Literal["chronological", "by_crop", "by_parcel", "by_type", "by_location"]
InsightType = Literal["patterns", "gaps", "efficiency", "next_steps", "comparison", "workload"]


class MesParcellesDataTool:
    """
    Comprehensive toolkit for Agricultural AI agent to efficiently access
    farm data from MesParcelles while preserving all valuable identifiers and data.
    """

    def __init__(self, farm_data: Dict[str, Any]):
        """Initialize with the raw farm data from MesParcelles."""
        self.farm_data = farm_data
        self._process_data()

    def _process_data(self):
        """Process and index the raw data for efficient retrieval while preserving all IDs and valuable data."""
        # Extract core data
        self.date_created = self.farm_data.get('date_created', '')
        self.farmer_id = self.farm_data.get('farmer_id')
        self.siret = self.farm_data.get('metadata', {}).get('parcels', [{}])[0].get('siret_exploitation',
                                                                                    '') if self.farm_data.get(
            'metadata', {}).get('parcels') else ''

        # Process parcels - preserve all fields including identifiers
        self.parcels = {}
        for parcel in self.farm_data.get('metadata', {}).get('parcels', []):
            uuid = parcel.get('uuid_parcelle', '')
            if uuid:
                # Preserve all parcel data including IDs and references
                self.parcels[uuid] = {
                    'uuid': uuid,
                    'name': parcel.get('nom', ''),
                    'area_ha': parcel.get('surface_mesuree_ha', 0),
                    'location_code': parcel.get('insee_commune', ''),
                    'millesime': parcel.get('millesime'),
                    'siret_exploitation': parcel.get('siret_exploitation', ''),
                    'uuid_parcelle_millesime_precedent': parcel.get('uuid_parcelle_millesime_precedent', ''),
                    'geometrie_vide': parcel.get('geometrie_vide', False),
                    'culture_intermediaire': parcel.get('culture_intermediaire'),
                    'current_crop': parcel.get('succession_cultures', [{}])[0].get('libelle', '')
                    if parcel.get('succession_cultures') else '',
                    'current_crop_id': parcel.get('succession_cultures', [{}])[0].get('id_culture')
                    if parcel.get('succession_cultures') else None,
                    'succession_cultures': parcel.get('succession_cultures', []),
                    'interventions': []
                }

        # Process interventions and link to parcels - preserve all data including IDs
        self.interventions = []
        for intervention in self.farm_data.get('metadata', {}).get('interventions', []):
            parcel_id = intervention.get('uuid_parcelle', '')

            # Create comprehensive intervention record with all fields
            intervention_data = {
                'uuid_intervention': intervention.get('uuid_intervention', ''),
                'type': intervention.get('type_intervention', {}).get('libelle', ''),
                'type_id': intervention.get('type_intervention', {}).get('id_type_intervention'),
                'crop': intervention.get('culture', {}).get('libelle', ''),
                'crop_id': intervention.get('culture', {}).get('id_culture'),
                'date_debut': intervention.get('date_debut', ''),
                'date_fin': intervention.get('date_fin', ''),
                'area_ha': intervention.get('surface_travaillee_ha', 0),
                'lot_number': intervention.get('numero_lot', ''),
                'parcel_id': parcel_id,
                'link_detail_intervention': intervention.get('link_detail_intervention', ''),
                'link_parcelle': intervention.get('link_parcelle', '')
            }

            self.interventions.append(intervention_data)

            # Add to parcel's interventions
            if parcel_id in self.parcels:
                parcel_name = self.parcels[parcel_id]['name']
                intervention_data['parcel_name'] = parcel_name
                self.parcels[parcel_id]['interventions'].append(intervention_data)

        # Sort interventions by date (newest first)
        self.interventions.sort(key=lambda x: x['date_debut'], reverse=True)

        # Index parcels by crop type for efficient lookup
        self.parcels_by_crop = {}
        self.parcels_by_crop_id = {}

        for parcel_id, parcel in self.parcels.items():
            crop = parcel['current_crop']
            crop_id = parcel['current_crop_id']

            if crop:
                if crop not in self.parcels_by_crop:
                    self.parcels_by_crop[crop] = []
                self.parcels_by_crop[crop].append({
                    'uuid': parcel_id,
                    'name': parcel['name'],
                    'area_ha': parcel['area_ha'],
                    'location_code': parcel['location_code'],
                    'millesime': parcel['millesime']
                })

            if crop_id:
                if crop_id not in self.parcels_by_crop_id:
                    self.parcels_by_crop_id[crop_id] = []
                self.parcels_by_crop_id[crop_id].append({
                    'uuid': parcel_id,
                    'name': parcel['name'],
                    'area_ha': parcel['area_ha'],
                    'location_code': parcel['location_code'],
                    'millesime': parcel['millesime']
                })

        # Index interventions by type for efficient lookup
        self.interventions_by_type = {}
        self.interventions_by_type_id = {}

        for intervention in self.interventions:
            int_type = intervention['type']
            int_type_id = intervention['type_id']

            if int_type:
                if int_type not in self.interventions_by_type:
                    self.interventions_by_type[int_type] = []
                self.interventions_by_type[int_type].append(intervention)

            if int_type_id:
                if int_type_id not in self.interventions_by_type_id:
                    self.interventions_by_type_id[int_type_id] = []
                self.interventions_by_type_id[int_type_id].append(intervention)

        # Calculate total farm area
        self.total_area = sum(parcel['area_ha'] for parcel in self.parcels.values())

        # Index parcels by location code
        self.parcels_by_location = {}
        for parcel_id, parcel in self.parcels.items():
            location = parcel['location_code']
            if location:
                if location not in self.parcels_by_location:
                    self.parcels_by_location[location] = []
                self.parcels_by_location[location].append({
                    'uuid': parcel_id,
                    'name': parcel['name'],
                    'area_ha': parcel['area_ha'],
                    'current_crop': parcel['current_crop'],
                    'current_crop_id': parcel['current_crop_id']
                })

    # ---- FARM OVERVIEW FUNCTIONS ----

    def get_farm_summary(self) -> str:
        """
        Get comprehensive farm overview with key statistics and crop distribution.
        
        Returns detailed farm summary including total area, parcel count, crop distribution
        with percentages, and intervention type summary. Essential for understanding the
        overall farm structure and recent activity patterns.
        
        Returns:
            str: Comprehensive farm overview with all key metrics and identifiers
        """
        crop_distribution = {}
        for parcel in self.parcels.values():
            crop = parcel['current_crop']
            crop_id = parcel['current_crop_id']
            if crop:
                if crop not in crop_distribution:
                    crop_distribution[crop] = {
                        'area_ha': 0,
                        'parcel_count': 0,
                        'crop_id': crop_id
                    }
                crop_distribution[crop]['area_ha'] += parcel['area_ha']
                crop_distribution[crop]['parcel_count'] += 1

        # Format crop distribution
        crops_formatted = []
        for crop, data in crop_distribution.items():
            percentage = (data['area_ha'] / self.total_area) * 100 if self.total_area else 0
            crops_formatted.append(
                f"{crop} (ID: {data['crop_id']}): {data['area_ha']:.2f} ha ({percentage:.1f}%), "
                f"{data['parcel_count']} parcels"
            )

        summary = [
            f"FARM SUMMARY (as of {self.date_created})",
            f"Farmer ID: {self.farmer_id}",
            f"SIRET: {self.siret}",
            f"Total parcels: {len(self.parcels)}",
            f"Total area: {self.total_area:.2f} hectares",
            "\nCROP DISTRIBUTION:",
        ]
        summary.extend(crops_formatted)

        # Add intervention type summary
        summary.append("\nINTERVENTION TYPES:")
        for int_type, interventions in self.interventions_by_type.items():
            int_type_id = interventions[0]['type_id'] if interventions else None
            summary.append(f"- {int_type} (ID: {int_type_id}): {len(interventions)} occurrences")

        # Add citation
        summary.append("\n---")
        summary.append("Source: MesParcelles (https://mesparcelles.fr/)")

        return "\n".join(summary)

    def get_recent_activities(self, limit: int = 5) -> str:
        """
        Get the most recent farm activities across all parcels with detailed information.
        
        Provides chronological overview of recent farm operations including dates, intervention
        types, crops involved, parcels affected, and areas worked. Essential for understanding
        current farm activity patterns and recent management decisions.
        
        Args:
            limit: Maximum number of recent activities to return (default: 5)
            
        Returns:
            str: Formatted list of recent farm activities with all relevant details
        """
        if not self.interventions:
            return "No recent activities recorded."

        activities = []
        for i, intervention in enumerate(self.interventions[:limit], 1):
            activities.append(
                f"{i}. {intervention['date_debut']}: {intervention['type']} (Type ID: {intervention['type_id']}) "
                f"of {intervention['crop']} (Crop ID: {intervention['crop_id']}) "
                f"on {intervention.get('parcel_name', 'Unknown')} (Parcel ID: {intervention['parcel_id']}) - "
                f"{intervention['area_ha']:.2f} ha - Intervention ID: {intervention['uuid_intervention']}"
            )

        result = "RECENT FARM ACTIVITIES:\n" + "\n".join(activities)
        result += "\n\n---\nSource: MesParcelles (https://mesparcelles.fr/)"
        return result

    # ---- PARCEL-SPECIFIC FUNCTIONS ----

    def get_parcel_details(self, parcel_identifier: str) -> str:
        """
        Get detailed information about a specific parcel by name or partial name match.
        
        Searches parcels by name (case-insensitive partial matching) and returns comprehensive
        parcel information including area, current crop, location, and complete intervention
        history. Use this when farmers ask about specific fields by name.
        
        Args:
            parcel_identifier: Parcel name or partial name to search for
            
        Returns:
            str: Detailed parcel information including intervention history
        """
        # Find parcel by name
        matching_parcels = []
        for parcel_id, parcel in self.parcels.items():
            if parcel_identifier.lower() in parcel['name'].lower():
                matching_parcels.append((parcel_id, parcel))

        if not matching_parcels:
            return f"Parcel '{parcel_identifier}' not found."

        # If multiple matches, take the first one but notify
        note = ""
        if len(matching_parcels) > 1:
            note = f"\nNote: Found {len(matching_parcels)} matching parcels. Showing the first one. Use parcel UUID for exact matching."

        parcel_id, parcel = matching_parcels[0]

        # Format interventions
        interventions_formatted = []
        sorted_interventions = sorted(parcel['interventions'], key=lambda x: x['date_debut'], reverse=True)
        for intervention in sorted_interventions:
            interventions_formatted.append(
                f"- {intervention['date_debut']} to {intervention['date_fin']}: "
                f"{intervention['type']} (Type ID: {intervention['type_id']}) of "
                f"{intervention['crop']} (Crop ID: {intervention['crop_id']}) - "
                f"{intervention['area_ha']:.2f} ha - Intervention ID: {intervention['uuid_intervention']}"
            )

        details = [
            f"PARCEL: {parcel['name']} (UUID: {parcel_id})",
            f"Area: {parcel['area_ha']:.2f} hectares",
            f"Current crop: {parcel['current_crop']} (Crop ID: {parcel['current_crop_id']})",
            f"Location code: {parcel['location_code']}",
            f"Millésime: {parcel['millesime']}",
            f"SIRET: {parcel['siret_exploitation']}",
            "\nINTERVENTION HISTORY:"
        ]

        if interventions_formatted:
            details.extend(interventions_formatted)
        else:
            details.append("No interventions recorded for this parcel.")

        if note:
            details.append(note)

        details.append("\n---")
        details.append("Source: MesParcelles (https://mesparcelles.fr/)")

        return "\n".join(details)

    def get_parcels_by_crop(self, crop_name: str) -> str:
        """
        Get all parcels growing a specific crop with area breakdown and statistics.
        
        Searches for parcels by crop name (partial matching supported) and returns detailed
        information about all matching parcels including individual areas and total area
        dedicated to that crop. Essential for crop-specific planning and management decisions.
        
        Args:
            crop_name: Name of the crop to search for (partial matching supported)
            
        Returns:
            str: List of all parcels growing the specified crop with area details
        """
        # Find matching crop with partial text match
        matching_crops = [crop for crop in self.parcels_by_crop.keys()
                          if crop_name.lower() in crop.lower()]

        if not matching_crops:
            return f"No parcels found with crop '{crop_name}'."

        results = []
        total_area = 0

        for crop in matching_crops:
            parcels = self.parcels_by_crop[crop]
            crop_area = sum(p['area_ha'] for p in parcels)
            total_area += crop_area

            # Get crop ID from the first parcel with this crop
            crop_id = None
            for parcel_id in self.parcels:
                if self.parcels[parcel_id]['current_crop'] == crop:
                    crop_id = self.parcels[parcel_id]['current_crop_id']
                    break

            results.append(f"CROP: {crop} (ID: {crop_id}, Total: {crop_area:.2f} ha)")
            for parcel in parcels:
                results.append(f"- {parcel['name']} (UUID: {parcel['uuid']}): {parcel['area_ha']:.2f} ha")

        results.insert(0, f"PARCELS GROWING {crop_name.upper()} (Total: {total_area:.2f} ha)")
        results.append("\n---")
        results.append("Source: MesParcelles (https://mesparcelles.fr/)")
        return "\n".join(results)

    # ---- INTERVENTION-SPECIFIC FUNCTIONS ----

    def get_intervention_history(self, intervention_type: str, limit: int = 5) -> str:
        """
        Get history of a specific intervention type across all parcels.
        
        Searches for interventions by type (partial matching supported) and returns chronological
        history showing where, when, and on what crops the interventions were performed.
        Essential for tracking specific farming operations like fertilization, planting, etc.
        
        Args:
            intervention_type: Type of intervention to search for (partial matching supported)
            limit: Maximum number of recent interventions to return (default: 5)
            
        Returns:
            str: Chronological history of the specified intervention type
        """
        matching_interventions = []
        for i in self.interventions:
            if intervention_type.lower() in i['type'].lower():
                matching_interventions.append(i)

        if not matching_interventions:
            return f"No '{intervention_type}' interventions found."

        results = [f"{intervention_type.upper()} HISTORY (most recent {min(limit, len(matching_interventions))})"]

        for i, intervention in enumerate(matching_interventions[:limit], 1):
            parcel_id = intervention['parcel_id']
            parcel_name = intervention.get('parcel_name', 'Unknown')

            results.append(
                f"{i}. {intervention['date_debut']}: {intervention['crop']} (Crop ID: {intervention['crop_id']}) "
                f"on {parcel_name} (UUID: {parcel_id}) - {intervention['area_ha']:.2f} ha - "
                f"Intervention ID: {intervention['uuid_intervention']}"
            )

        results.append("\n---")
        results.append("Source: MesParcelles (https://mesparcelles.fr/)")
        return "\n".join(results)

    def get_interventions_by_date_range(self, start_date: str, end_date: Optional[str] = None) -> str:
        """
        Get interventions within a specific date range with complete details.
        
        Filters interventions by date range and returns detailed information about all
        farm activities during that period. If end_date is not provided, returns
        interventions for the exact start_date only. Essential for time-based analysis.
        
        Args:
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format (optional, defaults to start_date)
            
        Returns:
            str: List of all interventions within the specified date range
        """
        if end_date is None:
            end_date = start_date

        matching_interventions = [i for i in self.interventions
                                  if start_date <= i['date_debut'] <= end_date]

        if not matching_interventions:
            date_range = f"on {start_date}" if start_date == end_date else f"between {start_date} and {end_date}"
            return f"No interventions found {date_range}."

        date_range = f"ON {start_date}" if start_date == end_date else f"FROM {start_date} TO {end_date}"
        results = [f"INTERVENTIONS {date_range}"]

        for i, intervention in enumerate(matching_interventions, 1):
            parcel_id = intervention['parcel_id']
            parcel_name = intervention.get('parcel_name', 'Unknown')

            results.append(
                f"{i}. {intervention['date_debut']}: {intervention['type']} (Type ID: {intervention['type_id']}) "
                f"of {intervention['crop']} (Crop ID: {intervention['crop_id']}) "
                f"on {parcel_name} (UUID: {parcel_id}) - {intervention['area_ha']:.2f} ha - "
                f"Intervention ID: {intervention['uuid_intervention']}"
            )

        results.append("\n---")
        results.append("Source: MesParcelles (https://mesparcelles.fr/)")
        return "\n".join(results)

    # ---- LOCATION-BASED FUNCTIONS ----

    def get_parcels_by_location(self, location_code: str) -> str:
        """
        Get all parcels in a specific location using INSEE commune code.
        
        Filters parcels by location code and returns detailed information about all
        parcels in that specific geographic area. Essential for location-specific
        planning, local regulations, and geographic analysis.
        
        Args:
            location_code: INSEE commune code to filter by
            
        Returns:
            str: List of all parcels in the specified location with crop details
        """
        if location_code not in self.parcels_by_location:
            return f"No parcels found in location '{location_code}'."

        parcels = self.parcels_by_location[location_code]
        total_area = sum(p['area_ha'] for p in parcels)

        results = [f"PARCELS IN LOCATION {location_code} (Total: {total_area:.2f} ha)"]

        for parcel in parcels:
            results.append(
                f"- {parcel['name']} (UUID: {parcel['uuid']}): {parcel['area_ha']:.2f} ha, "
                f"Crop: {parcel['current_crop']} (ID: {parcel['current_crop_id']})"
            )

        results.append("\n---")
        results.append("Source: MesParcelles (https://mesparcelles.fr/)")
        return "\n".join(results)

    # ---- HELPER METHODS FOR NEW TOOLS ----

    def _get_all_parcels_overview(self) -> str:
        """Get overview of all parcels with basic information."""
        if not self.parcels:
            return "No parcels found in your farm data."

        results = [f"ALL PARCELS OVERVIEW (Total: {len(self.parcels)} parcels, {self.total_area:.2f} ha)"]
        results.append("")

        # Sort parcels by area (largest first)
        sorted_parcels = sorted(self.parcels.items(),
                                key=lambda x: x[1]['area_ha'], reverse=True)

        for i, (parcel_id, parcel) in enumerate(sorted_parcels, 1):
            # Get recent intervention count
            recent_interventions = len([int for int in parcel['interventions']
                                        if int['date_debut'] >= '2025-01-01'])

            results.append(
                f"{i}. {parcel['name']} (UUID: {parcel_id})"
            )
            results.append(
                f"   Area: {parcel['area_ha']:.2f} ha | Crop: {parcel['current_crop']} "
                f"(ID: {parcel['current_crop_id']}) | Location: {parcel['location_code']}"
            )
            results.append(
                f"   Recent interventions: {recent_interventions} | "
                f"Last activity: {parcel['interventions'][0]['date_debut'] if parcel['interventions'] else 'None'}"
            )
            results.append("")

        results.append("---")
        results.append("Source: MesParcelles (https://mesparcelles.fr/)")
        return "\n".join(results)

    def _get_detailed_farm_overview(self) -> str:
        """Get detailed farm overview with individual parcel breakdown and interventions."""
        base_summary = self.get_farm_summary()
        detailed_parcels = self._get_all_parcels_overview()
        recent_activities = self.get_recent_activities(10)

        return f"{base_summary}\n\n{detailed_parcels}\n\n{recent_activities}"

    def _get_all_parcels_with_sorting(self, sort_by: str) -> str:
        """Get all parcels with specified sorting."""
        if not self.parcels:
            return "No parcels found in your farm data."

        # Sort parcels based on sort_by parameter
        if sort_by == "area":
            sorted_parcels = sorted(self.parcels.items(),
                                    key=lambda x: x[1]['area_ha'], reverse=True)
        elif sort_by == "name":
            sorted_parcels = sorted(self.parcels.items(),
                                    key=lambda x: x[1]['name'])
        elif sort_by == "crop":
            sorted_parcels = sorted(self.parcels.items(),
                                    key=lambda x: x[1]['current_crop'])
        elif sort_by == "location":
            sorted_parcels = sorted(self.parcels.items(),
                                    key=lambda x: x[1]['location_code'])
        elif sort_by == "activity":
            sorted_parcels = sorted(self.parcels.items(),
                                    key=lambda x: len(x[1]['interventions']), reverse=True)
        else:
            sorted_parcels = list(self.parcels.items())

        results = [f"ALL PARCELS (sorted by {sort_by})"]
        results.append("")

        for i, (parcel_id, parcel) in enumerate(sorted_parcels, 1):
            results.append(
                f"{i}. {parcel['name']} - {parcel['area_ha']:.2f} ha - "
                f"{parcel['current_crop']} - Location: {parcel['location_code']} - "
                f"Interventions: {len(parcel['interventions'])}"
            )

        results.append(f"\nTotal: {len(self.parcels)} parcels, {self.total_area:.2f} hectares")
        results.append("\n---")
        results.append("Source: MesParcelles (https://mesparcelles.fr/)")
        return "\n".join(results)

    def _get_seasonal_activities(self, focus: str, grouping: str) -> str:
        """Get seasonal activities with specified focus and grouping."""
        # Filter current season (2025) activities
        seasonal_interventions = [i for i in self.interventions if i['date_debut'].startswith('2025')]

        if focus != "all":
            # Filter by focus (crop, intervention type, or parcel)
            seasonal_interventions = [i for i in seasonal_interventions
                                      if focus.lower() in i['type'].lower() or
                                      focus.lower() in i['crop'].lower() or
                                      focus.lower() in i.get('parcel_name', '').lower()]

        if not seasonal_interventions:
            return f"No seasonal activities found for focus: {focus}"

        results = [f"SEASONAL ACTIVITIES 2025 (Focus: {focus}, Grouped by: {grouping})"]
        results.append("")

        if grouping == "by_crop":
            # Group by crop
            crop_groups = {}
            for intervention in seasonal_interventions:
                crop = intervention['crop']
                if crop not in crop_groups:
                    crop_groups[crop] = []
                crop_groups[crop].append(intervention)

            for crop, interventions in crop_groups.items():
                results.append(f"CROP: {crop} ({len(interventions)} interventions)")
                for intervention in interventions:
                    results.append(
                        f"  - {intervention['date_debut']}: {intervention['type']} "
                        f"on {intervention.get('parcel_name', 'Unknown')} - {intervention['area_ha']:.2f} ha"
                    )
                results.append("")

        elif grouping == "by_parcel":
            # Group by parcel
            parcel_groups = {}
            for intervention in seasonal_interventions:
                parcel = intervention.get('parcel_name', 'Unknown')
                if parcel not in parcel_groups:
                    parcel_groups[parcel] = []
                parcel_groups[parcel].append(intervention)

            for parcel, interventions in parcel_groups.items():
                results.append(f"PARCEL: {parcel} ({len(interventions)} interventions)")
                for intervention in interventions:
                    results.append(
                        f"  - {intervention['date_debut']}: {intervention['type']} "
                        f"of {intervention['crop']} - {intervention['area_ha']:.2f} ha"
                    )
                results.append("")

        else:  # chronological
            for i, intervention in enumerate(seasonal_interventions, 1):
                results.append(
                    f"{i}. {intervention['date_debut']}: {intervention['type']} "
                    f"of {intervention['crop']} on {intervention.get('parcel_name', 'Unknown')} - "
                    f"{intervention['area_ha']:.2f} ha"
                )

        results.append("---")
        results.append("Source: MesParcelles (https://mesparcelles.fr/)")
        return "\n".join(results)

    def _analyze_intervention_patterns(self, focus_area: Optional[str], timeframe_days: int) -> str:
        """Analyze intervention patterns and timing trends."""
        if not self.interventions:
            return "No intervention data available for pattern analysis."

        results = [f"INTERVENTION PATTERN ANALYSIS (Focus: {focus_area or 'All'})"]
        results.append("")

        # Analyze intervention frequency by month
        month_counts = {}
        crop_intervention_counts = {}

        for intervention in self.interventions:
            # Extract month from date
            month = intervention['date_debut'][:7]  # YYYY-MM
            if month not in month_counts:
                month_counts[month] = 0
            month_counts[month] += 1

            # Count interventions by crop
            crop = intervention['crop']
            if crop not in crop_intervention_counts:
                crop_intervention_counts[crop] = 0
            crop_intervention_counts[crop] += 1

        # Most active months
        sorted_months = sorted(month_counts.items(), key=lambda x: x[1], reverse=True)
        results.append("MOST ACTIVE MONTHS:")
        for month, count in sorted_months[:5]:
            results.append(f"- {month}: {count} interventions")

        results.append("")

        # Most intervention-intensive crops
        sorted_crops = sorted(crop_intervention_counts.items(), key=lambda x: x[1], reverse=True)
        results.append("MOST INTERVENTION-INTENSIVE CROPS:")
        for crop, count in sorted_crops:
            results.append(f"- {crop}: {count} interventions")

        results.append("")
        results.append("PATTERN INSIGHTS:")
        results.append(f"- Total interventions recorded: {len(self.interventions)}")
        results.append(f"- Average interventions per parcel: {len(self.interventions) / len(self.parcels):.1f}")
        results.append(f"- Most common intervention: {self.interventions[0]['type'] if self.interventions else 'None'}")

        results.append("\n---")
        results.append("Source: MesParcelles (https://mesparcelles.fr/)")
        return "\n".join(results)

    def _identify_management_gaps(self, focus_area: Optional[str], timeframe_days: int) -> str:
        """Identify potential missed interventions or inactive parcels."""
        results = [f"MANAGEMENT GAP ANALYSIS (Focus: {focus_area or 'All'})"]
        results.append("")

        # Find parcels with no recent activity
        inactive_parcels = []
        for parcel_id, parcel in self.parcels.items():
            if not parcel['interventions']:
                inactive_parcels.append(parcel['name'])
            elif parcel['interventions'][-1]['date_debut'] < '2025-01-01':
                inactive_parcels.append(parcel['name'])

        if inactive_parcels:
            results.append("PARCELS NEEDING ATTENTION (no recent activity):")
            for parcel in inactive_parcels:
                results.append(f"- {parcel}")
        else:
            results.append("✓ All parcels show recent activity")

        results.append("")

        # Analyze intervention consistency across similar crops
        crop_intervention_patterns = {}
        for parcel_id, parcel in self.parcels.items():
            crop = parcel['current_crop']
            if crop not in crop_intervention_patterns:
                crop_intervention_patterns[crop] = []
            crop_intervention_patterns[crop].append(len(parcel['interventions']))

        results.append("CROP MANAGEMENT CONSISTENCY:")
        for crop, intervention_counts in crop_intervention_patterns.items():
            if len(intervention_counts) > 1:
                avg_interventions = sum(intervention_counts) / len(intervention_counts)
                min_interventions = min(intervention_counts)
                max_interventions = max(intervention_counts)

                if max_interventions - min_interventions > 2:
                    results.append(
                        f"⚠ {crop}: Inconsistent management (range: {min_interventions}-{max_interventions} interventions)"
                    )
                else:
                    results.append(f"✓ {crop}: Consistent management (avg: {avg_interventions:.1f} interventions)")

        results.append("\n---")
        results.append("Source: MesParcelles (https://mesparcelles.fr/)")
        return "\n".join(results)

    def _analyze_operational_efficiency(self, focus_area: Optional[str], timeframe_days: int) -> str:
        """Analyze operation efficiency and workload distribution."""
        results = [f"OPERATIONAL EFFICIENCY ANALYSIS"]
        results.append("")

        # Calculate area efficiency (interventions per hectare)
        total_intervention_area = sum(i['area_ha'] for i in self.interventions)
        efficiency_ratio = total_intervention_area / self.total_area if self.total_area > 0 else 0

        results.append("AREA EFFICIENCY METRICS:")
        results.append(f"- Total farm area: {self.total_area:.2f} ha")
        results.append(f"- Total intervention area: {total_intervention_area:.2f} ha")
        results.append(f"- Efficiency ratio: {efficiency_ratio:.2f} (interventions per farm hectare)")

        results.append("")

        # Analyze workload by month
        monthly_workload = {}
        for intervention in self.interventions:
            month = intervention['date_debut'][:7]
            if month not in monthly_workload:
                monthly_workload[month] = 0
            monthly_workload[month] += intervention['area_ha']

        results.append("WORKLOAD DISTRIBUTION BY MONTH:")
        for month, area in sorted(monthly_workload.items()):
            results.append(f"- {month}: {area:.2f} ha worked")

        results.append("")
        results.append("EFFICIENCY INSIGHTS:")
        if efficiency_ratio > 2:
            results.append("- High intervention intensity - very active farm management")
        elif efficiency_ratio > 1:
            results.append("- Moderate intervention intensity - balanced management")
        else:
            results.append("- Low intervention intensity - extensive management style")

        results.append("\n---")
        results.append("Source: MesParcelles (https://mesparcelles.fr/)")
        return "\n".join(results)

    def _suggest_next_interventions(self, focus_area: Optional[str], timeframe_days: int) -> str:
        """Suggest upcoming interventions based on patterns."""
        results = [f"NEXT INTERVENTION SUGGESTIONS"]
        results.append("")

        # Analyze recent patterns to suggest next steps
        recent_interventions = [i for i in self.interventions if i['date_debut'] >= '2025-03-01']

        # Suggest based on crop stages and typical patterns
        crop_suggestions = {
            'blé tendre hiver': ['Disease monitoring', 'Spring fertilization', 'Weed control'],
            'colza hiver': ['Insect control', 'Flowering support', 'Harvest preparation'],
            'orge hiver': ['Growth monitoring', 'Disease prevention', 'Nutrient management'],
            'betterave sucrière': ['Seedbed preparation', 'Planting', 'Early weed control'],
            'lin fibres hiver': ['Growth monitoring', 'Disease management', 'Fiber quality optimization']
        }

        results.append("CROP-SPECIFIC SUGGESTIONS:")
        for crop in self.parcels_by_crop.keys():
            if crop in crop_suggestions:
                results.append(f"{crop.upper()}:")
                for suggestion in crop_suggestions[crop]:
                    results.append(f"  - {suggestion}")
            results.append("")

        # General timing suggestions based on current date
        results.append("SEASONAL RECOMMENDATIONS (June 2025):")
        results.append("- Monitor crop health and disease pressure")
        results.append("- Plan harvest timing for winter crops")
        results.append("- Prepare for summer crop management")
        results.append("- Review yield expectations and market planning")

        results.append("\n---")
        results.append("Source: MesParcelles (https://mesparcelles.fr/)")
        return "\n".join(results)

    def _generate_comparative_analysis(self, focus_area: Optional[str]) -> str:
        """Compare parcels/crops/locations performance."""
        results = [f"COMPARATIVE ANALYSIS (Focus: {focus_area or 'All'})"]
        results.append("")

        # Compare parcels by intervention intensity
        parcel_comparisons = []
        for parcel_id, parcel in self.parcels.items():
            interventions_per_ha = len(parcel['interventions']) / parcel['area_ha'] if parcel['area_ha'] > 0 else 0
            parcel_comparisons.append({
                'name': parcel['name'],
                'area': parcel['area_ha'],
                'interventions': len(parcel['interventions']),
                'intensity': interventions_per_ha,
                'crop': parcel['current_crop']
            })

        # Sort by intervention intensity
        parcel_comparisons.sort(key=lambda x: x['intensity'], reverse=True)

        results.append("PARCEL MANAGEMENT INTENSITY RANKING:")
        for i, parcel in enumerate(parcel_comparisons, 1):
            results.append(
                f"{i}. {parcel['name']} - {parcel['intensity']:.2f} interventions/ha "
                f"({parcel['interventions']} total, {parcel['area']:.2f} ha, {parcel['crop']})"
            )

        results.append("")

        # Compare crops by average management intensity
        crop_stats = {}
        for parcel in parcel_comparisons:
            crop = parcel['crop']
            if crop not in crop_stats:
                crop_stats[crop] = {'total_interventions': 0, 'total_area': 0, 'count': 0}
            crop_stats[crop]['total_interventions'] += parcel['interventions']
            crop_stats[crop]['total_area'] += parcel['area']
            crop_stats[crop]['count'] += 1

        results.append("CROP MANAGEMENT COMPARISON:")
        for crop, stats in crop_stats.items():
            avg_intensity = stats['total_interventions'] / stats['total_area'] if stats['total_area'] > 0 else 0
            results.append(
                f"- {crop}: {avg_intensity:.2f} interventions/ha average "
                f"({stats['count']} parcels, {stats['total_area']:.2f} ha total)"
            )

        results.append("\n---")
        results.append("Source: MesParcelles (https://mesparcelles.fr/)")
        return "\n".join(results)

    def _analyze_workload_distribution(self, timeframe_days: int) -> str:
        """Analyze workload distribution and planning."""
        results = [f"WORKLOAD DISTRIBUTION ANALYSIS"]
        results.append("")

        # Analyze workload by date
        daily_workload = {}
        for intervention in self.interventions:
            date = intervention['date_debut']
            if date not in daily_workload:
                daily_workload[date] = {'count': 0, 'area': 0}
            daily_workload[date]['count'] += 1
            daily_workload[date]['area'] += intervention['area_ha']

        # Find peak workload days
        peak_days = sorted(daily_workload.items(),
                           key=lambda x: x[1]['area'], reverse=True)[:5]

        results.append("PEAK WORKLOAD DAYS:")
        for date, workload in peak_days:
            results.append(
                f"- {date}: {workload['count']} interventions, {workload['area']:.2f} ha worked"
            )

        results.append("")

        # Analyze workload distribution by intervention type
        type_workload = {}
        for intervention in self.interventions:
            int_type = intervention['type']
            if int_type not in type_workload:
                type_workload[int_type] = {'count': 0, 'area': 0}
            type_workload[int_type]['count'] += 1
            type_workload[int_type]['area'] += intervention['area_ha']

        results.append("WORKLOAD BY INTERVENTION TYPE:")
        for int_type, workload in sorted(type_workload.items(),
                                         key=lambda x: x[1]['area'], reverse=True):
            results.append(
                f"- {int_type}: {workload['count']} operations, {workload['area']:.2f} ha total"
            )

        results.append("")
        results.append("WORKLOAD INSIGHTS:")
        total_operations = len(self.interventions)
        avg_daily_operations = total_operations / len(daily_workload) if daily_workload else 0
        results.append(f"- Total operations: {total_operations}")
        results.append(f"- Active days: {len(daily_workload)}")
        results.append(f"- Average operations per active day: {avg_daily_operations:.1f}")

        results.append("\n---")
        results.append("Source: MesParcelles (https://mesparcelles.fr/)")
        return "\n".join(results)


# ---- REFINED TOOL FUNCTIONS FOR AGENT INTEGRATION ----

async def get_farm_overview(user_id: int, detail_level: DetailLevel = "summary") -> str:
    """
    Master farm overview tool - handles most general farm questions.
    
    Provides comprehensive farm analysis with different levels of detail based on
    farmer's specific needs. Covers farm statistics, crop distribution, parcel
    breakdown, and recent activity patterns.
    
    Args:
        user_id: Farmer's user ID for data retrieval
        detail_level: Level of detail required:
            - "summary": High-level stats, crop distribution, recent activity count
            - "detailed": Individual parcel breakdown with recent interventions  
            - "parcels_only": Focus on all parcels with basic info
            
    Returns:
        str: Comprehensive farm overview adapted to requested detail level
    
    Example Usage:
        - "Tell me about my farm" → detail_level="summary"
        - "Give me an overview" → detail_level="summary" 
        - "Tell me about all my parcels" → detail_level="parcels_only"
        - "Show me everything in detail" → detail_level="detailed"
    """
    try:
        logger.debug(f"Fetching farm overview for user: {user_id}, detail: {detail_level}")
        raw_data = await fetch_farm_data(user_id)

        # Check if we have valid farm data
        if not raw_data or not raw_data.get('metadata', {}).get('parcels'):
            return ("FARM DATA NOT AVAILABLE: Your MesParcelles account appears to be empty or not connected. "
                    "To get farm insights, please ensure your parcels and agricultural data are properly "
                    "synchronized with [MesParcelles](https://mesparcelles.fr/). You may need to link your farm management system or "
                    "manually enter your parcel information in MesParcelles.")

        toolkit = MesParcellesDataTool(raw_data)

        if detail_level == "parcels_only":
            return toolkit._get_all_parcels_overview()
        elif detail_level == "detailed":
            return toolkit._get_detailed_farm_overview()
        else:
            return toolkit.get_farm_summary()

    except Exception as e:
        logger.error(f"Error fetching farm overview: {str(e)}")
        return ("FARM DATA UNAVAILABLE: Unable to retrieve your farm information from MesParcelles. "
                "This could be due to: (1) Your MesParcelles account is not properly connected, "
                "(2) No farm data has been entered in your account yet, or (3) A temporary connection issue. "
                "Please check your [MesParcelles](https://mesparcelles.fr/) account and ensure your farm data is properly set up.")


async def analyze_parcels(user_id: int,
                          scope: ParcelScope = "all",
                          filter_by: Optional[str] = None,
                          sort_by: SortOption = "area") -> str:
    """
    Intelligent parcel analysis - handles all parcel-related queries.
    
    Provides comprehensive parcel analysis with flexible filtering and sorting options.
    Can analyze all parcels, specific parcels, or group by various criteria.
    
    Args:
        user_id: Farmer's user ID for data retrieval
        scope: Analysis scope:
            - "all": All parcels overview
            - "single": Specific parcel (requires filter_by=parcel_name)
            - "by_crop": Parcels by crop type (requires filter_by=crop_name)  
            - "by_location": Parcels by location (requires filter_by=location_code)
        filter_by: Filter criteria (required for single, by_crop, by_location scopes)
        sort_by: Sorting option:
            - "area": Sort by parcel size
            - "activity": Sort by recent activity level
            - "crop": Sort by crop type
            - "location": Sort by location code
            - "name": Sort by parcel name
            
    Returns:
        str: Detailed parcel analysis based on specified criteria
    
    Example Usage:
        - "Show me parcelle n°1" → scope="single", filter_by="parcelle n°1"
        - "Which parcels have wheat?" → scope="by_crop", filter_by="wheat"
        - "Tell me about my biggest parcels" → scope="all", sort_by="area"
        - "What's in location 14030?" → scope="by_location", filter_by="14030"
    """
    try:
        logger.debug(f"Analyzing parcels for user: {user_id}, scope: {scope}, filter: {filter_by}")
        raw_data = await fetch_farm_data(user_id)

        # Check if we have valid farm data
        if not raw_data or not raw_data.get('metadata', {}).get('parcels'):
            return ("NO PARCEL DATA: Your MesParcelles account doesn't have any parcel data yet. "
                    "Please add your parcels to [MesParcelles](https://mesparcelles.fr/) first, including parcel names, areas, and current crops.")

        toolkit = MesParcellesDataTool(raw_data)

        if scope == "single":
            if not filter_by:
                return "ERROR: Parcel name required for single parcel analysis. Please specify filter_by parameter."
            return toolkit.get_parcel_details(filter_by)
        elif scope == "by_crop":
            if not filter_by:
                return "ERROR: Crop name required for crop-based analysis. Please specify filter_by parameter."
            return toolkit.get_parcels_by_crop(filter_by)
        elif scope == "by_location":
            if not filter_by:
                return "ERROR: Location code required for location-based analysis. Please specify filter_by parameter."
            return toolkit.get_parcels_by_location(filter_by)
        else:  # scope == "all"
            return toolkit._get_all_parcels_with_sorting(sort_by)

    except Exception as e:
        logger.error(f"Error analyzing parcels: {str(e)}")
        return ("PARCEL ANALYSIS UNAVAILABLE: Cannot retrieve parcel information from MesParcelles. "
                f"Please verify that your parcel data is properly set up in your [MesParcelles](https://mesparcelles.fr/) account.")


async def analyze_farm_activities(user_id: int,
                                  time_scope: TimeScope = "recent",
                                  focus: ActivityFocus = "all",
                                  grouping: GroupingOption = "chronological",
                                  limit: int = 10) -> str:
    """
    Comprehensive activity analysis - handles all intervention queries.
    
    Provides detailed analysis of farm activities with flexible time filtering,
    focus areas, and grouping options. Essential for tracking operations and planning.
    
    Args:
        user_id: Farmer's user ID for data retrieval
        time_scope: Time period to analyze:
            - "recent": Last activities (uses limit parameter)
            - "this_season": Current season activities (2025)
            - "date_range": Specific dates (requires focus="YYYY-MM-DD to YYYY-MM-DD")
            - "specific_date": Single date (requires focus="YYYY-MM-DD")
        focus: Activity focus:
            - "all": All interventions
            - "fertilization": Only fertilization activities
            - "planting": Only planting/seeding activities
            - "seeding": Only seeding activities
            - "treatment": Only treatment activities
            - "harvest": Only harvest activities
            - Or specific crop name or parcel name
        grouping: How to group results:
            - "chronological": Sort by date
            - "by_crop": Group by crop type
            - "by_parcel": Group by parcel
            - "by_type": Group by intervention type
            - "by_location": Group by location
        limit: Maximum number of activities to return (for recent scope)
            
    Returns:
        str: Comprehensive activity analysis based on specified criteria
    
    Example Usage:
        - "What did I do recently?" → time_scope="recent"
        - "Show me all fertilization work" → focus="fertilization"
        - "What happened in March?" → time_scope="date_range", focus="2025-03-01 to 2025-03-31"
        - "When did I plant my wheat?" → focus="wheat", grouping="by_crop"
    """
    try:
        logger.debug(f"Analyzing activities for user: {user_id}, time: {time_scope}, focus: {focus}")
        raw_data = await fetch_farm_data(user_id)

        # Check if we have valid farm data
        if not raw_data or not raw_data.get('metadata', {}).get('interventions'):
            return ("NO ACTIVITY DATA: Your MesParcelles account doesn't show any recorded farm activities yet. "
                    "To track your farm operations, start recording your interventions (planting, fertilization, "
                    "harvesting, etc.) in [MesParcelles](https://mesparcelles.fr/). This will help you monitor your farming timeline.")

        toolkit = MesParcellesDataTool(raw_data)

        # Handle different time scopes and focus areas
        if time_scope == "recent":
            if focus == "all":
                return toolkit.get_recent_activities(limit)
            else:
                return toolkit.get_intervention_history(focus, limit)
        elif time_scope == "date_range" or time_scope == "specific_date":
            # Parse date range from focus parameter
            if " to " in focus:
                start_date, end_date = focus.split(" to ")
                return toolkit.get_interventions_by_date_range(start_date.strip(), end_date.strip())
            else:
                return toolkit.get_interventions_by_date_range(focus)
        else:  # this_season
            return toolkit._get_seasonal_activities(focus, grouping)

    except Exception as e:
        logger.error(f"Error analyzing farm activities: {str(e)}")
        return ("ACTIVITY ANALYSIS UNAVAILABLE: Cannot retrieve farm activity information from MesParcelles. "
                "Please check your [MesParcelles](https://mesparcelles.fr/) account and ensure your farm operations are being logged.")


async def get_farm_insights(user_id: int,
                            insight_type: InsightType = "patterns",
                            focus_area: Optional[str] = None,
                            timeframe_days: int = 30) -> str:
    """
    Intelligent insights and planning support - provides actionable recommendations.
    
    Analyzes farm data to provide intelligent insights, identify patterns, gaps,
    and suggest next steps for optimal farm management. Uses pattern recognition
    and agricultural best practices.
    
    Args:
        user_id: Farmer's user ID for data retrieval
        insight_type: Type of insights to generate:
            - "patterns": Identify intervention patterns and timing trends
            - "gaps": Find potential missed interventions or inactive parcels
            - "efficiency": Analyze operation efficiency and workload distribution
            - "next_steps": Suggest upcoming interventions based on patterns
            - "comparison": Compare parcels/crops/locations performance
            - "workload": Analyze workload distribution and planning
        focus_area: Optional focus area:
            - "all": Farm-wide analysis (default)
            - Specific crop name: Crop-specific insights
            - Specific parcel name: Parcel-specific insights
            - Specific intervention type: Intervention-specific insights
        timeframe_days: Analysis timeframe in days (default: 30)
            
    Returns:
        str: Intelligent insights and actionable recommendations
    
    Example Usage:
        - "What should I do next?" → insight_type="next_steps"
        - "Which parcels need attention?" → insight_type="gaps"
        - "How is my wheat management?" → insight_type="patterns", focus_area="wheat"
        - "Am I behind on any work?" → insight_type="gaps"
    """
    try:
        logger.debug(f"Generating insights for user: {user_id}, type: {insight_type}, focus: {focus_area}")
        raw_data = await fetch_farm_data(user_id)

        # Check if we have valid farm data
        if not raw_data or not raw_data.get('metadata'):
            return ("NO INSIGHT DATA: Cannot generate farm insights because your MesParcelles account "
                    "doesn't have sufficient data yet. Please ensure your parcels and interventions are "
                    "properly recorded in [MesParcelles](https://mesparcelles.fr/) to get intelligent recommendations.")

        toolkit = MesParcellesDataTool(raw_data)

        # Generate different types of insights
        if insight_type == "patterns":
            return toolkit._analyze_intervention_patterns(focus_area, timeframe_days)
        elif insight_type == "gaps":
            return toolkit._identify_management_gaps(focus_area, timeframe_days)
        elif insight_type == "efficiency":
            return toolkit._analyze_operational_efficiency(focus_area, timeframe_days)
        elif insight_type == "next_steps":
            return toolkit._suggest_next_interventions(focus_area, timeframe_days)
        elif insight_type == "comparison":
            return toolkit._generate_comparative_analysis(focus_area)
        else:  # workload
            return toolkit._analyze_workload_distribution(timeframe_days)

    except Exception as e:
        logger.error(f"Error generating farm insights: {str(e)}")
        return ("INSIGHTS UNAVAILABLE: Cannot generate farm insights from MesParcelles data. "
                "Please ensure your farm data is properly set up in your [MesParcelles](https://mesparcelles.fr/) account.")
