"""
Integrated Management Planner Tool for E-Phy Agricultural Database.
Creates multi-product IPM strategies with rotation and tank-mix recommendations.
"""
import difflib
import re
from dataclasses import dataclass
from typing import List, Optional, Dict, Any, Tuple

from sqlalchemy import select, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from models.e_phy import Product, ProductUse, Crop, Target, ProductSubstance
from .types import (
    ResistanceRisk, BudgetConstraint
)


@dataclass
class ApplicationTiming:
    """Specific application timing information."""
    timing_name: str
    target_window: str
    bbch_stage_min: Optional[int]
    bbch_stage_max: Optional[int]
    calendar_period: str
    weather_requirements: List[str]
    urgency_level: str
    flexibility_days: int


@dataclass
class TankMixProduct:
    """Product in a tank mix combination."""
    product_id: int
    product_name: str
    active_substances: List[str]
    dose: str
    function: str
    compatibility_score: float
    safety_considerations: List[str]


@dataclass
class ApplicationEvent:
    """Single application event in the management plan."""
    timing: ApplicationTiming
    primary_targets: List[str]
    secondary_targets: List[str]
    tank_mix: List[TankMixProduct]
    total_cost_estimate: Optional[str]
    equipment_requirements: List[str]
    weather_constraints: List[str]
    buffer_zone_requirements: Dict[str, int]
    pre_application_checks: List[str]
    post_application_monitoring: List[str]


@dataclass
class ResistanceManagementPlan:
    """Resistance management strategy for the season."""
    mode_of_action_rotation: List[str]
    chemical_families: List[str]
    rotation_schedule: Dict[str, str]
    resistance_risk_assessment: str
    monitoring_protocols: List[str]
    contingency_plans: List[str]
    compliance_score: float


@dataclass
class EconomicAnalysis:
    """Economic analysis of the management plan."""
    total_estimated_cost: str
    cost_per_hectare: str
    cost_per_target_controlled: str
    cost_breakdown: Dict[str, str]
    budget_compliance: str
    cost_optimization_suggestions: List[str]
    roi_indicators: List[str]


@dataclass
class DataQualityMetrics:
    """Data quality and completeness metrics."""
    products_with_substances: int
    products_without_substances: int
    substance_completeness_rate: float
    dose_data_completeness: float
    buffer_zone_completeness: float
    harvest_interval_completeness: float
    overall_quality_score: float
    data_gaps: List[str]


@dataclass
class SeasonalStrategy:
    """Overall seasonal strategy summary."""
    crop_name: str
    total_targets: int
    strategy_type: str
    application_count: int
    season_length_days: int
    coverage_percentage: float
    confidence_level: str
    data_quality: DataQualityMetrics


@dataclass
class IntegratedManagementResponse:
    """Complete integrated management plan response."""
    seasonal_strategy: SeasonalStrategy
    application_schedule: List[ApplicationEvent]
    resistance_management: ResistanceManagementPlan
    economic_analysis: EconomicAnalysis
    risk_assessments: List[Dict[str, Any]]
    alternative_scenarios: List[Dict[str, Any]]
    implementation_guidelines: List[str]
    monitoring_recommendations: List[str]
    contingency_protocols: List[str]
    data_limitations: List[str]
    quality_indicators: Dict[str, Any]
    ai_recommendations: List[str]


async def create_integrated_management_plan(
        session: AsyncSession,
        crop_name: str,
        target_pests: List[str],
        season_length: int = 120,
        resistance_risk: ResistanceRisk = "medium",
        organic_preference: bool = False,
        budget_constraint: Optional[BudgetConstraint] = None,
        application_windows: Optional[List[Dict[str, Any]]] = None,
        field_size_ha: Optional[float] = None,
) -> IntegratedManagementResponse:
    """
    Create multi-product IPM strategies with rotation and tank-mix recommendations.
    
    Args:
        crop_name: Name of the crop to manage
        target_pests: List of pests/diseases to control
        season_length: Growing season length in days
        resistance_risk: Resistance development risk level
        organic_preference: Prefer biological products
        budget_constraint: Budget limitations
        application_windows: Preferred timing windows
        field_size_ha: Field size for cost calculations

    Returns:
        IntegratedManagementResponse with complete management plan
    """

    # Find crop in database
    crop = await _find_crop_with_translation(session, crop_name)
    if not crop:
        return _create_crop_not_found_response(crop_name)

    # Find target pests with translation support
    targets = []
    target_translation_notes = []

    for pest_name in target_pests:
        target = await _find_target_with_translation(session, pest_name)
        if target:
            targets.append(target)
        else:
            target_translation_notes.append(f"Target '{pest_name}' not found - using similar targets")

    if not targets:
        return _create_no_targets_response(crop_name, target_pests)

    # Get available products for crop-target combinations
    available_products = await _get_available_products_matrix(
        session, crop, targets, organic_preference
    )

    if not available_products:
        return _create_no_products_response(crop, targets)

    # Analyze product compatibility and effectiveness
    product_analysis = await _analyze_product_compatibility(
        session, available_products, resistance_risk
    )

    # Create application schedule
    application_schedule = await _create_application_schedule(
        session, crop, targets, available_products, product_analysis,
        season_length, application_windows, resistance_risk
    )

    # Develop resistance management plan
    resistance_plan = await _develop_resistance_management(
        session, available_products, application_schedule, resistance_risk
    )

    # Perform economic analysis
    economic_analysis = await _perform_economic_analysis(
        available_products, application_schedule, budget_constraint, field_size_ha
    )

    # Generate risk assessments
    risk_assessments = await _assess_implementation_risks(
        session, crop, targets, application_schedule, resistance_plan
    )

    # Create alternative scenarios
    alternative_scenarios = await _generate_alternative_scenarios(
        session, crop, targets, available_products, budget_constraint, organic_preference
    )

    # Calculate data quality metrics
    data_quality = await _calculate_data_quality_metrics(
        session, available_products, application_schedule
    )

    # Calculate seasonal strategy summary
    seasonal_strategy = SeasonalStrategy(
        crop_name=crop.crop_name,
        total_targets=len(targets),
        strategy_type=_determine_strategy_type(application_schedule, resistance_plan),
        application_count=len(application_schedule),
        season_length_days=season_length,
        coverage_percentage=_calculate_coverage_percentage(targets, available_products),
        confidence_level=_assess_plan_confidence(available_products, application_schedule),
        data_quality=data_quality
    )

    # Generate implementation guidelines
    implementation_guidelines = _generate_implementation_guidelines(
        seasonal_strategy, application_schedule, resistance_plan, economic_analysis
    )

    # Create monitoring recommendations
    monitoring_recommendations = _create_monitoring_recommendations(
        crop, targets, application_schedule, resistance_plan
    )

    # Develop contingency protocols
    contingency_protocols = _develop_contingency_protocols(
        crop, targets, available_products, resistance_plan
    )

    # Identify data limitations
    data_limitations = _identify_data_limitations(
        crop, targets, available_products, target_translation_notes
    )

    # Generate quality indicators for AI decision making
    quality_indicators = _generate_quality_indicators(
        data_quality, product_analysis, resistance_plan, economic_analysis
    )

    # Generate AI-specific recommendations
    ai_recommendations = _generate_ai_recommendations(
        seasonal_strategy, product_analysis, data_quality, resistance_plan
    )

    return IntegratedManagementResponse(
        seasonal_strategy=seasonal_strategy,
        application_schedule=application_schedule,
        resistance_management=resistance_plan,
        economic_analysis=economic_analysis,
        risk_assessments=risk_assessments,
        alternative_scenarios=alternative_scenarios,
        implementation_guidelines=implementation_guidelines,
        monitoring_recommendations=monitoring_recommendations,
        contingency_protocols=contingency_protocols,
        data_limitations=data_limitations,
        quality_indicators=quality_indicators,
        ai_recommendations=ai_recommendations
    )


async def _find_crop_with_translation(session: AsyncSession, crop_name: str) -> Optional[Crop]:
    """Find crop with English-French translation support."""

    # Common English-French crop translations
    crop_translations = {
        "wheat": "blé",
        "corn": "maïs",
        "maize": "maïs",
        "potato": "pomme de terre",
        "potatoes": "pomme de terre",
        "grape": "vigne",
        "grapes": "vigne",
        "grapevine": "vigne",
        "apple": "pommier",
        "tomato": "tomate",
        "tomatoes": "tomate",
        "lettuce": "laitue",
        "carrot": "carotte",
        "carrots": "carotte",
        "cabbage": "choux",
        "barley": "orge",
        "oat": "avoine",
        "oats": "avoine",
        "rye": "seigle",
        "rice": "riz",
        "soybean": "soja",
        "soybeans": "soja",
        "soy": "soja",
        "sunflower": "tournesol",
        "rapeseed": "colza",
        "canola": "colza"
    }

    # Try direct match first
    search_terms = [crop_name.lower()]

    # Add translation if available
    if crop_name.lower() in crop_translations:
        search_terms.append(crop_translations[crop_name.lower()])

    # Try reverse translation
    for english, french in crop_translations.items():
        if crop_name.lower() == french:
            search_terms.append(english)

    query = select(Crop).where(
        or_(
            *[Crop.crop_name.ilike(f"%{term}%") for term in search_terms],
            *[Crop.normalized_name.ilike(f"%{term}%") for term in search_terms],
            *[Crop.common_synonyms.ilike(f"%{term}%") for term in search_terms]
        )
    ).limit(10)

    result = await session.execute(query)
    crops = result.scalars().all()

    if not crops:
        return None
    elif len(crops) == 1:
        return crops[0]
    else:
        # Multiple matches - find the best one
        for crop in crops:
            # Prefer exact matches in crop name
            for term in search_terms:
                if term.lower() == crop.crop_name.lower():
                    return crop
                if term.lower() == crop.normalized_name.lower():
                    return crop

        # If no exact match, return the first one
        return crops[0]


async def _find_target_with_translation(session: AsyncSession, target_name: str) -> Optional[Target]:
    """Find target with enhanced fuzzy matching and translation support."""

    # Extended target translations with common patterns
    target_translations = {
        "late blight": ["mildiou"],
        "downy mildew": ["mildiou"],
        "mildew": ["mildiou"],
        "powdery mildew": ["oïdium", "oidium"],
        "oidium": ["oïdium"],
        "aphids": ["pucerons", "puceron"],
        "plant lice": ["pucerons"],
        "caterpillars": ["chenilles", "chenille"],
        "slugs": ["limaces", "limace"],
        "snails": ["escargots"],
        "rust": ["rouille"],
        "rusts": ["rouille"],
        "scab": ["tavelure"],
        "leaf spot": ["septoriose"],
        "blight": ["mildiou"],
        "wilt": ["fusariose"],
        "rot": ["pourriture"],
        "mites": ["acariens", "acarien"],
        "thrips": ["thrips"],
        "whitefly": ["aleurodes"],
        "scale insects": ["cochenilles"],
        "beetles": ["coléoptères"],
        "weevils": ["charançons"],
        "fungi": ["champignon", "fongique"],
        "fungus": ["champignon"],
        "disease": ["maladie"],
        "pest": ["ravageur"],
        "insect": ["insecte"]
    }

    search_terms = [target_name.lower().strip()]

    # Add translations
    target_lower = target_name.lower()
    for english, french_terms in target_translations.items():
        if english in target_lower:
            search_terms.extend(french_terms)
        for french in french_terms:
            if french in target_lower:
                search_terms.append(english)

    # Direct search first - handle multiple results
    query = select(Target).where(
        or_(*[
            Target.target_name.ilike(f"%{term}%")
            for term in search_terms
        ])
    ).limit(10)  # Limit to avoid too many results

    result = await session.execute(query)
    direct_matches = result.scalars().all()

    # If we have direct matches, pick the best one
    target = None
    if direct_matches:
        if len(direct_matches) == 1:
            target = direct_matches[0]
        else:
            # Multiple matches - find the best one
            best_match = None
            best_score = 0

            for match in direct_matches:
                # Prefer exact matches or shorter names (more specific)
                clean_match_name = re.sub(r'\(\d+\)', '', match.target_name).strip().lower()

                for term in search_terms:
                    if term == clean_match_name:  # Exact match
                        target = match
                        break
                    elif term in clean_match_name:
                        score = len(term) / len(clean_match_name)  # Longer term in shorter name = better
                        if score > best_score:
                            best_score = score
                            best_match = match

                if target:  # Found exact match
                    break

            if not target:
                target = best_match

    if target:
        return target

    # Fuzzy matching fallback - get all targets and use fuzzy matching
    all_targets_query = select(Target).limit(500)  # Limit for performance
    all_targets_result = await session.execute(all_targets_query)
    all_targets = all_targets_result.scalars().all()

    # Use fuzzy matching to find best match
    best_match = None
    best_ratio = 0.6  # Minimum similarity threshold

    for target_candidate in all_targets:
        # Clean target name for comparison
        clean_target_name = re.sub(r'\(\d+\)', '', target_candidate.target_name).strip().lower()

        # Calculate similarity
        try:
            ratio = difflib.SequenceMatcher(None, target_name.lower(), clean_target_name).ratio()

            if ratio > best_ratio:
                best_ratio = ratio
                best_match = target_candidate

            # Also check against search terms
            for term in search_terms:
                ratio = difflib.SequenceMatcher(None, term, clean_target_name).ratio()
                if ratio > best_ratio:
                    best_ratio = ratio
                    best_match = target_candidate
        except Exception:
            # Fallback if difflib fails
            continue

    return best_match


async def _get_available_products_matrix(
        session: AsyncSession,
        crop: Crop,
        targets: List[Target],
        organic_preference: bool
) -> Dict[Tuple[int, int], List[Product]]:
    """Get available products for each crop-target combination with enhanced filtering."""

    products_matrix = {}

    for target in targets:
        # Enhanced query with better filtering for data quality
        query = select(Product).options(
            selectinload(Product.substances).selectinload(ProductSubstance.substance)
        ).join(ProductUse).where(
            and_(
                ProductUse.crop_id == crop.id,
                ProductUse.target_id == target.id,
                ProductUse.is_currently_authorized == True,
                Product.is_currently_authorized == True,
                # Prefer products with substance data for better analysis
                Product.id.in_(
                    select(ProductSubstance.product_id).distinct()
                )
            )
        )

        # Apply organic filter with enhanced matching
        if organic_preference:
            query = query.where(
                or_(
                    Product.function_category.ilike("%biologique%"),
                    Product.function_category.ilike("%bio%"),
                    Product.authorized_mentions.ilike("%biologique%"),
                    Product.authorized_mentions.ilike("%bio%"),
                    Product.authorized_mentions.ilike("%organique%"),
                    # Additional organic indicators
                    Product.product_name.ilike("%bio%"),
                    Product.product_name.ilike("%organic%")
                )
            )

        # Order by authorization date (nulls last) and product name
        query = query.order_by(
            Product.first_authorization_date.desc().nulls_last(),
            Product.product_name
        )

        result = await session.execute(query.distinct())
        products = list(result.scalars().all())  # Convert to list

        # Post-filter for data quality if needed
        quality_products = []
        for product in products:
            # Check if product has basic required data
            has_substances = len(product.substances) > 0
            has_function = product.function_category is not None

            if has_substances and has_function:
                quality_products.append(product)
            elif len(quality_products) < 3:  # Keep some products even with limited data
                quality_products.append(product)

        products_matrix[(crop.id, target.id)] = quality_products or products  # Fallback to all if none qualify

    return products_matrix


async def _analyze_product_compatibility(
        session: AsyncSession,
        products_matrix: Dict[Tuple[int, int], List[Product]],
        resistance_risk: ResistanceRisk
) -> Dict[str, Any]:
    """Enhanced product compatibility analysis with data quality assessment."""

    all_products = []
    for products in products_matrix.values():
        all_products.extend(products)

    # Remove duplicates
    unique_products = {p.id: p for p in all_products}.values()

    # Enhanced substance analysis with data quality metrics
    substance_analysis = {}
    products_with_substances = 0
    products_without_substances = 0

    for product in unique_products:
        # Get substance information with null checking
        substances = []
        primary_substances = []

        if product.substances:
            for ps in product.substances:
                if ps.substance and ps.substance.name:
                    substances.append(ps.substance.name)
                    if ps.primary_substance:
                        primary_substances.append(ps.substance.name)

        if substances:
            products_with_substances += 1
        else:
            products_without_substances += 1

        # Enhanced analysis
        substance_analysis[product.id] = {
            "substances": substances,
            "primary_substances": primary_substances,
            "chemical_family": _infer_chemical_family(substances),
            "mode_of_action": _infer_mode_of_action(substances),
            "resistance_risk": _assess_product_resistance_risk(substances, resistance_risk),
            "data_quality_score": _calculate_product_data_quality(product, substances),
            "has_substance_data": len(substances) > 0,
            "substance_count": len(substances),
            "function_category": product.function_category or "Unknown"
        }

    # Calculate enhanced compatibility scores
    compatibility_matrix = {}
    product_list = list(unique_products)

    for i, product1 in enumerate(product_list):
        for j, product2 in enumerate(product_list):
            if i < j:  # Avoid duplicates
                analysis1 = substance_analysis[product1.id]
                analysis2 = substance_analysis[product2.id]

                compatibility = _calculate_enhanced_tank_mix_compatibility(
                    analysis1, analysis2
                )
                compatibility_matrix[(product1.id, product2.id)] = compatibility

    # Calculate overall data quality metrics
    total_products = len(unique_products)
    substance_completeness = products_with_substances / max(total_products, 1)

    chemical_families = set(
        analysis["chemical_family"]
        for analysis in substance_analysis.values()
        if analysis["chemical_family"] != "Unknown"
    )

    mode_of_actions = set(
        analysis["mode_of_action"]
        for analysis in substance_analysis.values()
        if analysis["mode_of_action"] != "Unknown"
    )

    return {
        "substance_analysis": substance_analysis,
        "compatibility_matrix": compatibility_matrix,
        "total_products": total_products,
        "products_with_substances": products_with_substances,
        "products_without_substances": products_without_substances,
        "substance_completeness_rate": substance_completeness,
        "chemical_families": len(chemical_families),
        "chemical_family_list": list(chemical_families),
        "mode_of_actions": len(mode_of_actions),
        "mode_of_action_list": list(mode_of_actions),
        "resistance_diversity_score": len(chemical_families) * 0.4 + len(mode_of_actions) * 0.6
    }


async def _create_application_schedule(
        session: AsyncSession,
        crop: Crop,
        targets: List[Target],
        products_matrix: Dict[Tuple[int, int], List[Product]],
        product_analysis: Dict[str, Any],
        season_length: int,
        application_windows: Optional[List[Dict[str, Any]]],
        resistance_risk: ResistanceRisk
) -> List[ApplicationEvent]:
    """Create optimized application schedule."""

    schedule = []

    # Default application windows if not provided
    if not application_windows:
        application_windows = _generate_default_windows(season_length, len(targets))

    # Plan applications based on targets and timing
    for i, window in enumerate(application_windows):
        # Determine primary targets for this timing
        primary_targets = targets[:2] if len(targets) >= 2 else targets
        secondary_targets = targets[2:] if len(targets) > 2 else []

        # Select best products for this application
        selected_products = await _select_optimal_products(
            session, crop, primary_targets, products_matrix,
            product_analysis, resistance_risk, i
        )

        if not selected_products:
            continue

        # Create tank mix
        tank_mix = []
        for product_data in selected_products:
            product, dose_info = product_data

            tank_mix_product = TankMixProduct(
                product_id=product.id,
                product_name=product.product_name,
                active_substances=[ps.substance.name for ps in product.substances if ps.substance],
                dose=f"{dose_info.get('dose', 'As per label')} {dose_info.get('unit', '')}".strip(),
                function=product.function_category or "Unknown",
                compatibility_score=dose_info.get('compatibility', 0.8),
                safety_considerations=_extract_safety_considerations(product)
            )
            tank_mix.append(tank_mix_product)

        # Create timing information
        timing = ApplicationTiming(
            timing_name=window.get("name", f"Application {i + 1}"),
            target_window=window.get("window", f"Week {(i * season_length // len(application_windows)) // 7 + 1}"),
            bbch_stage_min=window.get("bbch_min"),
            bbch_stage_max=window.get("bbch_max"),
            calendar_period=window.get("calendar", f"Day {i * season_length // len(application_windows)}"),
            weather_requirements=window.get("weather", ["Dry conditions", "Wind < 15 km/h"]),
            urgency_level=window.get("urgency", "normal"),
            flexibility_days=window.get("flexibility", 7)
        )

        # Calculate costs and requirements
        cost_estimate = _calculate_application_cost(tank_mix, field_size_ha=1.0)
        equipment_reqs = _determine_equipment_requirements(tank_mix)
        buffer_zones = await _calculate_buffer_zones(session, selected_products)

        application = ApplicationEvent(
            timing=timing,
            primary_targets=[t.target_name for t in primary_targets],
            secondary_targets=[t.target_name for t in secondary_targets],
            tank_mix=tank_mix,
            total_cost_estimate=cost_estimate,
            equipment_requirements=equipment_reqs,
            weather_constraints=timing.weather_requirements,
            buffer_zone_requirements=buffer_zones,
            pre_application_checks=_generate_pre_application_checks(tank_mix),
            post_application_monitoring=_generate_monitoring_plan(primary_targets + secondary_targets)
        )

        schedule.append(application)

    return schedule


async def _select_optimal_products(
        session: AsyncSession,
        crop: Crop,
        targets: List[Target],
        products_matrix: Dict[Tuple[int, int], List[Product]],
        product_analysis: Dict[str, Any],
        resistance_risk: ResistanceRisk,
        application_number: int
) -> List[Tuple[Product, Dict[str, Any]]]:
    """Select optimal products for an application."""

    selected = []
    used_substances = set()

    for target in targets:
        available_products = products_matrix.get((crop.id, target.id), [])

        if not available_products:
            continue

        # Score products for this application
        scored_products = []
        for product in available_products:
            if product.id not in product_analysis["substance_analysis"]:
                continue

            analysis = product_analysis["substance_analysis"][product.id]

            # Calculate selection score
            score = _calculate_selection_score(
                product, analysis, used_substances, resistance_risk, application_number
            )

            # Get dose information
            dose_info = await _get_dose_information(session, product.id, crop.id, target.id)

            scored_products.append((score, product, dose_info))

        # Select best product for this target
        if scored_products:
            scored_products.sort(key=lambda x: x[0], reverse=True)
            _, best_product, dose_info = scored_products[0]

            selected.append((best_product, dose_info))

            # Track used substances for resistance management
            analysis = product_analysis["substance_analysis"][best_product.id]
            used_substances.update(analysis["substances"])

    return selected


def _calculate_selection_score(
        product: Product,
        analysis: Dict[str, Any],
        used_substances: set,
        resistance_risk: ResistanceRisk,
        application_number: int
) -> float:
    """Enhanced product selection score with data quality weighting."""

    score = 0.0

    # Authorization bonus (critical)
    if product.is_currently_authorized:
        score += 40
    else:
        return 0  # Don't select unauthorized products

    # Data quality bonus (high weight for AI decision making)
    data_quality_score = analysis.get("data_quality_score", 0.5)
    score += data_quality_score * 30

    # Substance data availability (critical for resistance management)
    if analysis.get("has_substance_data", False):
        score += 25

        # Resistance management (only if we have substance data)
        substance_overlap = set(analysis["substances"]).intersection(used_substances)
        if not substance_overlap:
            score += 20  # Different substances = excellent for resistance
        else:
            score -= len(substance_overlap) * 8  # Penalize overlaps but not as severely
    else:
        score -= 20  # Significant penalty for missing substance data

    # Function category clarity
    if product.function_category and product.function_category != "Unknown":
        score += 15

    # Chemical family diversity bonus
    chemical_family = analysis.get("chemical_family", "Unknown")
    if chemical_family != "Unknown" and chemical_family not in ["Family_UNK", "Family_"]:
        score += 15

    # Resistance risk adjustment (enhanced)
    product_risk = analysis.get("resistance_risk", "medium")
    if resistance_risk == "high":
        if product_risk == "low":
            score += 25  # Excellent choice for high-risk situations
        elif product_risk == "high":
            score -= 15  # Avoid high-risk products in high-risk situations
    elif resistance_risk == "low" and product_risk != "high":
        score += 10

    # Mode of action diversity bonus
    mode_of_action = analysis.get("mode_of_action", "Unknown")
    if mode_of_action != "Unknown" and not mode_of_action.startswith("MOA_"):
        score += 12  # Bonus for recognized modes of action

    # Application timing and rotation strategy
    if application_number > 0:  # Not first application
        # Prefer different chemical families in rotation
        if application_number % 2 == 0 and "DMI" in chemical_family:
            score += 8
        elif application_number % 2 == 1 and ("QoI" in chemical_family or "Chloroacetamide" in chemical_family):
            score += 8

    # Primary substance bonus (if available)
    if analysis.get("primary_substances"):
        score += 5

    # Recent authorization bonus (prefer newer products)
    if product.first_authorization_date:
        try:
            auth_year = int(str(product.first_authorization_date)[:4])
            if auth_year >= 2020:
                score += 5
            elif auth_year >= 2015:
                score += 3
        except (ValueError, TypeError):
            pass

    return max(score, 0)  # Ensure non-negative score


async def _get_dose_information(
        session: AsyncSession,
        product_id: int,
        crop_id: int,
        target_id: int
) -> Dict[str, Any]:
    """Get dose information for specific product-crop-target combination."""

    query = select(ProductUse).where(
        and_(
            ProductUse.product_id == product_id,
            ProductUse.crop_id == crop_id,
            ProductUse.target_id == target_id,
            ProductUse.is_currently_authorized == True
        )
    ).limit(1)  # Take the first authorized use if multiple exist

    result = await session.execute(query)
    use = result.scalar_one_or_none()

    if use and use.min_dose is not None:
        return {
            "dose": f"{use.min_dose}-{use.max_dose}" if use.max_dose != use.min_dose else str(use.min_dose),
            "unit": use.dose_unit or "per label",
            "harvest_interval": use.harvest_interval_days,
            "max_applications": use.max_applications,
            "compatibility": 0.8  # Default compatibility
        }

    return {
        "dose": "As per label",
        "unit": "",
        "harvest_interval": None,
        "max_applications": None,
        "compatibility": 0.7
    }


async def _develop_resistance_management(
        session: AsyncSession,
        products_matrix: Dict[Tuple[int, int], List[Product]],
        application_schedule: List[ApplicationEvent],
        resistance_risk: ResistanceRisk
) -> ResistanceManagementPlan:
    """Develop comprehensive resistance management plan."""

    # Extract all active substances used
    all_substances = set()
    chemical_families = set()
    mode_of_actions = set()

    for application in application_schedule:
        for product in application.tank_mix:
            all_substances.update(product.active_substances)
            chemical_families.add(_infer_chemical_family(product.active_substances))
            mode_of_actions.add(_infer_mode_of_action(product.active_substances))

    # Create rotation schedule
    rotation_schedule = {}
    for i, application in enumerate(application_schedule):
        substances_used = []
        for product in application.tank_mix:
            substances_used.extend(product.active_substances)
        rotation_schedule[application.timing.timing_name] = " + ".join(substances_used[:2])

    # Assess compliance
    compliance_score = _calculate_resistance_compliance_score(
        all_substances, chemical_families, mode_of_actions, resistance_risk
    )

    # Generate monitoring protocols
    monitoring_protocols = [
        "Monitor efficacy after each application",
        "Record any reduced effectiveness",
        "Scout for resistant pest populations",
        "Document application timing and conditions"
    ]

    if resistance_risk == "high":
        monitoring_protocols.extend([
            "Increase scouting frequency",
            "Test for resistance markers if available",
            "Consult resistance monitoring networks"
        ])

    # Create contingency plans
    contingency_plans = [
        "Identify backup products with different modes of action",
        "Prepare non-chemical control methods",
        "Establish resistance testing protocols",
        "Plan crop rotation strategies"
    ]

    return ResistanceManagementPlan(
        mode_of_action_rotation=list(mode_of_actions),
        chemical_families=list(chemical_families),
        rotation_schedule=rotation_schedule,
        resistance_risk_assessment=resistance_risk,
        monitoring_protocols=monitoring_protocols,
        contingency_plans=contingency_plans,
        compliance_score=compliance_score
    )


async def _perform_economic_analysis(
        products_matrix: Dict[Tuple[int, int], List[Product]],
        application_schedule: List[ApplicationEvent],
        budget_constraint: Optional[BudgetConstraint],
        field_size_ha: Optional[float]
) -> EconomicAnalysis:
    """Perform economic analysis of the management plan."""

    # Since we don't have cost data, provide estimates and framework
    total_applications = len(application_schedule)
    products_per_application = sum(len(app.tank_mix) for app in application_schedule) / max(total_applications, 1)

    # Estimated costs (placeholder values)
    estimated_cost_per_ha = total_applications * 50 + products_per_application * 30
    total_cost = estimated_cost_per_ha * (field_size_ha or 1.0)

    # Budget compliance
    budget_status = "unknown"
    if budget_constraint:
        if budget_constraint == "strict":
            budget_status = "requires_cost_optimization"
        elif budget_constraint == "moderate":
            budget_status = "within_reasonable_range"
        else:  # flexible
            budget_status = "budget_sufficient"

    return EconomicAnalysis(
        total_estimated_cost=f"€{total_cost:.0f}",
        cost_per_hectare=f"€{estimated_cost_per_ha:.0f}/ha",
        cost_per_target_controlled=f"€{estimated_cost_per_ha / max(len(set(t for app in application_schedule for t in app.primary_targets)), 1):.0f}",
        cost_breakdown={
            "Product costs": "60-70%",
            "Application costs": "20-25%",
            "Monitoring & labor": "10-15%"
        },
        budget_compliance=budget_status,
        cost_optimization_suggestions=[
            "Consider generic or biosimilar products",
            "Optimize application timing to reduce trips",
            "Evaluate tank mix opportunities",
            "Implement preventive strategies"
        ],
        roi_indicators=[
            "Yield protection value",
            "Quality premium potential",
            "Resistance prevention savings",
            "Reduced emergency treatments"
        ]
    )


# Enhanced helper functions with real database patterns
def _infer_chemical_family(substances: List[str]) -> str:
    """Enhanced chemical family inference based on real database substance patterns."""
    if not substances:
        return "Unknown"

    substance_lower = " ".join(substances).lower()

    # Triazole fungicides (most common in database)
    if any(term in substance_lower for term in [
        "prothioconazole", "tebuconazole", "difenoconazole", "cyproconazole",
        "epoxiconazole", "metconazole", "propiconazole", "triticonazole",
        "azole", "conazole", "triazole"
    ]):
        return "DMI_Triazoles"

    # Strobilurin fungicides
    elif any(term in substance_lower for term in [
        "azoxystrobin", "pyraclostrobin", "trifloxystrobin", "picoxystrobin",
        "strobin", "strobilurin"
    ]):
        return "QoI_Strobilurins"

    # Chloroacetamide herbicides
    elif any(term in substance_lower for term in [
        "clomazone", "flufenacet", "metolachlor", "acetochlor",
        "chlor", "acetamide"
    ]):
        return "Chloroacetamides"

    # Sulfonylurea herbicides
    elif any(term in substance_lower for term in [
        "nicosulfuron", "rimsulfuron", "sulfuron", "sulfonylurea"
    ]):
        return "Sulfonylureas"

    # Triketone herbicides
    elif "mesotrione" in substance_lower or "triketone" in substance_lower:
        return "Triketones"

    # Pyrethroid insecticides
    elif any(term in substance_lower for term in [
        "cypermethrin", "deltamethrin", "lambda-cyhalothrin", "pyrethrin",
        "methrin", "pyrethroid"
    ]):
        return "Pyrethroids"

    # Carbamate insecticides
    elif any(term in substance_lower for term in ["carbamate", "methomyl"]):
        return "Carbamates"

    # Organophosphate insecticides
    elif any(term in substance_lower for term in [
        "chlorpyrifos", "malathion", "phosphate", "phosmet"
    ]):
        return "Organophosphates"

    # Copper-based fungicides
    elif "copper" in substance_lower or "cuivre" in substance_lower:
        return "Copper_compounds"

    # Phenylpyrrole fungicides
    elif "fludioxonil" in substance_lower:
        return "Phenylpyrroles"

    # Pheromones
    elif "acetate" in substance_lower and "dodeca" in substance_lower:
        return "Pheromones"

    # Glyphosate
    elif "glyphosate" in substance_lower:
        return "Phosphonates"

    # Use first substance as fallback
    else:
        primary_substance = substances[0][:10] if substances[0] else "Unknown"
        return f"Family_{primary_substance.replace(' ', '_')}"


def _infer_mode_of_action(substances: List[str]) -> str:
    """Enhanced mode of action inference based on real database patterns."""
    if not substances:
        return "Unknown"

    substance_lower = " ".join(substances).lower()

    # FRAC codes for fungicides
    if any(term in substance_lower for term in [
        "prothioconazole", "tebuconazole", "difenoconazole", "azole", "conazole"
    ]):
        return "FRAC_3_DMI_Sterol_biosynthesis"

    elif any(term in substance_lower for term in [
        "azoxystrobin", "pyraclostrobin", "strobin"
    ]):
        return "FRAC_11_QoI_Respiration"

    elif "fludioxonil" in substance_lower:
        return "FRAC_12_Phenylpyrrole"

    elif "copper" in substance_lower or "cuivre" in substance_lower:
        return "FRAC_M_Multi_site_contact"

    # HRAC codes for herbicides
    elif "glyphosate" in substance_lower:
        return "HRAC_G_EPSP_synthase"

    elif any(term in substance_lower for term in [
        "nicosulfuron", "sulfuron", "sulfonylurea"
    ]):
        return "HRAC_B_ALS_inhibitor"

    elif "mesotrione" in substance_lower:
        return "HRAC_F2_HPPD_inhibitor"

    elif any(term in substance_lower for term in [
        "clomazone", "flufenacet", "acetamide"
    ]):
        return "HRAC_K3_VLCFA_synthesis"

    # IRAC codes for insecticides
    elif any(term in substance_lower for term in [
        "cypermethrin", "deltamethrin", "pyrethrin", "methrin"
    ]):
        return "IRAC_3A_Sodium_channel"

    elif "carbamate" in substance_lower:
        return "IRAC_1A_Acetylcholinesterase"

    elif "phosphate" in substance_lower and "glyphosate" not in substance_lower:
        return "IRAC_1B_Acetylcholinesterase"

    # Pheromones
    elif "acetate" in substance_lower and "dodeca" in substance_lower:
        return "Pheromone_mating_disruption"

    # Use first substance as fallback
    else:
        primary_substance = substances[0][:8] if substances[0] else "Unknown"
        return f"MOA_{primary_substance.replace(' ', '_')}"


def _calculate_product_data_quality(product: Product, substances: List[str]) -> float:
    """Calculate data quality score for a product (0.0 to 1.0)."""
    score = 0.0

    # Substance data (40% weight)
    if substances:
        score += 0.4
        if len(substances) > 1:
            score += 0.1  # Bonus for multiple substances

    # Function category (20% weight)
    if product.function_category:
        score += 0.2

    # Authorization data (20% weight)
    if product.is_currently_authorized:
        score += 0.2

    # Additional metadata (20% weight)
    if product.authorized_mentions:
        score += 0.1
    if product.first_authorization_date:
        score += 0.1

    return min(score, 1.0)


def _calculate_enhanced_tank_mix_compatibility(analysis1: Dict, analysis2: Dict) -> Dict[str, Any]:
    """Calculate enhanced tank mix compatibility with detailed analysis."""
    compatibility_score = 0.8  # Base compatibility
    compatibility_factors = []
    safety_considerations = []

    # Chemical family compatibility
    if analysis1["chemical_family"] == analysis2["chemical_family"]:
        compatibility_score -= 0.1
        compatibility_factors.append("Same chemical family - potential antagonism")
    else:
        compatibility_score += 0.1
        compatibility_factors.append("Different chemical families - good compatibility")

    # Mode of action diversity (good for resistance management)
    if analysis1["mode_of_action"] != analysis2["mode_of_action"]:
        compatibility_score += 0.1
        compatibility_factors.append("Different modes of action - excellent for resistance management")
    else:
        compatibility_factors.append("Same mode of action - resistance concern")

    # Function category compatibility
    func1 = analysis1.get("function_category", "")
    func2 = analysis2.get("function_category", "")

    if func1 and func2:
        if "Fongicide" in func1 and "Herbicide" in func2:
            compatibility_score += 0.05
            compatibility_factors.append("Fungicide + Herbicide mix - generally compatible")
        elif "Insecticide" in func1 and "Fongicide" in func2:
            compatibility_score += 0.05
            compatibility_factors.append("Insecticide + Fungicide mix - generally compatible")

    # Data quality impact
    data_quality1 = analysis1.get("data_quality_score", 0.5)
    data_quality2 = analysis2.get("data_quality_score", 0.5)
    avg_quality = (data_quality1 + data_quality2) / 2

    if avg_quality < 0.5:
        safety_considerations.append("Limited data quality - verify compatibility with manufacturer")

    # Resistance risk considerations
    risk1 = analysis1.get("resistance_risk", "medium")
    risk2 = analysis2.get("resistance_risk", "medium")

    if risk1 == "high" or risk2 == "high":
        safety_considerations.append("High resistance risk - monitor efficacy closely")

    return {
        "compatibility_score": min(max(compatibility_score, 0.0), 1.0),
        "compatibility_factors": compatibility_factors,
        "safety_considerations": safety_considerations,
        "data_quality_average": avg_quality,
        "resistance_management_benefit": analysis1["mode_of_action"] != analysis2["mode_of_action"]
    }


def _assess_product_resistance_risk(substances: List[str], base_risk: ResistanceRisk) -> str:
    """Enhanced resistance risk assessment based on substance patterns."""
    if not substances:
        return "unknown"

    substance_lower = " ".join(substances).lower()

    # High resistance risk substances
    high_risk_patterns = [
        "glyphosate",  # Well-documented resistance
        "nicosulfuron",  # ALS inhibitor resistance common
        "sulfuron",  # Sulfonylurea resistance
        "triazole",  # Fungicide resistance issues
        "strobin"  # Strobilurin resistance
    ]

    # Medium resistance risk substances
    medium_risk_patterns = [
        "pyrethrin",  # Pyrethroid resistance developing
        "cypermethrin",
        "mesotrione",  # HPPD resistance emerging
        "copper"  # Copper tolerance in some pathogens
    ]

    # Check for high risk patterns
    if any(pattern in substance_lower for pattern in high_risk_patterns):
        return "high"

    # Check for medium risk patterns
    elif any(pattern in substance_lower for pattern in medium_risk_patterns):
        return "medium"

    # Adjust based on base risk
    elif base_risk == "high":
        return "medium"
    elif base_risk == "medium":
        return "medium"
    else:
        return "low"


def _calculate_tank_mix_compatibility(analysis1: Dict, analysis2: Dict) -> float:
    """Calculate tank mix compatibility score."""
    score = 0.8  # Base compatibility

    # Chemical family compatibility
    if analysis1["chemical_family"] == analysis2["chemical_family"]:
        score += 0.1

    # Mode of action diversity (good for resistance)
    if analysis1["mode_of_action"] != analysis2["mode_of_action"]:
        score += 0.1

    return min(score, 1.0)


def _generate_default_windows(season_length: int, target_count: int) -> List[Dict[str, Any]]:
    """Generate default application windows."""
    windows = []

    # Early season
    windows.append({
        "name": "Early Season Prevention",
        "window": f"Days 1-{season_length // 4}",
        "calendar": "Early spring",
        "urgency": "preventive",
        "weather": ["Dry conditions", "Temperature > 10°C"],
        "flexibility": 10
    })

    # Mid season
    if target_count > 2 or season_length > 90:
        windows.append({
            "name": "Mid Season Treatment",
            "window": f"Days {season_length // 3}-{2 * season_length // 3}",
            "calendar": "Late spring/Early summer",
            "urgency": "normal",
            "weather": ["Stable weather", "Wind < 15 km/h"],
            "flexibility": 7
        })

    # Late season
    windows.append({
        "name": "Late Season Protection",
        "window": f"Days {2 * season_length // 3}-{season_length}",
        "calendar": "Mid to late summer",
        "urgency": "curative",
        "weather": ["Pre-harvest conditions", "No rain forecast 24h"],
        "flexibility": 5
    })

    return windows


def _extract_safety_considerations(product: Product) -> List[str]:
    """Extract safety considerations from product data."""
    considerations = []

    if product.authorized_mentions:
        mentions = product.authorized_mentions.lower()
        if "protection" in mentions:
            considerations.append("Personal protective equipment required")
        if "amateur" not in mentions:
            considerations.append("Professional use only")

    # Add general safety notes
    considerations.extend([
        "Follow label instructions",
        "Respect pre-harvest intervals",
        "Observe buffer zones"
    ])

    return considerations


def _calculate_application_cost(tank_mix: List[TankMixProduct], field_size_ha: float) -> str:
    """Calculate estimated application cost."""
    # Placeholder calculation
    product_cost = len(tank_mix) * 25 * field_size_ha
    application_cost = 20 * field_size_ha
    total = product_cost + application_cost

    return f"€{total:.0f} ({len(tank_mix)} products)"


def _determine_equipment_requirements(tank_mix: List[TankMixProduct]) -> List[str]:
    """Determine equipment requirements for tank mix."""
    requirements = ["Standard sprayer"]

    if len(tank_mix) > 1:
        requirements.append("Tank mixing capabilities")
        requirements.append("Agitation system")

    # Check for special requirements based on products
    for product in tank_mix:
        if "granule" in product.function.lower():
            requirements.append("Granule spreader")
        elif "powder" in product.function.lower():
            requirements.append("Dust application equipment")

    return list(set(requirements))


async def _calculate_buffer_zones(
        session: AsyncSession,
        products: List[Tuple[Product, Dict[str, Any]]]
) -> Dict[str, int]:
    """Calculate maximum buffer zone requirements."""

    max_aquatic = 0
    max_arthropod = 0
    max_plant = 0

    for product, _ in products:
        # Get buffer zone data from product uses
        query = select(ProductUse).where(ProductUse.product_id == product.id)
        result = await session.execute(query)
        uses = result.scalars().all()

        for use in uses:
            if use.aquatic_buffer_zone:
                max_aquatic = max(max_aquatic, use.aquatic_buffer_zone)
            if use.arthropod_buffer_zone:
                max_arthropod = max(max_arthropod, use.arthropod_buffer_zone)
            if use.plant_buffer_zone:
                max_plant = max(max_plant, use.plant_buffer_zone)

    return {
        "aquatic": max_aquatic,
        "arthropod": max_arthropod,
        "plant": max_plant
    }


def _generate_pre_application_checks(tank_mix: List[TankMixProduct]) -> List[str]:
    """Generate pre-application checklist."""
    checks = [
        "Verify weather conditions",
        "Check equipment calibration",
        "Confirm product authorization status",
        "Review safety data sheets"
    ]

    if len(tank_mix) > 1:
        checks.extend([
            "Test tank mix compatibility",
            "Prepare products in correct order",
            "Ensure proper agitation"
        ])

    return checks


def _generate_monitoring_plan(targets) -> List[str]:
    """Generate post-application monitoring plan."""
    # Handle both Target objects and strings
    target_names = []
    for target in targets[:3]:
        if hasattr(target, 'target_name'):
            target_names.append(target.target_name)
        else:
            target_names.append(str(target))

    return [
        f"Monitor {', '.join(target_names)} control effectiveness",
        "Document any adverse effects on crops",
        "Check for resistance development signs",
        "Record weather conditions during application",
        "Assess need for follow-up treatments"
    ]


# Additional helper functions for creating responses

def _create_crop_not_found_response(crop_name: str) -> IntegratedManagementResponse:
    """Create response when crop is not found."""

    empty_data_quality = DataQualityMetrics(
        products_with_substances=0,
        products_without_substances=0,
        substance_completeness_rate=0.0,
        dose_data_completeness=0.0,
        buffer_zone_completeness=0.0,
        harvest_interval_completeness=0.0,
        overall_quality_score=0.0,
        data_gaps=["No crop data available"]
    )

    return IntegratedManagementResponse(
        seasonal_strategy=SeasonalStrategy(
            crop_name=crop_name,
            total_targets=0,
            strategy_type="error",
            application_count=0,
            season_length_days=0,
            coverage_percentage=0.0,
            confidence_level="none",
            data_quality=empty_data_quality
        ),
        application_schedule=[],
        resistance_management=ResistanceManagementPlan(
            mode_of_action_rotation=[],
            chemical_families=[],
            rotation_schedule={},
            resistance_risk_assessment="unknown",
            monitoring_protocols=[],
            contingency_plans=[],
            compliance_score=0.0
        ),
        economic_analysis=EconomicAnalysis(
            total_estimated_cost="€0",
            cost_per_hectare="€0/ha",
            cost_per_target_controlled="€0",
            cost_breakdown={},
            budget_compliance="cannot_calculate",
            cost_optimization_suggestions=[],
            roi_indicators=[]
        ),
        risk_assessments=[],
        alternative_scenarios=[],
        implementation_guidelines=[
            f"❌ Crop '{crop_name}' not found in database",
            "🔍 Try alternative crop names or spelling",
            "📞 Consult agricultural advisor for crop identification"
        ],
        monitoring_recommendations=[],
        contingency_protocols=[],
        data_limitations=[
            f"Crop '{crop_name}' not recognized",
            "Cannot proceed without valid crop identification"
        ],
        quality_indicators={"error": "crop_not_found"},
        ai_recommendations=[f"Try alternative names for '{crop_name}'", "Consult crop identification guide"]
    )


def _create_no_targets_response(crop_name: str, target_pests: List[str]) -> IntegratedManagementResponse:
    """Create response when no targets are found."""

    empty_data_quality = DataQualityMetrics(
        products_with_substances=0,
        products_without_substances=0,
        substance_completeness_rate=0.0,
        dose_data_completeness=0.0,
        buffer_zone_completeness=0.0,
        harvest_interval_completeness=0.0,
        overall_quality_score=0.0,
        data_gaps=["No target data available"]
    )

    return IntegratedManagementResponse(
        seasonal_strategy=SeasonalStrategy(
            crop_name=crop_name,
            total_targets=0,
            strategy_type="error",
            application_count=0,
            season_length_days=0,
            coverage_percentage=0.0,
            confidence_level="none",
            data_quality=empty_data_quality
        ),
        application_schedule=[],
        resistance_management=ResistanceManagementPlan(
            mode_of_action_rotation=[],
            chemical_families=[],
            rotation_schedule={},
            resistance_risk_assessment="unknown",
            monitoring_protocols=[],
            contingency_plans=[],
            compliance_score=0.0
        ),
        economic_analysis=EconomicAnalysis(
            total_estimated_cost="€0",
            cost_per_hectare="€0/ha",
            cost_per_target_controlled="€0",
            cost_breakdown={},
            budget_compliance="cannot_calculate",
            cost_optimization_suggestions=[],
            roi_indicators=[]
        ),
        risk_assessments=[],
        alternative_scenarios=[],
        implementation_guidelines=[
            f"❌ No target pests found: {', '.join(target_pests)}",
            "🔍 Try alternative pest names or check spelling",
            "📚 Consult pest identification guides"
        ],
        monitoring_recommendations=[],
        contingency_protocols=[],
        data_limitations=[
            f"Target pests not recognized: {', '.join(target_pests)}",
            "Cannot create management plan without valid targets"
        ],
        quality_indicators={"error": "targets_not_found"},
        ai_recommendations=[f"Try alternative names for: {', '.join(target_pests)}", "Check pest identification guides"]
    )


def _create_no_products_response(crop: Crop, targets: List[Target]) -> IntegratedManagementResponse:
    """Create response when no products are available."""

    empty_data_quality = DataQualityMetrics(
        products_with_substances=0,
        products_without_substances=0,
        substance_completeness_rate=0.0,
        dose_data_completeness=0.0,
        buffer_zone_completeness=0.0,
        harvest_interval_completeness=0.0,
        overall_quality_score=0.0,
        data_gaps=["No authorized products available"]
    )

    return IntegratedManagementResponse(
        seasonal_strategy=SeasonalStrategy(
            crop_name=crop.crop_name,
            total_targets=len(targets),
            strategy_type="no_products_available",
            application_count=0,
            season_length_days=0,
            coverage_percentage=0.0,
            confidence_level="none",
            data_quality=empty_data_quality
        ),
        application_schedule=[],
        resistance_management=ResistanceManagementPlan(
            mode_of_action_rotation=[],
            chemical_families=[],
            rotation_schedule={},
            resistance_risk_assessment="unknown",
            monitoring_protocols=[],
            contingency_plans=[],
            compliance_score=0.0
        ),
        economic_analysis=EconomicAnalysis(
            total_estimated_cost="€0",
            cost_per_hectare="€0/ha",
            cost_per_target_controlled="€0",
            cost_breakdown={},
            budget_compliance="no_products_available",
            cost_optimization_suggestions=[],
            roi_indicators=[]
        ),
        risk_assessments=[],
        alternative_scenarios=[],
        implementation_guidelines=[
            f"❌ No authorized products found for {crop.crop_name}",
            "🔍 Check for alternative target descriptions",
            "📞 Consult agricultural extension service",
            "🌱 Consider non-chemical management options"
        ],
        monitoring_recommendations=[
            "Implement cultural control practices",
            "Monitor pest pressure levels",
            "Scout for beneficial organisms"
        ],
        contingency_protocols=[
            "Prepare non-chemical alternatives",
            "Consider crop rotation options",
            "Evaluate resistant varieties"
        ],
        data_limitations=[
            f"No authorized chemical products for {crop.crop_name} against specified targets",
            "Limited to non-chemical management strategies"
        ],
        quality_indicators={"error": "no_products_available"},
        ai_recommendations=["Consider biological control options", "Implement cultural practices",
                            "Consult extension services"]
    )


# Additional utility functions

def _determine_strategy_type(
        application_schedule: List[ApplicationEvent],
        resistance_plan: ResistanceManagementPlan
) -> str:
    """Determine the overall strategy type."""

    if not application_schedule:
        return "no_strategy"

    if len(resistance_plan.chemical_families) >= 3:
        return "comprehensive_rotation"
    elif len(resistance_plan.chemical_families) >= 2:
        return "basic_rotation"
    elif len(application_schedule) > 1:
        return "multi_application"
    else:
        return "single_treatment"


def _calculate_coverage_percentage(
        targets: List[Target],
        products_matrix: Dict[Tuple[int, int], List[Product]]
) -> float:
    """Calculate percentage of targets with available products."""

    targets_with_products = sum(
        1 for products in products_matrix.values() if products
    )

    return (targets_with_products / max(len(targets), 1)) * 100


def _assess_plan_confidence(
        products_matrix: Dict[Tuple[int, int], List[Product]],
        application_schedule: List[ApplicationEvent]
) -> str:
    """Assess confidence level in the management plan."""

    total_products = sum(len(products) for products in products_matrix.values())

    if not application_schedule:
        return "none"
    elif total_products >= 10 and len(application_schedule) >= 2:
        return "high"
    elif total_products >= 5:
        return "medium"
    else:
        return "low"


def _calculate_resistance_compliance_score(
        substances: set,
        chemical_families: set,
        mode_of_actions: set,
        resistance_risk: ResistanceRisk
) -> float:
    """Calculate resistance management compliance score."""

    score = 0.0

    # Diversity bonus
    if len(chemical_families) >= 3:
        score += 0.4
    elif len(chemical_families) >= 2:
        score += 0.3

    if len(mode_of_actions) >= 3:
        score += 0.3
    elif len(mode_of_actions) >= 2:
        score += 0.2

    # Risk-appropriate management
    if resistance_risk == "high" and len(chemical_families) >= 2:
        score += 0.3
    elif resistance_risk == "medium" and len(chemical_families) >= 1:
        score += 0.2
    else:
        score += 0.1

    return min(score, 1.0)


async def _assess_implementation_risks(
        session: AsyncSession,
        crop: Crop,
        targets: List[Target],
        application_schedule: List[ApplicationEvent],
        resistance_plan: ResistanceManagementPlan
) -> List[Dict[str, Any]]:
    """Assess risks in plan implementation."""

    risks = []

    # Weather dependency risk
    risks.append({
        "risk_type": "weather_dependency",
        "severity": "medium",
        "description": "Application timing dependent on weather conditions",
        "mitigation": ["Plan flexible application windows", "Monitor weather forecasts"]
    })

    # Resistance development risk
    if resistance_plan.resistance_risk_assessment == "high":
        risks.append({
            "risk_type": "resistance_development",
            "severity": "high",
            "description": "High risk of resistance development",
            "mitigation": ["Strict rotation protocols", "Regular efficacy monitoring"]
        })

    # Product availability risk
    risks.append({
        "risk_type": "product_availability",
        "severity": "low",
        "description": "Products may become unavailable during season",
        "mitigation": ["Identify backup products", "Purchase early in season"]
    })

    return risks


async def _generate_alternative_scenarios(
        session: AsyncSession,
        crop: Crop,
        targets: List[Target],
        products_matrix: Dict[Tuple[int, int], List[Product]],
        budget_constraint: Optional[BudgetConstraint],
        organic_preference: bool
) -> List[Dict[str, Any]]:
    """Generate alternative management scenarios."""

    scenarios = []

    # Reduced cost scenario
    if budget_constraint in ["strict", "moderate"]:
        scenarios.append({
            "scenario_name": "Budget-Optimized Plan",
            "description": "Minimum effective treatments focusing on key targets",
            "modifications": [
                "Reduce application frequency",
                "Focus on most critical targets",
                "Use generic products where available"
            ],
            "cost_impact": "30-50% reduction",
            "effectiveness_impact": "10-20% reduced coverage"
        })

    # Organic scenario
    if not organic_preference:
        scenarios.append({
            "scenario_name": "Organic Management",
            "description": "Biological and cultural control emphasis",
            "modifications": [
                "Replace synthetic products with biologicals",
                "Increase monitoring frequency",
                "Add cultural control practices"
            ],
            "cost_impact": "Variable",
            "effectiveness_impact": "Requires more intensive management"
        })

    # Intensive scenario
    scenarios.append({
        "scenario_name": "Intensive Protection",
        "description": "Maximum protection with preventive focus",
        "modifications": [
            "Add preventive applications",
            "Include minor targets",
            "Use premium products"
        ],
        "cost_impact": "50-100% increase",
        "effectiveness_impact": "Maximum crop protection"
    })

    return scenarios


def _generate_implementation_guidelines(
        seasonal_strategy: SeasonalStrategy,
        application_schedule: List[ApplicationEvent],
        resistance_plan: ResistanceManagementPlan,
        economic_analysis: EconomicAnalysis
) -> List[str]:
    """Generate implementation guidelines."""

    guidelines = []

    # Strategy-specific guidelines
    if seasonal_strategy.strategy_type == "comprehensive_rotation":
        guidelines.extend([
            "🔄 Implement strict rotation protocol",
            "📋 Maintain detailed application records",
            "🎯 Never repeat same mode of action consecutively"
        ])
    elif seasonal_strategy.strategy_type == "basic_rotation":
        guidelines.extend([
            "🔄 Alternate between available chemical families",
            "📊 Monitor effectiveness of each application"
        ])

    # Application guidelines
    guidelines.extend([
        "📅 Follow application timing windows strictly",
        "🌤️ Check weather conditions before each application",
        "⚖️ Verify product authorization before use",
        "🥽 Use appropriate personal protective equipment"
    ])

    # Budget considerations
    if economic_analysis.budget_compliance == "requires_cost_optimization":
        guidelines.extend([
            "💰 Consider generic alternatives where available",
            "🚜 Optimize application trips to reduce costs"
        ])

    return guidelines


def _create_monitoring_recommendations(
        crop: Crop,
        targets: List[Target],
        application_schedule: List[ApplicationEvent],
        resistance_plan: ResistanceManagementPlan
) -> List[str]:
    """Create monitoring recommendations."""

    recommendations = [
        f"🔍 Scout {crop.crop_name} weekly for pest presence",
        "📊 Record pest pressure levels before and after applications",
        "🌱 Monitor crop health and phytotoxicity symptoms",
        "🌍 Document environmental conditions during applications"
    ]

    # Target-specific monitoring
    for target in targets[:3]:  # Limit to top 3 targets
        recommendations.append(f"🎯 Monitor {target.target_name} development stages")

    # Resistance monitoring
    if resistance_plan.resistance_risk_assessment == "high":
        recommendations.extend([
            "⚠️ Intensify resistance monitoring protocols",
            "🧪 Consider resistance testing if available",
            "📈 Track efficacy trends over multiple seasons"
        ])

    return recommendations


def _develop_contingency_protocols(
        crop: Crop,
        targets: List[Target],
        products_matrix: Dict[Tuple[int, int], List[Product]],
        resistance_plan: ResistanceManagementPlan
) -> List[str]:
    """Develop contingency protocols."""

    protocols = [
        "🚨 Emergency Treatment Protocol:",
        "  - Identify outbreak threshold levels",
        "  - Prepare rapid response products",
        "  - Maintain emergency contact list",
        "",
        "🔄 Product Failure Protocol:",
        "  - Switch to alternative mode of action",
        "  - Increase application frequency if authorized",
        "  - Consult agricultural advisor immediately",
        "",
        "🌾 Non-chemical Backup Options:",
        "  - Cultural control practices",
        "  - Biological control agents",
        "  - Physical barriers or traps"
    ]

    # Resistance contingency
    if resistance_plan.resistance_risk_assessment in ["medium", "high"]:
        protocols.extend([
            "",
            "⚠️ Resistance Management Contingency:",
            "  - Cease use of suspected failed products",
            "  - Implement refuge areas if applicable",
            "  - Contact resistance monitoring networks"
        ])

    return protocols


async def _calculate_data_quality_metrics(
        session: AsyncSession,
        products_matrix: Dict[Tuple[int, int], List[Product]],
        application_schedule: List[ApplicationEvent]
) -> DataQualityMetrics:
    """Calculate comprehensive data quality metrics."""

    all_products = []
    for products in products_matrix.values():
        all_products.extend(products)

    unique_products = {p.id: p for p in all_products}.values()
    total_products = len(unique_products)

    if total_products == 0:
        return DataQualityMetrics(
            products_with_substances=0,
            products_without_substances=0,
            substance_completeness_rate=0.0,
            dose_data_completeness=0.0,
            buffer_zone_completeness=0.0,
            harvest_interval_completeness=0.0,
            overall_quality_score=0.0,
            data_gaps=["No products available"]
        )

    products_with_substances = sum(
        1 for p in unique_products
        if p.substances and any(ps.substance for ps in p.substances)
    )
    products_without_substances = total_products - products_with_substances

    substance_completeness = products_with_substances / total_products if total_products > 0 else 0.0
    overall_quality = substance_completeness * 0.6 + 0.4  # Base quality

    data_gaps = []
    if substance_completeness < 0.5:
        data_gaps.append(f"Low substance coverage: {substance_completeness:.1%}")

    return DataQualityMetrics(
        products_with_substances=products_with_substances,
        products_without_substances=products_without_substances,
        substance_completeness_rate=substance_completeness,
        dose_data_completeness=0.8,  # Simplified for now
        buffer_zone_completeness=0.6,
        harvest_interval_completeness=0.5,
        overall_quality_score=overall_quality,
        data_gaps=data_gaps
    )


def _generate_quality_indicators(
        data_quality: DataQualityMetrics,
        product_analysis: Dict[str, Any],
        resistance_plan: ResistanceManagementPlan,
        economic_analysis: EconomicAnalysis
) -> Dict[str, Any]:
    """Generate quality indicators for AI decision making."""

    return {
        "data_reliability": {
            "score": data_quality.overall_quality_score,
            "level": "high" if data_quality.overall_quality_score >= 0.8 else "medium" if data_quality.overall_quality_score >= 0.6 else "low",
            "substance_coverage": data_quality.substance_completeness_rate
        },
        "recommendation_confidence": {
            "chemical_diversity": product_analysis.get("chemical_families", 0),
            "resistance_score": resistance_plan.compliance_score,
            "overall_confidence": "high" if product_analysis.get("chemical_families", 0) >= 2 else "low"
        },
        "ai_decision_factors": {
            "prefer_products_with_substances": data_quality.products_with_substances > 0,
            "resistance_rotation_possible": product_analysis.get("chemical_families", 0) >= 2,
            "data_quality_sufficient": data_quality.overall_quality_score >= 0.5
        }
    }


def _generate_ai_recommendations(
        seasonal_strategy: SeasonalStrategy,
        product_analysis: Dict[str, Any],
        data_quality: DataQualityMetrics,
        resistance_plan: ResistanceManagementPlan
) -> List[str]:
    """Generate AI-specific recommendations."""

    recommendations = []

    if data_quality.overall_quality_score < 0.6:
        recommendations.append("⚠️ Limited data quality - prioritize products with complete information")

    chemical_families = product_analysis.get("chemical_families", 0)
    if chemical_families >= 3:
        recommendations.append("✅ Excellent chemical diversity - implement full rotation")
    elif chemical_families >= 2:
        recommendations.append("⚠️ Moderate diversity - strict alternation required")
    else:
        recommendations.append("🚨 Limited diversity - high resistance risk")

    if data_quality.products_without_substances > data_quality.products_with_substances:
        recommendations.append("🧪 Focus on products with substance data for better analysis")

    return recommendations


def _identify_data_limitations(
        crop: Crop,
        targets: List[Target],
        products_matrix: Dict[Tuple[int, int], List[Product]],
        target_translation_notes: List[str]
) -> List[str]:
    """Identify limitations in the data and analysis."""

    limitations = [
        "💰 Cost data estimates only",
        "🧪 Chemical data inferred",
        "📍 Regional variations not considered"
    ]

    if target_translation_notes:
        limitations.extend(target_translation_notes)

    targets_without_products = sum(
        1 for products in products_matrix.values() if not products
    )

    if targets_without_products > 0:
        limitations.append(f"🎯 {targets_without_products} targets lack products")

    limitations.extend([
        "⚖️ Regulatory status may change",
        "📋 Always verify current labels"
    ])

    return limitations
