import logging


def setup_logger():
    # host = '**************'

    logger = logging.getLogger('coachello-server')
    logger.setLevel(logging.DEBUG)

    # hipster_format = logging.Formatter(
    #     '%(asctime)s %(levelname)10s %(thread)d --- [%(name)10s] %(module)20s.%(funcName)s - %(message)s')
    #
    hipster_format = logging.Formatter(
        '%(levelname)8s %(thread)d - %(module)20s.%(funcName)s - %(message)s')
    ch = logging.StreamHandler()
    ch.setLevel(logging.DEBUG)
    ch.setFormatter(hipster_format)
    logger.addHandler(ch)

    # Production only
    # verbose = CREDENTIALS.LOG_LEVEL
    # debugging = CREDENTIALS.DEBUG_LOGS

    # if debugging and int(debugging):
    #     ls = logstash.LogstashHandler(host, 5000, version=1)
    #     if verbose:
    #         ls.setLevel(logging.DEBUG)
    #     else:
    #         ls.setLevel(logging.WARN)
    #     logger.addHandler(ls)

    return logger


logger = setup_logger()
