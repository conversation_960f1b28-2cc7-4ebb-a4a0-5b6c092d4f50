from schemas import BaseModel


class WeatherAPIException(Exception):
    """Exception raised for weather API errors"""
    pass


class ForecastDay(BaseModel):
    """Daily weather forecast data relevant for agriculture"""
    date: str
    max_temp_c: float
    min_temp_c: float
    avg_temp_c: float
    max_wind_kph: float
    total_precip_mm: float
    avg_humidity: float
    chance_of_rain: int
    uv: float
    condition: str
