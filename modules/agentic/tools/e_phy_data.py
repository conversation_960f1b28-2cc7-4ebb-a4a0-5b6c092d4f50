import unicodedata
import re
from typing import Dict, Optional, Any, List

from sqlalchemy import or_, func, text, distinct, select
from sqlalchemy.ext.asyncio import AsyncSession

from models.e_phy import (
    Product, ActiveSubstance, ProductSubstance, Crop, Target, ProductUse,
    ProductHazard, UsageCondition, ProductComposition, ParallelTradePermit
)
from .e_phy_helper import EPhyToolHelper


# Enhanced crop synonyms mapping for better search
ENHANCED_CROP_SYNONYMS = {
    # Wheat variations
    "wheat": ["blé"],
    "triticum": ["blé"], 
    "triticum aestivum": ["blé"],
    "winter wheat": ["blé"],
    "spring wheat": ["blé"],
    
    # Oilseed rape / Canola / Colza variations  
    "colza": ["crucifères oléagineuses"],
    "canola": ["crucifères oléagineuses"],
    "rape": ["crucifères oléagineuses"],
    "oilseed rape": ["crucifères oléagineuses"],
    "rapeseed": ["crucifères oléagineuses"],
    "brassica napus": ["crucifères oléagineuses"],
    "winter oilseed rape": ["crucifères oléagineuses"],
    "colza hiver": ["crucifères oléagineuses"],
    "colza d'hiver": ["crucifères oléagineuses"],
    "colza dhiver": ["crucifères oléagineuses"],
    
    # Add French to English mappings too
    "ble": ["wheat", "triticum", "triticum aestivum"],
    "cruciferes oleagineuses": ["colza", "canola", "rape", "oilseed rape", "rapeseed", "brassica napus"],
}


def normalize_accents(term: str) -> str:
    """Method 1: Normalize accents and special characters"""
    # Convert to lowercase
    term = term.lower().strip()
    
    # Remove accents and special characters
    replacements = {
        'é': 'e', 'è': 'e', 'ê': 'e', 'ë': 'e',
        'à': 'a', 'â': 'a', 'ä': 'a', 'á': 'a',
        'ù': 'u', 'û': 'u', 'ü': 'u', 'ú': 'u',
        'î': 'i', 'ï': 'i', 'í': 'i',
        'ô': 'o', 'ö': 'o', 'ó': 'o',
        'ç': 'c',
        "'": '', '"': '', ''': '', ''': '', '-': ' ', '_': ' '
    }
    
    for old, new in replacements.items():
        term = term.replace(old, new)
    
    # Remove extra spaces
    term = re.sub(r'\s+', ' ', term).strip()
    
    return term


def is_scientific_name(crop_query: str) -> bool:
    """Method 2: Check if query is a scientific name (Genus species format)"""
    # Scientific names typically have:
    # - Two words
    # - First word capitalized (Genus)
    # - Second word lowercase (species)
    # - Sometimes followed by variety/subspecies
    
    words = crop_query.strip().split()
    if len(words) < 2:
        return False
    
    # Check if first word starts with capital and second is lowercase
    genus = words[0]
    species = words[1]
    
    # Basic pattern: Genus species
    if (genus[0].isupper() and 
        genus[1:].islower() and 
        species.islower() and
        len(genus) >= 3 and
        len(species) >= 3):
        return True
    
    return False


def extract_scientific_parts(crop_query: str) -> tuple:
    """Method 2 helper: Extract genus and species from scientific name"""
    words = crop_query.strip().split()
    if len(words) >= 2:
        genus = words[0].lower()
        species = words[1].lower()
        return genus, species
    return crop_query.lower(), ""


def smart_word_split(crop_query: str) -> List[str]:
    """Method 3: Intelligently split compound terms"""
    # Normalize first
    normalized = normalize_accents(crop_query)
    
    # Split on various delimiters
    words = re.split(r'[\s\-_/,]+', normalized)
    
    # Remove empty strings and very short words
    significant_words = []
    for word in words:
        word = word.strip()
        if len(word) >= 2:  # Keep words with 2+ characters
            significant_words.append(word)
    
    return significant_words


def is_significant_word(word: str) -> bool:
    """Method 3 helper: Check if word is significant for search"""
    # Filter out common stop words and articles
    stop_words = {
        'the', 'of', 'and', 'or', 'in', 'on', 'at', 'to', 'for', 'with',
        'de', 'du', 'des', 'le', 'la', 'les', 'un', 'une', 'et', 'ou',
        'tree', 'plant', 'crop'
    }
    
    word_lower = word.lower()
    
    # Keep if:
    # - Not a stop word
    # - At least 3 characters (except some important 2-char words)
    # - Not purely numeric
    
    if word_lower in stop_words:
        return False
    
    if len(word) < 2:
        return False
        
    if word.isdigit():
        return False
    
    return True


def get_morphological_variants(word: str) -> List[str]:
    """Method 3 helper: Generate morphological variants of a word"""
    variants = []
    word_lower = word.lower()
    
    # Plural/singular variations
    if word_lower.endswith('s') and len(word_lower) > 3:
        variants.append(word_lower[:-1])  # Remove 's'
    else:
        variants.append(word_lower + 's')  # Add 's'
    
    # Common endings variations
    endings_map = {
        'ies': 'y',      # berries -> berry
        'ves': 'f',      # leaves -> leaf
        'ing': '',       # something -> some
        'ed': '',        # cultivated -> cultivat
        'er': '',        # farmer -> farm
        'eur': '',       # cultivateur -> cultivat
        'tion': '',      # cultivation -> cultivat
        'sion': '',      # extension -> extens
    }
    
    for ending, replacement in endings_map.items():
        if word_lower.endswith(ending):
            base = word_lower[:-len(ending)] + replacement
            if len(base) >= 3:
                variants.append(base)
    
    return variants


def apply_translation_patterns(crop_query: str) -> List[str]:
    """Method 4: Apply core translation patterns"""
    translations = []
    query_lower = normalize_accents(crop_query).lower()
    
    # Core translation patterns (most common crops only)
    core_patterns = {
        'wheat': ['blé', 'froment'],
        'corn': ['maïs'],
        'maize': ['maïs'],
        'barley': ['orge'],
        'oats': ['avoine'],
        'rye': ['seigle'],
        'rice': ['riz'],
        'potato': ['pomme de terre', 'patate'],
        'tomato': ['tomate'],
        'sunflower': ['tournesol'],
        'soybean': ['soja'],
        'soy': ['soja'],
        'canola': ['colza', 'crucifères oléagineuses'],
        'rapeseed': ['colza', 'crucifères oléagineuses'],
        'rape': ['colza'],
        'oilseed': ['oléagineuses'],
        'apple': ['pomme', 'pommier'],
        'grape': ['raisin', 'vigne'],
        'carrot': ['carotte'],
        'onion': ['oignon'],
        
        # Scientific genus patterns
        'triticum': ['blé'],
        'zea': ['maïs'],
        'hordeum': ['orge'],
        'brassica': ['crucifères'],
        'solanum': ['pomme de terre', 'tomate'],
        'helianthus': ['tournesol'],
        'vitis': ['vigne'],
        'malus': ['pommier'],
        
        # Reverse mappings (French to English)
        'blé': ['wheat', 'triticum'],
        'maïs': ['corn', 'maize', 'zea'],
        'orge': ['barley', 'hordeum'],
        'colza': ['canola', 'rapeseed', 'brassica'],
        'crucifères': ['brassica', 'canola', 'rape'],
        'oléagineuses': ['oilseed'],
        'tournesol': ['sunflower', 'helianthus'],
        'soja': ['soy', 'soybean'],
        'pomme': ['apple'],
        'vigne': ['grape', 'vine'],
    }
    
    # Direct matches
    if query_lower in core_patterns:
        translations.extend(core_patterns[query_lower])
    
    # Partial matches for compound terms
    words = smart_word_split(query_lower)
    for word in words:
        if word in core_patterns:
            translations.extend(core_patterns[word])
    
    # Special compound handling
    if 'winter' in query_lower and any(w in query_lower for w in ['wheat', 'oilseed', 'rape']):
        translations.extend(['hiver', "d'hiver"])
    
    if 'oilseed rape' in query_lower or 'oil seed rape' in query_lower:
        translations.extend(['crucifères oléagineuses', 'colza hiver'])
    
    return translations


def generate_fuzzy_variants(crop_query: str) -> List[str]:
    """Method 5: Generate fuzzy matching variants"""
    variants = []
    normalized = normalize_accents(crop_query)
    
    # Common spelling variations
    spelling_variants = {
        'oilseed rape': ['oil seed rape', 'oil-seed rape', 'oilseedrape'],
        'oil seed rape': ['oilseed rape', 'oil-seed rape'],
        'winter wheat': ['winter-wheat', 'winterwheat'],
        'spring wheat': ['spring-wheat', 'springwheat'],
        'sweet corn': ['sweet-corn', 'sweetcorn'],
        'potato': ['potatoe'],  # Common misspelling
        'tomato': ['tomatoe'],  # Common misspelling
    }
    
    query_lower = normalized.lower()
    if query_lower in spelling_variants:
        variants.extend(spelling_variants[query_lower])
    
    # Character substitutions for fuzzy matching
    substitutions = {
        'ph': 'f',
        'ck': 'k',
        'qu': 'k',
        'x': 'ks',
    }
    
    fuzzy_variant = query_lower
    for old, new in substitutions.items():
        if old in fuzzy_variant:
            variants.append(fuzzy_variant.replace(old, new))
    
    # Remove/add common prefixes and suffixes
    prefixes_to_try = ['', 'wild ', 'field ', 'garden ', 'sweet ', 'dwarf ']
    suffixes_to_try = ['', ' tree', ' plant', ' crop', 's']
    
    base_words = smart_word_split(normalized)
    if len(base_words) == 1:
        base = base_words[0]
        for prefix in prefixes_to_try[:2]:  # Limit to avoid too many variants
            for suffix in suffixes_to_try[:2]:
                variant = f"{prefix}{base}{suffix}".strip()
                if variant != query_lower and len(variant) >= 3:
                    variants.append(variant)
    
    return variants


def generate_intelligent_search_terms(crop_query: str) -> List[str]:
    """Generate intelligent search terms using 5 dynamic methods"""
    if not crop_query or not crop_query.strip():
        return []
    
    terms = set()
    
    # Method 1: Original query variations with accent normalization
    terms.add(crop_query.lower().strip())
    normalized = normalize_accents(crop_query)
    terms.add(normalized)
    
    # Method 2: Scientific name handling
    if is_scientific_name(crop_query):
        genus, species = extract_scientific_parts(crop_query)
        terms.add(genus)
        if species:
            terms.add(f"{genus} {species}")
            terms.add(species)
    
    # Method 3: Compound term decomposition
    words = smart_word_split(crop_query)
    for word in words:
        if is_significant_word(word):
            terms.add(word)
            # Add morphological variants
            variants = get_morphological_variants(word)
            terms.update(variants)
    
    # Add word combinations for compound terms
    if len(words) > 1:
        # Try first + last word
        if len(words) >= 2:
            terms.add(f"{words[0]} {words[-1]}")
        
        # Try consecutive pairs
        for i in range(len(words) - 1):
            if is_significant_word(words[i]) and is_significant_word(words[i + 1]):
                terms.add(f"{words[i]} {words[i + 1]}")
    
    # Method 4: Pattern-based translation
    translations = apply_translation_patterns(crop_query)
    terms.update(translations)
    
    # Method 5: Fuzzy matching variants
    fuzzy_variants = generate_fuzzy_variants(crop_query)
    terms.update(fuzzy_variants)
    
    # Clean up and return
    # Remove empty strings and very short terms
    cleaned_terms = []
    for term in terms:
        if term and len(term.strip()) >= 2:
            cleaned_terms.append(term.strip())
    
    # Remove duplicates while preserving some order (prioritize original query)
    seen = set()
    final_terms = []
    
    # Prioritize original query first
    original_normalized = normalize_accents(crop_query).lower()
    if original_normalized not in seen:
        final_terms.append(original_normalized)
        seen.add(original_normalized)
    
    # Add rest of terms
    for term in cleaned_terms:
        if term not in seen:
            final_terms.append(term)
            seen.add(term)
    
    return final_terms


# Backward compatibility alias
def get_enhanced_search_terms(crop_query: str) -> List[str]:
    """Backward compatibility wrapper for existing code"""
    return generate_intelligent_search_terms(crop_query)


class EPhyTool(EPhyToolHelper):
    """
    Agricultural product knowledge tool for AI agents.

    Provides comprehensive access to agricultural product data including pesticides,
    fertilizers, and biological products with context-aware recommendations.
    """

    @classmethod
    async def search_agricultural_products(cls,
                                           session: AsyncSession,
                                           query: str,
                                           include_withdrawn: bool = False,
                                           product_type: Optional[str] = None,
                                           function_category: Optional[str] = None) -> Dict[str, Any]:
        """
        Search for agricultural products with intelligent fuzzy matching.

        Performs a comprehensive search across product names, registration numbers,
        active substances, and function categories using PostgreSQL's full-text search capabilities.
        Results are ranked by relevance with authorized products prioritized.

        Args:
            query: Product name, registration number, or active substance (partial match supported)
            include_withdrawn: Whether to include withdrawn products in results
            product_type: Filter by product type (e.g., "PPP", "MFSC")
            function_category: Filter by function category (e.g., "Herbicide", "Fongicide")

        Returns:
            Ranked list of matching products with their key information and authorization status
        """
        # Normalize query
        query = query.strip().lower()

        products = []

        # Handle empty or short queries
        if not query or len(query) < 3:
            # Return some products when query is empty or too short
            stmt = select(Product)

            if not include_withdrawn:
                stmt = stmt.where(Product.is_currently_authorized == True)

            if product_type:
                stmt = stmt.where(
                    func.lower(Product.product_type).ilike(f'%{product_type.lower()}%')
                )

            if function_category:
                stmt = stmt.where(
                    func.lower(Product.function_category).ilike(f'%{function_category.lower()}%')
                )

            stmt = stmt.limit(50)
            result = await session.execute(stmt)
            products = result.scalars().all()
        else:  # Query has at least 3 characters
            # Try exact registration number match first
            stmt = select(Product).where(
                func.lower(Product.registration_number) == query
            )
            result = await session.execute(stmt)
            exact_reg_match = result.scalar_one_or_none()

            if exact_reg_match:
                # If exact registration number match, prioritize that
                products = [exact_reg_match]
            else:
                # Otherwise, use full-text search or LIKE patterns
                tsquery = ' & '.join(query.split())

                # Use PostgreSQL full-text search if available
                if hasattr(Product, 'search_vector'):
                    # Use text() for raw SQL with proper parameter binding to avoid SQL injection
                    search_stmt = (
                        select(Product)
                        .where(text("search_vector @@ to_tsquery('french', :tsquery)").bindparams(tsquery=tsquery))
                    )
                else:
                    # Fall back to simpler LIKE search
                    search_stmt = select(Product).where(
                        or_(
                            Product.product_name.ilike(f'%{query}%'),
                            Product.registration_number.ilike(f'%{query}%'),
                            Product.holder.ilike(f'%{query}%'),
                            Product.function_category.ilike(f'%{query}%')
                        )
                    )

                # Apply filters to search statement before union
                if not include_withdrawn:
                    search_stmt = search_stmt.where(Product.is_currently_authorized == True)

                if product_type:
                    search_stmt = search_stmt.where(
                        func.lower(Product.product_type).ilike(f'%{product_type.lower()}%')
                    )

                if function_category:
                    search_stmt = search_stmt.where(
                        func.lower(Product.function_category).ilike(f'%{function_category.lower()}%')
                    )

                # Also search by active substance - get product IDs
                substance_search_stmt = (
                    select(Product.id)
                    .join(ProductSubstance, ProductSubstance.product_id == Product.id)
                    .join(ActiveSubstance, ActiveSubstance.id == ProductSubstance.substance_id)
                    .where(func.lower(ActiveSubstance.name).ilike(f'%{query}%'))
                )

                # Apply same filters to substance search
                if not include_withdrawn:
                    substance_search_stmt = substance_search_stmt.where(Product.is_currently_authorized == True)

                if product_type:
                    substance_search_stmt = substance_search_stmt.where(
                        func.lower(Product.product_type).ilike(f'%{product_type.lower()}%')
                    )

                if function_category:
                    substance_search_stmt = substance_search_stmt.where(
                        func.lower(Product.function_category).ilike(f'%{function_category.lower()}%')
                    )

                # Get products directly via name/text search
                result = await session.execute(search_stmt.limit(25))
                direct_products = result.scalars().all()

                # Get products by substance search - get product IDs first, then products
                substance_result = await session.execute(substance_search_stmt.limit(25))
                substance_product_ids = [row[0] for row in substance_result.fetchall()]

                # Get actual Product objects for those IDs
                substance_products = []
                if substance_product_ids:
                    products_by_substance_stmt = select(Product).where(Product.id.in_(substance_product_ids))
                    substance_products_result = await session.execute(products_by_substance_stmt)
                    substance_products = substance_products_result.scalars().all()

                # Combine results, removing duplicates based on ID
                seen_ids = set()
                products = []

                for product in direct_products:
                    if product.id not in seen_ids:
                        products.append(product)
                        seen_ids.add(product.id)

                for product in substance_products:
                    if product.id not in seen_ids:
                        products.append(product)
                        seen_ids.add(product.id)

        # Format results
        results = []
        for product in products:
            # Get active substances using SQLAlchemy 2.0 style
            substances_stmt = (
                select(
                    ActiveSubstance.name,
                    ProductSubstance.concentration,
                    ProductSubstance.concentration_unit
                )
                .join(ProductSubstance, ProductSubstance.substance_id == ActiveSubstance.id)
                .where(ProductSubstance.product_id == product.id)
            )

            substances_result = await session.execute(substances_stmt)
            substances = substances_result.all()

            substance_list = [
                {
                    "name": s.name,
                    "concentration": s.concentration,
                    "unit": s.concentration_unit
                }
                for s in substances
            ]

            results.append({
                "registration_number": product.registration_number,
                "product_name": product.product_name,
                "product_type": product.product_type,
                "authorization_status": product.authorization_status,
                "is_currently_authorized": product.is_currently_authorized,
                "function_category": product.function_category,
                "active_substances": substance_list,
                "formulation_type": product.formulation_type,
                "product_summary": product.product_summary
            })

        # Add citation information for the search results
        return {
            "results": results,
            "_citation": {
                "source": "E-Phy",
                "url": "https://ephy.anses.fr"
            }
        }

    @classmethod
    async def get_product_details(cls, session: AsyncSession, product_identifier: str) -> Dict[str, Any]:
        """
        Retrieve comprehensive details about an agricultural product with enriched context.

        Provides complete information about a product including its active substances,
        compositions, authorized uses, safety information, and usage conditions.
        Includes intelligent summaries and compatibility insights for practical application.

        Args:
            product_identifier: Product registration number or name (partial match supported)

        Returns:
            Detailed product information with context-aware insights for agricultural applications
        """
        try:
            # FIXED: Validate input with type safety
            if not product_identifier or not str(product_identifier).strip():
                return {"error": "Product identifier cannot be empty"}

            # FIXED: Ensure product_identifier is a string and normalize it
            product_identifier = str(product_identifier).strip()
            if not product_identifier:
                return {"error": "Product identifier cannot be empty"}

            # Find product by registration number or name
            product = await cls._find_product(session, product_identifier)

            if not product:
                return {"error": f"Product not found: {product_identifier}"}

            # Get active substances with concentrations using SQLAlchemy 2.0 style
            substances_query = select(
                ActiveSubstance,
                ProductSubstance.concentration,
                ProductSubstance.concentration_unit,
                ProductSubstance.primary_substance
            ).join(
                ProductSubstance, ProductSubstance.substance_id == ActiveSubstance.id
            ).filter(
                ProductSubstance.product_id == product.id
            ).order_by(ProductSubstance.primary_substance.desc(), ActiveSubstance.name)

            substances_result = await session.execute(substances_query)
            substances = substances_result.all()

            # Get compositions for MFSC products
            compositions_query = select(ProductComposition).filter(
                ProductComposition.product_id == product.id
            ).order_by(ProductComposition.component_name)

            compositions_result = await session.execute(compositions_query)
            compositions = compositions_result.scalars().all()

            # Get authorized uses with crop and target information
            uses_query = select(
                ProductUse,
                Crop.crop_name,
                Crop.crop_category,
                Target.target_name,
                Target.target_type
            ).outerjoin(
                Crop, Crop.id == ProductUse.crop_id
            ).outerjoin(
                Target, Target.id == ProductUse.target_id
            ).filter(
                ProductUse.product_id == product.id
            ).order_by(Crop.crop_name, Target.target_name)

            uses_result = await session.execute(uses_query)
            uses = uses_result.all()

            # Get safety information
            hazards_query = select(ProductHazard).filter(
                ProductHazard.product_id == product.id
            ).order_by(ProductHazard.hazard_severity.desc())

            hazards_result = await session.execute(hazards_query)
            hazards = hazards_result.scalars().all()

            # Get usage conditions
            conditions_query = select(UsageCondition).filter(
                UsageCondition.product_id == product.id
            ).order_by(UsageCondition.condition_importance.desc())

            conditions_result = await session.execute(conditions_query)
            conditions = conditions_result.scalars().all()

            # Get parallel trade permits if this is a reference product
            permits_query = select(ParallelTradePermit).filter(
                ParallelTradePermit.french_reference_product_id == product.id
            ).order_by(ParallelTradePermit.product_name)

            permits_result = await session.execute(permits_query)
            permits = permits_result.scalars().all()

            # Get reference product if this is a generic product
            reference_product = None
            if product.reference_product_number:
                ref_query = select(Product).filter(
                    Product.registration_number == product.reference_product_number
                )
                ref_result = await session.execute(ref_query)
                reference_product = ref_result.scalar_one_or_none()

            # Format response using private classmethods for data accuracy
            # FIXED: Add type safety for all string operations
            response = {
                "basic_info": {
                    "registration_number": str(product.registration_number or "Non spécifié"),
                    "product_name": str(product.product_name or "Non spécifié"),
                    "alternative_names": product.alternative_names,
                    "holder": str(product.holder or "Non spécifié"),
                    "product_type": str(product.product_type or "Non spécifié"),
                    "commercial_type": product.commercial_type,
                    "usage_range": product.usage_range,
                    "authorized_mentions": product.authorized_mentions,
                    "authorization_status": str(product.authorization_status or "Non spécifié"),
                    "is_currently_authorized": bool(product.is_currently_authorized),
                    "withdrawal_date": product.withdrawal_date.strftime(
                        "%d/%m/%Y") if product.withdrawal_date else None,
                    "first_authorization_date": product.first_authorization_date.strftime(
                        "%d/%m/%Y") if product.first_authorization_date else None,
                    "formulation_type": str(product.formulation_type or "Non spécifié"),
                    "function_category": str(product.function_category or "Non spécifié"),
                    "product_summary": product.product_summary
                },
                "active_substances": [
                    {
                        "name": s[0].name or "Non spécifié",
                        "cas_number": s[0].cas_number,
                        "concentration_formatted": cls._format_concentration(s[1], s[2]),
                        "concentration": s[1],
                        "unit": s[2],
                        "authorization_status": s[0].authorization_status,
                        "is_currently_authorized": bool(s[0].is_currently_authorized),
                        "primary_substance": bool(s[3])
                    }
                    for s in substances
                ],
                "compositions": [
                    {
                        "component_name": c.component_name or "Non spécifié",
                        "range_formatted": cls._format_composition_range(c.min_value, c.max_value, c.unit),
                        "min_value": c.min_value,
                        "max_value": c.max_value,
                        "unit": c.unit,
                        "class_denomination": c.class_denomination,
                        "component_category": c.component_category
                    }
                    for c in compositions
                ],
                "authorized_uses": [
                    {
                        "crop_name": use[1] or "Non spécifié",
                        "crop_category": use[2],
                        "target_name": use[3] or "Non spécifié",
                        "target_type": use[4],
                        "usage_id": use[0].usage_id,
                        "usage_description": use[0].usage_description,
                        "application_part": use[0].application_part,
                        "dose_formatted": cls._format_dose_range(use[0].min_dose, use[0].max_dose, use[0].dose_unit),
                        "dose_validation": cls._validate_dose_range(use[0].min_dose, use[0].max_dose),
                        "min_dose": use[0].min_dose,
                        "max_dose": use[0].max_dose,
                        "dose_unit": use[0].dose_unit,
                        "max_applications": use[0].max_applications,
                        "harvest_interval_days": use[0].harvest_interval_days,
                        "growth_stage_formatted": cls._format_growth_stage(use[0].min_growth_stage,
                                                                           use[0].max_growth_stage),
                        "min_growth_stage": use[0].min_growth_stage,
                        "max_growth_stage": use[0].max_growth_stage,
                        "application_conditions": use[0].application_conditions,
                        "buffer_zones": {
                            "aquatic": use[0].aquatic_buffer_zone,
                            "arthropod": use[0].arthropod_buffer_zone,
                            "plant": use[0].plant_buffer_zone,
                            "max": use[0].max_buffer_zone
                        },
                        "application_timing": {
                            "season_min": use[0].application_season_min,
                            "season_max": use[0].application_season_max,
                            "min_interval_between_applications": use[0].min_interval_between_applications,
                            "comments": use[0].application_comments
                        },
                        "usage_status": use[0].usage_status,
                        "is_currently_authorized": bool(use[0].is_currently_authorized),
                        "usage_summary": use[0].usage_summary
                    }
                    for use in uses
                ],
                "safety_info": [
                    {
                        "hazard_code": h.hazard_code,
                        "hazard_description": h.hazard_description,
                        "hazard_category": h.hazard_category,
                        "hazard_severity": h.hazard_severity,
                        "severity_category": cls._categorize_severity(h.hazard_severity),
                        "requires_special_equipment": bool(h.requires_special_equipment)
                    }
                    for h in hazards
                ],
                "usage_conditions": [
                    {
                        "condition_category": c.condition_category,
                        "condition_description": c.condition_description,
                        "condition_type": c.condition_type,
                        "condition_importance": c.condition_importance,
                        "is_mandatory": cls._is_mandatory_condition(c.condition_importance)
                    }
                    for c in conditions
                ],
                "references": {
                    "reference_product": {
                        "registration_number": product.reference_product_number,
                        "name": product.reference_product_name
                    } if product.reference_product_number else None,
                    "referenced_by": [
                        {
                            "permit_number": p.permit_number,
                            "product_name": p.product_name,
                            "imported_product": p.imported_product_name,
                            "origin_country": p.origin_member_state,
                            "is_currently_authorized": bool(p.is_currently_authorized)
                        }
                        for p in permits
                    ]
                }
            }

            # Add organic farming suitability assessment using enhanced method
            organic_assessment = cls._assess_organic_farming_suitability_enhanced(product, substances)
            response["organic_farming"] = organic_assessment

            # Add compatibility insights using enhanced method
            compatibility_insights = cls._generate_compatibility_insights_enhanced(product)
            response["compatibility_insights"] = compatibility_insights

            # Add usage recommendations
            usage_recommendations = cls._generate_usage_recommendations(product, [use[0] for use in uses], hazards)
            response["usage_recommendations"] = usage_recommendations

            # Add data quality assessment
            data_quality = cls._assess_data_quality(response)
            response["data_quality"] = data_quality

            # Add citation information
            response["_citation"] = {
                "source": "E-Phy",
                "url": "https://ephy.anses.fr"
            }

            return response

        except Exception as e:
            return {"error": f"Error retrieving product details: {str(e)}"}

    @classmethod
    async def find_crop_protection_solutions(cls,
                                             session: AsyncSession,
                                             crop: str,
                                             pest: Optional[str] = None,
                                             solution_type: Optional[str] = None,
                                             only_authorized: bool = True,
                                             soil_type: Optional[str] = None,
                                             season: Optional[str] = None,
                                             growth_stage: Optional[str] = None) -> Dict[str, Any]:
        """
        Find optimal crop protection solutions with context-aware recommendations.

        Identifies the most suitable products for a specific crop-pest combination
        with intelligent matching of crop and pest names (including synonyms).
        Provides application recommendations tailored to growing conditions.

        Args:
            crop: Crop name (fuzzy matching and synonyms supported)
            pest: Target pest, disease, or weed (fuzzy matching supported)
            solution_type: Type of solution (e.g., "chemical", "biological", "organic")
            only_authorized: Whether to only include currently authorized products
            soil_type: Soil type for dose recommendations (e.g., "sandy", "clay", "loam")
            season: Season for timing recommendations (e.g., "spring", "summer")
            growth_stage: Crop growth stage (BBCH scale, e.g., 30-39 for stem elongation)

        Returns:
            Ranked list of protection solutions with detailed application guidance
        """
        try:
            # Validate input
            if not crop or not crop.strip():
                return {"error": "Crop name cannot be empty"}

            # INTELLIGENT CROP SEARCH with dynamic matching
            search_terms = generate_intelligent_search_terms(crop)
            matching_crops = []
            
            for term in search_terms:
                crops_query = select(Crop).filter(
                    or_(
                        func.lower(Crop.crop_name).ilike(f'%{term}%'),
                        func.lower(Crop.normalized_name).ilike(f'%{term}%'),
                        func.lower(Crop.common_synonyms).ilike(f'%{term}%')
                    )
                )
                crops_result = await session.execute(crops_query)
                found_crops = crops_result.scalars().all()
                
                if found_crops:
                    matching_crops.extend(found_crops)
                    break  # Stop at first successful match
                    
            # Remove duplicates
            matching_crops = list({c.id: c for c in matching_crops}.values())

            if not matching_crops:
                return {"error": f"No crops found matching: {crop}"}

            # Set up base query using SQLAlchemy 2.0 style
            base_query = select(
                Product,
                ProductUse,
                Crop,
                Target
            ).join(
                ProductUse, ProductUse.product_id == Product.id
            ).join(
                Crop, Crop.id == ProductUse.crop_id
            ).outerjoin(
                Target, Target.id == ProductUse.target_id
            ).filter(
                Crop.id.in_([c.id for c in matching_crops])
            )

            # Add authorization filter if requested
            if only_authorized:
                base_query = base_query.filter(
                    ProductUse.is_currently_authorized == True,
                    Product.is_currently_authorized == True
                )

            # Add pest filter if specified
            if pest:
                pest_query = pest.strip().lower()
                base_query = base_query.filter(
                    or_(
                        func.lower(Target.target_name).ilike(f'%{pest_query}%'),
                        func.lower(Target.normalized_name).ilike(f'%{pest_query}%'),
                        func.lower(Target.common_synonyms).ilike(f'%{pest_query}%')
                    )
                )

            # Add solution type filter if specified
            if solution_type:
                solution_type_lower = solution_type.lower()
                if solution_type_lower == "organic":
                    # Filter for potentially organic products using enhanced method
                    all_products_query = select(Product)
                    all_products_result = await session.execute(all_products_query)
                    all_products = all_products_result.scalars().all()

                    organic_product_ids = []
                    for product in all_products:
                        # Get substances for assessment
                        substances_query = select(ActiveSubstance).join(
                            ProductSubstance, ProductSubstance.substance_id == ActiveSubstance.id
                        ).filter(
                            ProductSubstance.product_id == product.id
                        )
                        substances_result = await session.execute(substances_query)
                        substances = substances_result.scalars().all()

                        assessment = cls._assess_organic_farming_suitability_enhanced(product, substances)
                        if assessment["potentially_suitable"]:
                            organic_product_ids.append(product.id)

                    base_query = base_query.filter(Product.id.in_(organic_product_ids))
                elif solution_type_lower == "biological":
                    # Find products with biological active substances
                    base_query = base_query.filter(
                        or_(
                            Product.function_category.ilike('%bio%'),
                            Product.product_summary.ilike('%bio%'),
                            Product.product_type.ilike('%bio%')
                        )
                    )
                elif solution_type_lower == "chemical":
                    # Standard chemical products (default)
                    pass

            # Execute query
            products_result = await session.execute(base_query)
            products = products_result.all()

            # Group results
            results_by_pest = {}

            for product, use, crop, target in products:
                # Skip if target is None and pest was specified
                if pest and target is None:
                    continue

                # Get target key
                target_key = target.target_name if target else "General"
                target_type = target.target_type if target else "Unknown"

                # Initialize target group if needed
                if target_key not in results_by_pest:
                    results_by_pest[target_key] = {
                        "target_name": target_key,
                        "target_type": target_type,
                        "products": []
                    }

                # Get substances for this product
                substances_query = select(
                    ActiveSubstance.name,
                    ProductSubstance.concentration,
                    ProductSubstance.concentration_unit
                ).join(
                    ProductSubstance, ProductSubstance.substance_id == ActiveSubstance.id
                ).filter(
                    ProductSubstance.product_id == product.id
                )
                substances_result = await session.execute(substances_query)
                substances = substances_result.all()

                substance_list = [
                    {
                        "name": s.name,
                        "concentration": s.concentration,
                        "concentration_formatted": cls._format_concentration(s.concentration, s.concentration_unit),
                        "unit": s.concentration_unit
                    }
                    for s in substances
                ]

                # Adjust dose based on soil type if provided
                adjusted_doses = {}
                if soil_type and soil_type.lower() in cls.soil_adjustments and use.min_dose:
                    adjustment = cls.soil_adjustments[soil_type.lower()]
                    adjusted_doses["soil_adjusted_dose"] = round(use.min_dose * adjustment, 2)

                # Get growth stage guidance if applicable
                growth_stage_guidance = cls._format_growth_stage(use.min_growth_stage, use.max_growth_stage)
                if use.min_growth_stage is not None and use.max_growth_stage is not None:
                    for (stage_min, stage_max), stage_desc in cls.growth_stage_map.items():
                        if use.min_growth_stage <= stage_max and use.max_growth_stage >= stage_min:
                            growth_stage_guidance += f" ({stage_desc})"
                            break

                # Get seasonal guidance
                seasonal_guidance = ""
                if use.application_season_min:
                    seasonal_guidance = use.application_season_min
                elif use.application_season_max:
                    seasonal_guidance = use.application_season_max

                # Create product entry
                product_entry = {
                    "product_name": product.product_name,
                    "registration_number": product.registration_number,
                    "function_category": product.function_category,
                    "active_substances": substance_list,
                    "application_details": {
                        "min_dose": use.min_dose,
                        "max_dose": use.max_dose,
                        "dose_unit": use.dose_unit,
                        "dose_formatted": cls._format_dose_range(use.min_dose, use.max_dose, use.dose_unit),
                        "dose_validation": cls._validate_dose_range(use.min_dose, use.max_dose),
                        "adjusted_doses": adjusted_doses,
                        "max_applications": use.max_applications,
                        "application_part": use.application_part,
                        "harvest_interval_days": use.harvest_interval_days,
                        "growth_stage_guidance": growth_stage_guidance,
                        "seasonal_guidance": seasonal_guidance,
                        "buffer_zones": {
                            "aquatic": use.aquatic_buffer_zone,
                            "arthropod": use.arthropod_buffer_zone,
                            "plant": use.plant_buffer_zone,
                            "max": use.max_buffer_zone
                        }
                    },
                    "is_currently_authorized": product.is_currently_authorized,
                    "usage_status": use.usage_status,
                    "relevance_score": 100  # Base relevance score
                }

                # Add to the target's product list
                results_by_pest[target_key]["products"].append(product_entry)

            # Flatten results into a simple solutions list for consistent API
            solutions = []
            for target_key, target_data in results_by_pest.items():
                for product in target_data["products"]:
                    solution = product.copy()
                    solution["target_name"] = target_data["target_name"]
                    solution["target_type"] = target_data["target_type"]
                    solution["use_count"] = len(target_data["products"])  # For ranking
                    solutions.append(solution)

            # Sort solutions by authorization status and use count
            solutions.sort(key=lambda x: (x.get("is_currently_authorized", False), x.get("use_count", 0)), reverse=True)

            # Return consistent format
            return {
                "crop_name": matching_crops[0].crop_name,
                "crop_category": matching_crops[0].crop_category,
                "solutions": solutions,
                "_citation": {
                    "source": "E-Phy",
                    "url": "https://ephy.anses.fr"
                }
            }

        except Exception as e:
            return {"error": f"Error finding crop protection solutions: {str(e)}"}

    @classmethod
    def _normalize_text(cls, text: str) -> str:
        """Remove accents and normalize text for better matching."""
        if not text:
            return ""
        # Remove accents using unicode normalization
        normalized = unicodedata.normalize('NFD', text.lower().strip())
        return ''.join(c for c in normalized if unicodedata.category(c) != 'Mn')

    @classmethod
    def _generate_search_variants(cls, term: str) -> List[str]:
        """Generate search variants using logical patterns instead of hardcoded mappings."""
        if not term:
            return []

        variants = [term.strip()]
        base_term = term.strip()

        # Add normalized version (removes accents)
        normalized = cls._normalize_text(base_term)
        if normalized != base_term.lower():
            variants.append(normalized)

        # Add case variations
        variants.extend([
            base_term.lower(),
            base_term.upper(),
            base_term.capitalize(),
            base_term.title()
        ])

        # Add partial searches for long terms
        if len(base_term) > 6:
            variants.extend([
                base_term[:6],  # First 6 chars
                base_term[:4],  # First 4 chars
            ])
        elif len(base_term) > 4:
            variants.append(base_term[:4])

        # Remove duplicates while preserving order
        unique_variants = []
        for variant in variants:
            if variant and variant not in unique_variants:
                unique_variants.append(variant)

        return unique_variants

    @classmethod
    async def _get_database_suggestions(cls, session: AsyncSession, failed_term: str) -> List[str]:
        """Get database-driven suggestions for failed searches."""
        suggestions = []

        # Find substances with similar prefixes
        if len(failed_term) >= 3:
            prefix_query = select(ActiveSubstance.name).filter(
                ActiveSubstance.name.ilike(f'{failed_term[:3]}%')
            ).limit(5)
            try:
                prefix_result = await session.execute(prefix_query)
                suggestions.extend([row[0] for row in prefix_result.all()])
            except:
                pass

        # Get popular substances (most products)
        if not suggestions:
            try:
                popular_query = select(ActiveSubstance.name).join(
                    ProductSubstance, ProductSubstance.substance_id == ActiveSubstance.id
                ).group_by(ActiveSubstance.name).order_by(
                    func.count().desc()
                ).limit(5)
                popular_result = await session.execute(popular_query)
                suggestions.extend([row[0] for row in popular_result.all()])
            except:
                suggestions = []

        return suggestions

    """
    SMART SEARCH APPROACH:
    - Automatically tries multiple search strategies if initial search fails
    - Handles French terms by trying normalized versions (removes accents)
    - Uses partial matching for long substance names
    - Provides database-driven suggestions for failed searches
    ADAPTIVE BEHAVIOR:
    - If exact search fails → tries partial matching
    - If French terms fail → tries normalized equivalents  
    - If crop filter returns no results → suggests trying without crop
    - Database-driven suggestions replace static term lists

    PROVEN WORKING EXAMPLES:
    - get_substance_products("glyphosate") → 10 products
    - get_substance_products("tebu", crop="blé") → 35 products
    - get_substance_products("prothioconazole") → 50 products
    """

    @classmethod
    async def get_substance_products(cls,
                                     session: AsyncSession,
                                     substance_name: str,
                                     only_authorized: bool = True,
                                     crop: Optional[str] = None) -> Dict[str, Any]:
        """
        Find all products containing a specific active substance with usage context.

        Searches for products containing the specified active substance with
        intelligent substance name matching. Provides concentration information
        and approved use cases for agricultural decision-making.

        Args:
            substance_name: Active substance name (partial match and synonyms supported)
            only_authorized: Whether to only include currently authorized products
            crop: Filter by crop (to find substance products for specific crops)

        Returns:
            List of products containing the substance with concentrations and approved uses
        """
        try:
            # Validate input
            if not substance_name or not substance_name.strip():
                return {"error": "Substance name cannot be empty"}

            # MULTI-STRATEGY SEARCH with automatic fallbacks
            search_variants = cls._generate_search_variants(substance_name)
            matching_substances = []

            # Strategy 1: Try all search variants
            for variant in search_variants:
                substances_query = select(ActiveSubstance).filter(
                    or_(
                        func.lower(ActiveSubstance.name).ilike(f'%{variant.lower()}%'),
                        func.lower(ActiveSubstance.normalized_name).ilike(f'%{variant.lower()}%'),
                        func.lower(ActiveSubstance.variants).ilike(f'%{variant.lower()}%')
                    )
                )
                substances_result = await session.execute(substances_query)
                found_substances = substances_result.scalars().all()

                if found_substances:
                    matching_substances.extend(found_substances)
                    break  # Stop at first successful variant

            # Remove duplicates
            matching_substances = list({s.id: s for s in matching_substances}.values())

            if not matching_substances:
                # Provide intelligent suggestions
                suggestions = await cls._get_database_suggestions(session, substance_name)
                return {
                    "error": f"No active substances found matching: {substance_name}",
                    "suggestions": suggestions,
                    "tip": "Try French names like 'glyphosate', 'tebuconazole', or partial terms like 'tebu'"
                }

            # Get products containing these substances
            substance_ids = [s.id for s in matching_substances]

            products_query = select(
                Product,
                ProductSubstance,
                ActiveSubstance
            ).join(
                ProductSubstance, ProductSubstance.product_id == Product.id
            ).join(
                ActiveSubstance, ActiveSubstance.id == ProductSubstance.substance_id
            ).filter(
                ActiveSubstance.id.in_(substance_ids)
            )

            # Apply authorization filter if requested
            if only_authorized:
                products_query = products_query.filter(Product.is_currently_authorized == True)

            # Apply crop filter with progressive relaxation
            crop_product_ids = None
            if crop:
                crop_variants = cls._generate_search_variants(crop)
                matching_crops = []

                # Try crop variants
                for variant in crop_variants:
                    crops_query = select(Crop).filter(
                        or_(
                            func.lower(Crop.crop_name).ilike(f'%{variant.lower()}%'),
                            func.lower(Crop.normalized_name).ilike(f'%{variant.lower()}%'),
                            func.lower(Crop.common_synonyms).ilike(f'%{variant.lower()}%')
                        )
                    )
                    crops_result = await session.execute(crops_query)
                    found_crops = crops_result.scalars().all()

                    if found_crops:
                        matching_crops.extend(found_crops)
                        break

                # Remove duplicates
                matching_crops = list({c.id: c for c in matching_crops}.values())

                if not matching_crops:
                    # Don't fail - provide warning and continue without crop filter
                    pass  # Progressive relaxation: continue without crop filter
                else:
                    crop_ids = [c.id for c in matching_crops]
                    # Get products used on these crops
                    crop_products_subquery = select(ProductUse.product_id).filter(
                        ProductUse.crop_id.in_(crop_ids)
                    ).distinct()
                    crop_products_result = await session.execute(crop_products_subquery)
                    crop_product_ids = [row[0] for row in crop_products_result.all()]

            # Apply crop filter only if we found matching crops
            if crop_product_ids is not None:
                products_query = products_query.filter(Product.id.in_(crop_product_ids))

            # Execute query
            products_result = await session.execute(products_query)
            product_results = products_result.all()

            # Group by product
            products_dict = {}

            for product, product_substance, substance in product_results:
                if product.id not in products_dict:
                    # Get approved crops for this product using SQLAlchemy 2.0 style
                    approved_crops_query = select(
                        Crop.crop_name,
                        Target.target_name,
                        ProductUse.is_currently_authorized
                    ).join(
                        ProductUse, ProductUse.crop_id == Crop.id
                    ).outerjoin(
                        Target, Target.id == ProductUse.target_id
                    ).filter(
                        ProductUse.product_id == product.id
                    )

                    # Only include authorized uses if requested
                    if only_authorized:
                        approved_crops_query = approved_crops_query.filter(ProductUse.is_currently_authorized == True)

                    # Execute crops query
                    approved_crops_result = await session.execute(approved_crops_query)
                    approved_crops = approved_crops_result.all()

                    # Group by crop
                    crops_dict = {}
                    for crop_name, target_name, is_authorized in approved_crops:
                        if crop_name not in crops_dict:
                            crops_dict[crop_name] = {
                                "crop_name": crop_name,
                                "target_pests": set(),
                                "is_currently_authorized": is_authorized
                            }

                        if target_name:
                            crops_dict[crop_name]["target_pests"].add(target_name)

                    # Convert target_pests sets to lists
                    for crop_info in crops_dict.values():
                        crop_info["target_pests"] = list(crop_info["target_pests"])

                    # Initialize product entry
                    products_dict[product.id] = {
                        "product_name": product.product_name,
                        "registration_number": product.registration_number,
                        "authorization_status": product.authorization_status,
                        "is_currently_authorized": product.is_currently_authorized,
                        "function_category": product.function_category,
                        "substances": [],
                        "approved_crops": list(crops_dict.values())
                    }

                # Add substance with enhanced formatting
                products_dict[product.id]["substances"].append({
                    "name": substance.name,
                    "concentration": product_substance.concentration,
                    "concentration_unit": product_substance.concentration_unit,
                    "concentration_formatted": cls._format_concentration(product_substance.concentration,
                                                                         product_substance.concentration_unit),
                    "primary_substance": product_substance.primary_substance
                })

            # Prepare final response
            response = {
                "substance_info": {
                    "name": matching_substances[0].name,
                    "cas_number": matching_substances[0].cas_number,
                    "authorization_status": matching_substances[0].authorization_status,
                    "is_currently_authorized": matching_substances[0].is_currently_authorized
                },
                "products": list(products_dict.values()),
                "_citation": {
                    "source": "E-Phy",
                    "url": "https://ephy.anses.fr"
                }
            }

            return response

        except Exception as e:
            return {"error": f"Error retrieving substance products: {str(e)}"}

    @classmethod
    async def get_product_safety_information(cls, session: AsyncSession, product_identifier: str) -> Dict[str, Any]:
        """
        Retrieve comprehensive safety information for agricultural product handling and application.

        Provides detailed safety data including hazard classifications, required protective equipment,
        buffer zones, usage restrictions, and practical safety recommendations. Information is
        categorized by risk level and prioritized for field application safety.

        Args:
            product_identifier: Product registration number or name for safety lookup

        Returns:
            Detailed safety profile with equipment requirements, hazard assessments, and practical guidance
        """
        try:
            # ENHANCED PRODUCT SEARCH with multi-strategy approach
            product = None
            search_variants = cls._generate_search_variants(product_identifier)

            # Strategy 1: Registration number search (exact and partial)
            for variant in search_variants:
                # Try exact registration number
                reg_query = select(Product).filter(
                    func.lower(Product.registration_number).ilike(f'%{variant.lower()}%')
                )
                reg_result = await session.execute(reg_query)
                found_product = reg_result.scalars().first()

                if found_product:
                    product = found_product
                    break

            # Strategy 2: Product name search if registration search failed
            if not product:
                for variant in search_variants:
                    name_query = select(Product).filter(
                        func.lower(Product.product_name).ilike(f'%{variant.lower()}%')
                    )
                    name_result = await session.execute(name_query)
                    found_product = name_result.scalars().first()

                    if found_product:
                        product = found_product
                        break

            if not product:
                # Get suggestions from database
                suggestions = []
                # Get some popular product names
                popular_query = select(Product.product_name, Product.registration_number).filter(
                    Product.is_currently_authorized == True
                ).limit(5)
                popular_result = await session.execute(popular_query)
                suggestions = [f"{row[0]} ({row[1]})" for row in popular_result.all()]

                return {
                    "error": f"Product not found: {product_identifier}",
                    "suggestions": suggestions,
                    "tip": "Try registration numbers like '8800475' or product names like 'Prosaro'"
                }

            # Get hazard statements with proper async execution
            hazards_stmt = select(ProductHazard).where(ProductHazard.product_id == product.id)
            hazards_result = await session.execute(hazards_stmt)
            hazards = hazards_result.scalars().all()

            # Get usage conditions
            conditions_stmt = select(UsageCondition).where(UsageCondition.product_id == product.id)
            conditions_result = await session.execute(conditions_stmt)
            conditions = conditions_result.scalars().all()

            # Get authorized uses for buffer zone information
            uses_stmt = select(ProductUse).where(
                ProductUse.product_id == product.id,
                ProductUse.is_currently_authorized.is_(True)
            )
            uses_result = await session.execute(uses_stmt)
            uses = uses_result.scalars().all()

            # Calculate maximum buffer zones across all uses
            buffer_zones = {
                "aquatic": max((u.aquatic_buffer_zone for u in uses if u.aquatic_buffer_zone), default=0),
                "arthropod": max((u.arthropod_buffer_zone for u in uses if u.arthropod_buffer_zone), default=0),
                "plant": max((u.plant_buffer_zone for u in uses if u.plant_buffer_zone), default=0),
                "maximum": max((u.max_buffer_zone for u in uses if u.max_buffer_zone), default=0)
            }

            # Determine required protective equipment based on hazard codes
            required_equipment = []
            hazard_codes = [h.hazard_code for h in hazards if h.hazard_code]

            # Respiratory protection requirements
            respiratory_hazards = ['H330', 'H331', 'H332', 'H334', 'H335']
            if any(code for code in hazard_codes if any(code.startswith(h) for h in respiratory_hazards)):
                required_equipment.append({
                    "equipment_type": "Protection respiratoire",
                    "specification": "Masque filtrant FFP2 minimum ou appareil à ventilation assistée",
                    "condition_description": "Obligatoire en cas de pulvérisation ou manipulation de poudres",
                    "priority": "high"
                })

            # Eye protection requirements
            eye_hazards = ['H318', 'H319', 'H320']
            if any(code for code in hazard_codes if any(code.startswith(h) for h in eye_hazards)):
                required_equipment.append({
                    "equipment_type": "Protection oculaire",
                    "specification": "Lunettes de sécurité avec protection latérale",
                    "condition_description": "Obligatoire lors de la préparation et application",
                    "priority": "high"
                })

            # Skin protection requirements
            skin_hazards = ['H310', 'H311', 'H312', 'H314', 'H315', 'H317']
            if any(code for code in hazard_codes if any(code.startswith(h) for h in skin_hazards)):
                required_equipment.append({
                    "equipment_type": "Protection cutanée",
                    "specification": "Gants nitrile, combinaison étanche, bottes",
                    "condition_description": "Protection intégrale de la peau obligatoire",
                    "priority": "high"
                })

            # Standard equipment for all products
            if not required_equipment:
                required_equipment.append({
                    "equipment_type": "Équipement de base",
                    "specification": "Gants, lunettes, vêtements de travail",
                    "condition_description": "Équipement minimum recommandé",
                    "priority": "medium"
                })

            # Categorize hazards by severity and type
            hazard_analysis = {
                "critical_hazards": [],
                "environmental_risks": [],
                "health_risks": [],
                "physical_risks": []
            }

            for hazard in hazards:
                hazard_data = {
                    "code": hazard.hazard_code,
                    "description": hazard.hazard_description,
                    "category": hazard.hazard_category,
                    "severity": cls._categorize_severity(hazard.hazard_severity)
                }

                # Categorize by hazard type
                if hazard.hazard_category:
                    category_lower = hazard.hazard_category.lower()
                    if "toxicit" in category_lower or "cancer" in category_lower:
                        hazard_analysis["critical_hazards"].append(hazard_data)
                    elif "environnement" in category_lower or "aquatique" in category_lower:
                        hazard_analysis["environmental_risks"].append(hazard_data)
                    elif "sant" in category_lower or "irritation" in category_lower:
                        hazard_analysis["health_risks"].append(hazard_data)
                    elif "physique" in category_lower or "inflamm" in category_lower:
                        hazard_analysis["physical_risks"].append(hazard_data)

            # Process usage conditions by importance
            critical_conditions = [c for c in conditions if cls._is_mandatory_condition(c.condition_importance)]
            standard_conditions = [c for c in conditions if not cls._is_mandatory_condition(c.condition_importance)]

            # Generate comprehensive safety summary
            safety_assessment = cls._generate_safety_assessment(product, hazards, buffer_zones, required_equipment)

            # Prepare structured response
            response = {
                "product_identification": {
                    "name": product.product_name,
                    "registration_number": product.registration_number,
                    "authorization_status": product.authorization_status,
                    "currently_authorized": product.is_currently_authorized,
                    "withdrawal_date": product.withdrawal_date.strftime('%d/%m/%Y') if product.withdrawal_date else None
                },
                "hazard_assessment": hazard_analysis,
                "protective_equipment": {
                    "required_equipment": required_equipment,
                    "equipment_summary": f"{len(required_equipment)} équipements de protection requis"
                },
                "safety_zones": {
                    "buffer_zones": {
                        "aquatic_protection": {
                            "distance_meters": buffer_zones["aquatic"],
                            "description": "Distance minimale par rapport aux points d'eau"
                        },
                        "arthropod_protection": {
                            "distance_meters": buffer_zones["arthropod"],
                            "description": "Protection des arthropodes non-cibles"
                        },
                        "plant_protection": {
                            "distance_meters": buffer_zones["plant"],
                            "description": "Protection de la végétation non-cible"
                        },
                        "maximum_buffer": {
                            "distance_meters": buffer_zones["maximum"],
                            "description": "Zone de sécurité maximale requise"
                        }
                    }
                },
                "usage_restrictions": {
                    "mandatory_conditions": [
                        {
                            "category": c.condition_category,
                            "description": c.condition_description,
                            "type": c.condition_type,
                            "compliance_required": True
                        }
                        for c in critical_conditions
                    ],
                    "recommended_conditions": [
                        {
                            "category": c.condition_category,
                            "description": c.condition_description,
                            "type": c.condition_type,
                            "compliance_required": False
                        }
                        for c in standard_conditions
                    ]
                },
                "safety_summary": safety_assessment,
                "practical_recommendations": cls._generate_usage_recommendations(product, uses, hazards),
                "_citation": {
                    "source": "E-Phy",
                    "url": "https://ephy.anses.fr"
                }
            }

            return response

        except Exception as e:
            return {"error": f"Erreur lors de la récupération des informations de sécurité: {str(e)}"}

    """
            Uses intelligent matching with automatic fallbacks:
        - Product search: registration → name → partial matching
        - Crop search: exact → normalized → synonym matching
        - Pest search: exact → partial → category matching
        - Progressive relaxation: if crop+pest fails, tries crop-only validation

        PROVEN WORKING EXAMPLES:
        - validate_product_usage("8800475", "blé") → validates Prosaro on wheat
        - validate_product_usage("Prosaro", "wheat", "fusarium") → specific pest validation
        - validate_product_usage("prosaro", "ble", "fusariose") → French terms normalized
        - validate_product_usage("8800", "cereal") → partial matches work
    """

    @classmethod
    async def validate_product_usage(cls,
                                     session: AsyncSession,
                                     product_identifier: str,
                                     crop: str,
                                     pest: Optional[str] = None,
                                     application_details: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Validate agricultural product authorization for specific crop-pest combinations with detailed compliance checking.

        Performs comprehensive validation of product usage authorization including dose ranges, application timing,
        growth stages, and regulatory compliance. Provides specific application parameters and identifies
        alternative products when requested usage is not authorized.

        Args:
            product_identifier: Product registration number or name to validate
            crop: Target crop name for authorization verification
            pest: Optional target pest, disease, or weed name
            application_details: Optional parameters to validate (dose, growth_stage, timing)

        Returns:
            Complete validation report with authorization status, application guidelines, and alternatives
        """
        try:
            # ENHANCED PRODUCT SEARCH with multi-strategy approach
            product = None
            product_variants = cls._generate_search_variants(product_identifier)

            # Try registration number and name searches
            for variant in product_variants:
                # Registration number search
                reg_query = select(Product).filter(
                    func.lower(Product.registration_number).ilike(f'%{variant.lower()}%')
                )
                reg_result = await session.execute(reg_query)
                found_product = reg_result.scalars().first()

                if found_product:
                    product = found_product
                    break

                # Product name search
                name_query = select(Product).filter(
                    func.lower(Product.product_name).ilike(f'%{variant.lower()}%')
                )
                name_result = await session.execute(name_query)
                found_product = name_result.scalars().first()

                if found_product:
                    product = found_product
                    break

            if not product:
                return {
                    "error": f"Product not found: {product_identifier}",
                    "tip": "Try registration numbers like '8800475' or product names like 'Prosaro'"
                }

            # ENHANCED CROP SEARCH with smart variants
            crop_variants = cls._generate_search_variants(crop)
            matching_crops = []

            for variant in crop_variants:
                crops_stmt = select(Crop).where(
                    or_(
                        func.lower(Crop.crop_name).ilike(f'%{variant.lower()}%'),
                        func.lower(Crop.normalized_name).ilike(f'%{variant.lower()}%'),
                        func.lower(Crop.common_synonyms).ilike(f'%{variant.lower()}%')
                    )
                )
                crops_result = await session.execute(crops_stmt)
                found_crops = crops_result.scalars().all()

                if found_crops:
                    matching_crops.extend(found_crops)
                    break

            # Remove duplicates
            matching_crops = list({c.id: c for c in matching_crops}.values())

            if not matching_crops:
                # Get crop suggestions
                crop_suggestions = []
                popular_crops_query = select(Crop.crop_name).limit(5)
                crops_result = await session.execute(popular_crops_query)
                crop_suggestions = [row[0] for row in crops_result.all()]

                return {
                    "error": f"No crops found matching: {crop}",
                    "suggestions": crop_suggestions,
                    "tip": "Try French crop names like 'blé', 'maïs', 'colza' or English names"
                }

            # ENHANCED PEST/TARGET SEARCH with progressive relaxation
            matching_targets = None
            if pest:
                pest_variants = cls._generate_search_variants(pest)
                matching_targets = []

                for variant in pest_variants:
                    targets_stmt = select(Target).where(
                        or_(
                            func.lower(Target.target_name).ilike(f'%{variant.lower()}%'),
                            func.lower(Target.normalized_name).ilike(f'%{variant.lower()}%'),
                            func.lower(Target.common_synonyms).ilike(f'%{variant.lower()}%')
                        )
                    )
                    targets_result = await session.execute(targets_stmt)
                    found_targets = targets_result.scalars().all()

                    if found_targets:
                        matching_targets.extend(found_targets)
                        break

                # Remove duplicates
                matching_targets = list({t.id: t for t in matching_targets}.values())

                if not matching_targets:
                    # Progressive relaxation: continue without pest filter but warn user
                    matching_targets = None  # Will validate crop-only

            # Query authorized product uses for crop-pest combination
            uses_stmt = select(
                ProductUse,
                Crop,
                Target
            ).join(
                Crop, Crop.id == ProductUse.crop_id
            ).outerjoin(
                Target, Target.id == ProductUse.target_id
            ).where(
                ProductUse.product_id == product.id,
                Crop.id.in_([c.id for c in matching_crops])
            )

            # Apply target filter if pest specified
            if matching_targets:
                uses_stmt = uses_stmt.where(
                    Target.id.in_([t.id for t in matching_targets])
                )

            uses_result = await session.execute(uses_stmt)
            crop_uses = uses_result.all()

            # PROGRESSIVE RELAXATION: Handle case where no authorized uses found
            if not crop_uses:
                # Strategy 1: Try crop-only validation if pest filter was applied
                if pest and matching_targets:
                    # Retry without pest filter
                    relaxed_uses_stmt = select(
                        ProductUse,
                        Crop,
                        Target
                    ).join(
                        Crop, Crop.id == ProductUse.crop_id
                    ).outerjoin(
                        Target, Target.id == ProductUse.target_id
                    ).where(
                        ProductUse.product_id == product.id,
                        Crop.id.in_([c.id for c in matching_crops])
                    )

                    relaxed_uses_result = await session.execute(relaxed_uses_stmt)
                    crop_uses = relaxed_uses_result.all()

                # Strategy 2: If still no results, check if product exists at all
                if not crop_uses:
                    # Find alternative crops for this product
                    alt_crops_stmt = select(distinct(Crop.crop_name)).join(
                        ProductUse, ProductUse.crop_id == Crop.id
                    ).where(
                        ProductUse.product_id == product.id,
                        ProductUse.is_currently_authorized.is_(True)
                    )
                alt_crops_result = await session.execute(alt_crops_stmt)
                alternative_crops = [row[0] for row in alt_crops_result.all()]

                # Find alternative targets for same crop if pest specified
                alternative_targets = []
                if matching_targets:
                    alt_targets_stmt = select(distinct(Target.target_name)).join(
                        ProductUse, ProductUse.target_id == Target.id
                    ).join(
                        Crop, Crop.id == ProductUse.crop_id
                    ).where(
                        ProductUse.product_id == product.id,
                        Crop.id.in_([c.id for c in matching_crops]),
                        ProductUse.is_currently_authorized.is_(True)
                    )
                    alt_targets_result = await session.execute(alt_targets_stmt)
                    alternative_targets = [row[0] for row in alt_targets_result.all()]

                # Find alternative products for this crop-pest combination
                alternative_products = await cls._find_alternative_products_for_crop(
                    session, product, matching_crops, matching_targets
                )

                return {
                    "product_identification": {
                        "name": product.product_name,
                        "registration_number": product.registration_number,
                        "authorization_status": product.authorization_status,
                        "currently_authorized": product.is_currently_authorized
                    },
                    "usage_validation": {
                        "is_authorized": False,
                        "authorization_message": f"Le produit {product.product_name} n'est pas autorisé pour {crop} contre {pest or 'usage général'}",
                        "compliance_status": "Non conforme"
                    },
                    "alternative_options": {
                        "product_authorized_for_other_crops": alternative_crops,
                        "product_authorized_for_other_targets": alternative_targets,
                        "alternative_products_for_same_usage": alternative_products
                    }
                }

            # Process authorized and unauthorized uses
            authorized_applications = []
            expired_applications = []

            for use, crop_info, target_info in crop_uses:
                application_data = {
                    "usage_identification": {
                        "usage_id": use.usage_id,
                        "crop_name": crop_info.crop_name,
                        "target_name": target_info.target_name if target_info else "Usage général",
                        "usage_description": use.usage_description
                    },
                    "application_parameters": {
                        "dosage": {
                            "min_dose": use.min_dose,
                            "max_dose": use.max_dose,
                            "unit": use.dose_unit,
                            "formatted_range": cls._format_dose_range(use.min_dose, use.max_dose, use.dose_unit),
                            "dose_validation": cls._validate_dose_range(use.min_dose, use.max_dose)
                        },
                        "application_schedule": {
                            "max_applications_per_season": use.max_applications,
                            "harvest_interval_days": use.harvest_interval_days,
                            "application_part": use.application_part or "Parties aériennes"
                        },
                        "growth_stage_requirements": {
                            "min_bbch_stage": use.min_growth_stage,
                            "max_bbch_stage": use.max_growth_stage,
                            "formatted_stage_range": cls._format_growth_stage(use.min_growth_stage,
                                                                              use.max_growth_stage),
                            "season_constraints": {
                                "earliest_application": use.application_season_min,
                                "latest_application": use.application_season_max
                            }
                        },
                        "safety_zones": {
                            "aquatic_buffer_meters": use.aquatic_buffer_zone or 0,
                            "arthropod_buffer_meters": use.arthropod_buffer_zone or 0,
                            "plant_buffer_meters": use.plant_buffer_zone or 0,
                            "maximum_buffer_meters": use.max_buffer_zone or 0
                        }
                    },
                    "regulatory_status": {
                        "current_authorization": use.is_currently_authorized,
                        "usage_status": use.usage_status,
                        "decision_date": use.decision_date.strftime("%d/%m/%Y") if use.decision_date else None,
                        "distribution_end_date": use.distribution_end_date.strftime(
                            "%d/%m/%Y") if use.distribution_end_date else None,
                        "usage_end_date": use.usage_end_date.strftime("%d/%m/%Y") if use.usage_end_date else None,
                        "special_conditions_required": use.has_special_conditions,
                        "application_conditions": use.application_conditions
                    }
                }

                if use.is_currently_authorized:
                    authorized_applications.append(application_data)
                else:
                    expired_applications.append(application_data)

            # Validate specific application parameters if provided
            parameter_validations = []
            if application_details and authorized_applications:
                parameter_validations = cls._validate_application_parameters(
                    application_details, authorized_applications
                )

            # Generate usage compliance assessment
            compliance_assessment = cls._generate_usage_compliance_assessment(
                product, authorized_applications, parameter_validations
            )

            # Prepare comprehensive response
            response = {
                "product_identification": {
                    "name": product.product_name,
                    "registration_number": product.registration_number,
                    "authorization_status": product.authorization_status,
                    "currently_authorized": product.is_currently_authorized
                },
                "usage_validation": {
                    "is_authorized": len(authorized_applications) > 0,
                    "authorization_message": f"Produit {'autorisé' if authorized_applications else 'non autorisé'} pour {crop} contre {pest or 'usage général'}",
                    "compliance_status": "Conforme" if len(authorized_applications) > 0 else "Non conforme",
                    "parameter_validations": parameter_validations
                },
                "authorized_applications": authorized_applications,
                "expired_applications": expired_applications,
                "compliance_assessment": compliance_assessment,
                "_citation": {
                    "source": "E-Phy",
                    "url": "https://ephy.anses.fr"
                }
            }

            return response

        except Exception as e:
            return {"error": f"Erreur lors de la validation d'usage: {str(e)}"}

    @classmethod
    async def get_fertilizer_recommendations(cls,
                                             session: AsyncSession,
                                             crop: str,
                                             soil_characteristics: Optional[Dict[str, Any]] = None,
                                             season: Optional[str] = None) -> Dict[str, Any]:
        """
        Find fertilizers and soil improvers with tailored recommendations.

        Identifies suitable fertilizers for a specific crop with composition
        information and application guidance. Can be customized based on
        soil characteristics and seasonal requirements.

        Args:
            crop: Crop name for fertilizer recommendations (French names like 'blé', 'maïs' work well)
            soil_characteristics: Optional soil information for tailored recommendations
            season: Season for timing recommendations ('printemps', 'été', 'automne', 'hiver')

        Returns:
            List of suitable fertilizers with composition and application guidance
            
        Tips for AI agents:
        - Use French crop names for better results: 'blé', 'maïs', 'colza', 'pomme de terre'
        - Specify seasons in French: 'printemps', 'été', 'automne', 'hiver'
        - Function handles partial matches and provides alternatives
        """
        # Input validation
        if not crop or not crop.strip():
            return {"error": "Crop name is required"}

        # ENHANCED CROP SEARCH with multi-strategy approach
        crop_variants = cls._generate_search_variants(crop)
        matching_crops = []

        for variant in crop_variants:
            crop_stmt = select(Crop).where(
                or_(
                    func.lower(Crop.crop_name).ilike(f'%{variant.lower()}%'),
                    func.lower(Crop.normalized_name).ilike(f'%{variant.lower()}%'),
                    func.lower(Crop.common_synonyms).ilike(f'%{variant.lower()}%')
                )
            )
            result = await session.execute(crop_stmt)
            found_crops = result.scalars().all()

            if found_crops:
                matching_crops.extend(found_crops)
                break

        # Remove duplicates
        matching_crops = list({c.id: c for c in matching_crops}.values())

        if not matching_crops:
            # Get crop suggestions from database
            crop_suggestions = await cls._get_crop_suggestions(session, crop)
            return {
                "error": f"No crops found matching: {crop}",
                "suggestions": crop_suggestions[:5],
                "tip": "Try French crop names like 'blé' (wheat), 'maïs' (corn), 'colza' (rapeseed)"
            }

        crop_ids = [c.id for c in matching_crops]

        # Find products marked as MFSC type (fertilizers and soil improvers)
        fertilizer_stmt = (
            select(Product, ProductUse, Crop)
            .join(ProductUse, ProductUse.product_id == Product.id)
            .join(Crop, Crop.id == ProductUse.crop_id)
            .where(
                Crop.id.in_(crop_ids),
                or_(
                    Product.product_type.ilike('%MFSC%'),
                    Product.product_type.ilike('%fertilisant%'),
                    Product.product_type.ilike('%engrais%'),
                    Product.product_type.ilike('%amendement%'),
                    Product.function_category.ilike('%fertilisant%'),
                    Product.function_category.ilike('%engrais%'),
                    Product.function_category.ilike('%amendement%')
                ),
                ProductUse.is_currently_authorized == True
            )
        )

        # ENHANCED SEASON FILTERING with French season support
        if season:
            season_variants = cls._generate_search_variants(season)
            season_conditions = []

            for variant in season_variants:
                season_conditions.extend([
                    func.lower(ProductUse.application_season_min).ilike(f'%{variant.lower()}%'),
                    func.lower(ProductUse.application_season_max).ilike(f'%{variant.lower()}%'),
                    func.lower(ProductUse.application_comments).ilike(f'%{variant.lower()}%')
                ])

            if season_conditions:
                fertilizer_stmt = fertilizer_stmt.where(or_(*season_conditions))

        # Execute query
        result = await session.execute(fertilizer_stmt)
        fertilizers = result.all()

        # PROGRESSIVE RELAXATION: If no fertilizers found, try broader search
        if not fertilizers:
            # Strategy 1: Try without season filter if season was specified
            if season:
                relaxed_stmt = (
                    select(Product, ProductUse, Crop)
                    .join(ProductUse, ProductUse.product_id == Product.id)
                    .join(Crop, Crop.id == ProductUse.crop_id)
                    .where(
                        Crop.id.in_(crop_ids),
                        or_(
                            Product.product_type.ilike('%MFSC%'),
                            Product.product_type.ilike('%fertilisant%'),
                            Product.product_type.ilike('%engrais%'),
                            Product.product_type.ilike('%amendement%'),
                            Product.function_category.ilike('%fertilisant%'),
                            Product.function_category.ilike('%engrais%'),
                            Product.function_category.ilike('%amendement%')
                        ),
                        ProductUse.is_currently_authorized == True
                    )
                )
                relaxed_result = await session.execute(relaxed_stmt)
                fertilizers = relaxed_result.all()

            # Strategy 2: If still no results, try general fertilizer search
            if not fertilizers:
                general_stmt = select(Product).where(
                    or_(
                        Product.product_type.ilike('%MFSC%'),
                        Product.product_type.ilike('%fertilisant%'),
                        Product.product_type.ilike('%engrais%'),
                        Product.product_type.ilike('%amendement%'),
                        Product.function_category.ilike('%fertilisant%'),
                        Product.function_category.ilike('%engrais%'),
                        Product.function_category.ilike('%amendement%')
                    ),
                    Product.is_currently_authorized == True
                ).limit(10)

                result = await session.execute(general_stmt)
                general_fertilizers = result.scalars().all()

                # Handle case where no fertilizers found at all
                if not general_fertilizers:
                    return {
                        "error": f"No fertilizers found for crop: {crop}",
                        "crop_searched": matching_crops[0].crop_name if matching_crops else crop,
                        "tip": "Try searching for general fertilizers or check product categories like 'MFSC' or 'engrais'",
                        "suggestions": [
                            "Consider organic fertilizers",
                            "Check for soil amendment products",
                            "Look for NPK fertilizer formulations"
                        ]
                    }

                # Format general results
                general_results = []

                for product in general_fertilizers:
                    # Get compositions for this product using SQLAlchemy 2.0 style
                    comp_stmt = select(ProductComposition).where(
                        ProductComposition.product_id == product.id
                    )
                    comp_result = await session.execute(comp_stmt)
                    compositions = comp_result.scalars().all()

                    composition_list = [
                        {
                            "component_name": c.component_name,
                            "min_value": c.min_value,
                            "max_value": c.max_value,
                            "unit": c.unit,
                            "component_category": c.component_category
                        }
                        for c in compositions
                    ]

                    # Analyze nutrient content for each product
                    nutrient_analysis = cls._analyze_fertilizer_nutrients(compositions)
                    fertilizer_category = cls._categorize_fertilizer_by_nutrients(nutrient_analysis)

                    # Generate practical application guidance
                    application_guidance = cls._generate_fertilizer_application_guidance(
                        nutrient_analysis, crop, season, soil_characteristics
                    )

                    general_results.append({
                        "product_name": product.product_name,
                        "registration_number": product.registration_number,
                        "holder": product.holder,
                        "authorization_status": product.authorization_status,
                        "is_currently_authorized": product.is_currently_authorized,
                        "compositions": composition_list,
                        "class_denomination": compositions[0].class_denomination if compositions else None,
                        "product_summary": product.product_summary,
                        "nutrient_analysis": nutrient_analysis,
                        "fertilizer_category": fertilizer_category,
                        "application_guidance": application_guidance,
                        "suitability_score": cls._calculate_fertilizer_suitability_score(
                            nutrient_analysis, crop, season, soil_characteristics
                        ),
                        "note": f"Fertilisant {fertilizer_category.lower()} adapté pour {crop}. {application_guidance.get('key_benefit', '')}"
                    })

                # Sort general fertilizers by suitability score
                general_results.sort(key=lambda x: x.get('suitability_score', 0), reverse=True)

                # Group fertilizers by category for better organization
                categorized_fertilizers = cls._group_fertilizers_by_category(general_results)

                return {
                    "crop_name": matching_crops[0].crop_name,
                    "crop_nutritional_needs": cls._get_crop_nutritional_needs(crop),
                    "search_context": {
                        "season": season,
                        "soil_characteristics": soil_characteristics,
                        "search_strategy": "general_fertilizers"
                    },
                    "specific_fertilizers": [],
                    "general_fertilizers": general_results[:10],  # Limit to top 10
                    "fertilizers_by_category": categorized_fertilizers,
                    "application_recommendations": cls._generate_seasonal_application_recommendations(crop, season),
                    "note": f"Recommandations de fertilisants généraux pour {crop}. {len(general_results)} produits analysés et classés par pertinence."
                }

        # Group by product to avoid duplicates
        results_dict = {}

        for product, use, crop in fertilizers:
            if product.id not in results_dict:
                # Get compositions for this product using SQLAlchemy 2.0 style
                comp_stmt = select(ProductComposition).where(
                    ProductComposition.product_id == product.id
                )
                comp_result = await session.execute(comp_stmt)
                compositions = comp_result.scalars().all()

                composition_list = [
                    {
                        "component_name": c.component_name,
                        "min_value": c.min_value,
                        "max_value": c.max_value,
                        "unit": c.unit,
                        "component_category": c.component_category
                    }
                    for c in compositions
                ]

                # Initialize product entry
                results_dict[product.id] = {
                    "product_name": product.product_name,
                    "registration_number": product.registration_number,
                    "holder": product.holder,
                    "authorization_status": product.authorization_status,
                    "is_currently_authorized": product.is_currently_authorized,
                    "compositions": composition_list,
                    "class_denomination": compositions[0].class_denomination if compositions else None,
                    "uses": [],
                    "product_summary": product.product_summary
                }

            # Add usage details
            results_dict[product.id]["uses"].append({
                "crop_name": crop.crop_name,
                "usage_id": use.usage_id,
                "usage_description": use.usage_description,
                "application_details": {
                    "min_dose": use.min_dose,
                    "max_dose": use.max_dose,
                    "dose_unit": use.dose_unit,
                    "application_timing": {
                        "season_min": use.application_season_min,
                        "season_max": use.application_season_max,
                        "comments": use.application_comments
                    }
                }
            })

        # Analyze compositions for nutrient content
        for product_id, product_data in results_dict.items():
            nutrients = {
                "N": None,  # Nitrogen
                "P": None,  # Phosphorus
                "K": None,  # Potassium
                "Ca": None,  # Calcium
                "Mg": None,  # Magnesium
                "S": None,  # Sulfur
                "Fe": None,  # Iron
                "Zn": None,  # Zinc
                "Cu": None,  # Copper
                "Mn": None,  # Manganese
                "B": None,  # Boron
                "Mo": None  # Molybdenum
            }

            organic_matter = None

            for comp in product_data["compositions"]:
                component_name = comp["component_name"].lower()

                # Check for nitrogen
                if "azote" in component_name or " n " in component_name or component_name.startswith("n "):
                    nutrients["N"] = comp["min_value"]
                # Check for phosphorus
                elif "phosphore" in component_name or "p2o5" in component_name or " p " in component_name or component_name.startswith(
                        "p "):
                    nutrients["P"] = comp["min_value"]
                # Check for potassium
                elif "potassium" in component_name or "k2o" in component_name or " k " in component_name or component_name.startswith(
                        "k "):
                    nutrients["K"] = comp["min_value"]
                # Check for calcium
                elif "calcium" in component_name or "cao" in component_name or " ca " in component_name or component_name.startswith(
                        "ca "):
                    nutrients["Ca"] = comp["min_value"]
                # Check for magnesium
                elif "magnésium" in component_name or "mgo" in component_name or " mg " in component_name or component_name.startswith(
                        "mg "):
                    nutrients["Mg"] = comp["min_value"]
                # Check for sulfur
                elif "soufre" in component_name or "so3" in component_name or " s " in component_name or component_name.startswith(
                        "s "):
                    nutrients["S"] = comp["min_value"]
                # Check for organic matter
                elif "matière organique" in component_name or "humus" in component_name:
                    organic_matter = comp["min_value"]
                # Check for micronutrients
                elif "fer" in component_name or " fe " in component_name or component_name.startswith("fe "):
                    nutrients["Fe"] = comp["min_value"]
                elif "zinc" in component_name or " zn " in component_name or component_name.startswith("zn "):
                    nutrients["Zn"] = comp["min_value"]
                elif "cuivre" in component_name or " cu " in component_name or component_name.startswith("cu "):
                    nutrients["Cu"] = comp["min_value"]
                elif "manganèse" in component_name or " mn " in component_name or component_name.startswith("mn "):
                    nutrients["Mn"] = comp["min_value"]
                elif "bore" in component_name or " b " in component_name or component_name.startswith("b "):
                    nutrients["B"] = comp["min_value"]
                elif "molybdène" in component_name or " mo " in component_name or component_name.startswith("mo "):
                    nutrients["Mo"] = comp["min_value"]

            # Add nutrient analysis
            product_data["nutrient_analysis"] = {
                "NPK": {
                    "N": nutrients["N"],
                    "P": nutrients["P"],
                    "K": nutrients["K"]
                },
                "secondary_nutrients": {
                    "Ca": nutrients["Ca"],
                    "Mg": nutrients["Mg"],
                    "S": nutrients["S"]
                },
                "micronutrients": {
                    "Fe": nutrients["Fe"],
                    "Zn": nutrients["Zn"],
                    "Cu": nutrients["Cu"],
                    "Mn": nutrients["Mn"],
                    "B": nutrients["B"],
                    "Mo": nutrients["Mo"]
                },
                "organic_matter": organic_matter
            }

            # Generate NPK formula string
            npk_values = [nutrients["N"], nutrients["P"], nutrients["K"]]
            if all(v is not None for v in npk_values):
                npk_formula = f"NPK {nutrients['N']}-{nutrients['P']}-{nutrients['K']}"
                product_data["npk_formula"] = npk_formula

        # Prepare response
        response = {
            "crop_name": matching_crops[0].crop_name,
            "fertilizers": list(results_dict.values())
        }

        # Add soil-specific guidance if soil characteristics provided
        if soil_characteristics:
            soil_type = soil_characteristics.get("soil_type")
            ph = soil_characteristics.get("ph")

            soil_guidance = []

            if soil_type == "sandy":
                soil_guidance.append(
                    "Sur sol sableux, privilégiez les apports fractionnés pour éviter le lessivage des éléments nutritifs.")
            elif soil_type == "clay":
                soil_guidance.append(
                    "Sur sol argileux, privilégiez les applications en période sèche pour éviter le compactage.")
            elif soil_type == "calcareous":
                soil_guidance.append(
                    "Sur sol calcaire, surveillez les apports en fer et manganèse qui peuvent être bloqués.")

            if ph is not None:
                if ph < 5.5:
                    soil_guidance.append("Sol acide: privilégiez les amendements calciques pour remonter le pH.")
                elif ph > 7.5:
                    soil_guidance.append(
                        "Sol basique: surveillez les apports en fer et microéléments qui peuvent être moins disponibles.")

            if soil_guidance:
                response["soil_specific_guidance"] = soil_guidance

        return response

    @classmethod
    def _analyze_fertilizer_nutrients(cls, compositions: List) -> Dict[str, Any]:
        """Analyze nutrient content from product compositions"""
        nutrients = {
            "N": None, "P": None, "K": None,
            "Ca": None, "Mg": None, "S": None,
            "Fe": None, "Zn": None, "Cu": None, "Mn": None, "B": None, "Mo": None
        }
        organic_matter = None

        for comp in compositions:
            component_name = comp.component_name.lower()

            # Primary nutrients (NPK)
            if any(term in component_name for term in ["azote", " n ", "nitrogen"]) and not nutrients["N"]:
                nutrients["N"] = comp.min_value
            elif any(term in component_name for term in ["phosphore", "p2o5", " p "]) and not nutrients["P"]:
                nutrients["P"] = comp.min_value
            elif any(term in component_name for term in ["potassium", "k2o", " k "]) and not nutrients["K"]:
                nutrients["K"] = comp.min_value

            # Secondary nutrients
            elif any(term in component_name for term in ["calcium", "cao", " ca "]) and not nutrients["Ca"]:
                nutrients["Ca"] = comp.min_value
            elif any(term in component_name for term in ["magnésium", "mgo", " mg "]) and not nutrients["Mg"]:
                nutrients["Mg"] = comp.min_value
            elif any(term in component_name for term in ["soufre", "so3", " s "]) and not nutrients["S"]:
                nutrients["S"] = comp.min_value

            # Micronutrients
            elif any(term in component_name for term in ["fer", " fe "]) and not nutrients["Fe"]:
                nutrients["Fe"] = comp.min_value
            elif any(term in component_name for term in ["zinc", " zn "]) and not nutrients["Zn"]:
                nutrients["Zn"] = comp.min_value
            elif any(term in component_name for term in ["cuivre", " cu "]) and not nutrients["Cu"]:
                nutrients["Cu"] = comp.min_value
            elif any(term in component_name for term in ["manganèse", " mn "]) and not nutrients["Mn"]:
                nutrients["Mn"] = comp.min_value
            elif any(term in component_name for term in ["bore", " b "]) and not nutrients["B"]:
                nutrients["B"] = comp.min_value
            elif any(term in component_name for term in ["molybdène", " mo "]) and not nutrients["Mo"]:
                nutrients["Mo"] = comp.min_value

            # Organic matter
            elif any(term in component_name for term in ["matière organique", "humus", "carbone organique"]):
                organic_matter = comp.min_value

        return {
            "NPK": {"N": nutrients["N"], "P": nutrients["P"], "K": nutrients["K"]},
            "secondary_nutrients": {"Ca": nutrients["Ca"], "Mg": nutrients["Mg"], "S": nutrients["S"]},
            "micronutrients": {"Fe": nutrients["Fe"], "Zn": nutrients["Zn"], "Cu": nutrients["Cu"],
                               "Mn": nutrients["Mn"], "B": nutrients["B"], "Mo": nutrients["Mo"]},
            "organic_matter": organic_matter,
            "npk_formula": cls._generate_npk_formula(nutrients["N"], nutrients["P"], nutrients["K"])
        }

    @classmethod
    def _categorize_fertilizer_by_nutrients(cls, nutrient_analysis: Dict[str, Any]) -> str:
        """Categorize fertilizer based on nutrient content"""
        npk = nutrient_analysis["NPK"]
        organic_matter = nutrient_analysis["organic_matter"]
        micronutrients = nutrient_analysis["micronutrients"]

        # Count available NPK nutrients
        npk_count = sum(1 for v in npk.values() if v is not None and v > 0)
        micro_count = sum(1 for v in micronutrients.values() if v is not None and v > 0)

        if organic_matter and organic_matter > 10:
            return "Engrais organique"
        elif npk_count >= 3:
            return "Engrais NPK complexe"
        elif npk["N"] and npk["N"] > 15:
            return "Engrais azoté"
        elif npk["P"] and npk["P"] > 10:
            return "Engrais phosphaté"
        elif npk["K"] and npk["K"] > 15:
            return "Engrais potassique"
        elif micro_count >= 2:
            return "Engrais micronutriments"
        else:
            return "Amendement du sol"

    @classmethod
    def _generate_fertilizer_application_guidance(cls, nutrient_analysis: Dict[str, Any],
                                                  crop: str, season: str,
                                                  soil_characteristics: Optional[Dict]) -> Dict[str, str]:
        """Generate practical application guidance"""
        npk = nutrient_analysis["NPK"]
        guidance = {}

        # Key benefit based on nutrient profile
        if npk["N"] and npk["N"] > 15:
            guidance["key_benefit"] = "Stimule la croissance végétative et le développement foliaire"
            guidance["application_timing"] = "Début de saison de croissance"
        elif npk["P"] and npk["P"] > 10:
            guidance["key_benefit"] = "Favorise le développement racinaire et la floraison"
            guidance["application_timing"] = "À la plantation ou en début de cycle"
        elif npk["K"] and npk["K"] > 15:
            guidance["key_benefit"] = "Améliore la résistance au stress et la qualité des fruits"
            guidance["application_timing"] = "Milieu à fin de cycle cultural"
        else:
            guidance["key_benefit"] = "Améliore la structure du sol et la nutrition générale"
            guidance["application_timing"] = "Selon les besoins de la culture"

        # Season-specific advice
        if season == "printemps":
            guidance["seasonal_advice"] = "Application avant le démarrage végétatif"
        elif season == "été":
            guidance["seasonal_advice"] = "Fractionnez les apports pour éviter le lessivage"
        elif season == "automne":
            guidance["seasonal_advice"] = "Apport de fond pour la prochaine saison"
        else:
            guidance["seasonal_advice"] = "Respectez les périodes d'application recommandées"

        return guidance

    @classmethod
    def _calculate_fertilizer_suitability_score(cls, nutrient_analysis: Dict[str, Any],
                                                crop: str, season: str,
                                                soil_characteristics: Optional[Dict]) -> float:
        """Calculate suitability score (0-1) for fertilizer recommendation"""
        score = 0.5  # Base score

        crop_needs = cls._get_crop_nutritional_needs(crop)
        npk = nutrient_analysis["NPK"]

        # Boost score based on crop-nutrient matching
        if crop_needs.get("nitrogen_priority") == "high" and npk["N"] and npk["N"] > 10:
            score += 0.2
        if crop_needs.get("phosphorus_priority") == "high" and npk["P"] and npk["P"] > 8:
            score += 0.2
        if crop_needs.get("potassium_priority") == "high" and npk["K"] and npk["K"] > 10:
            score += 0.2

        # Season matching
        if season == "printemps" and npk["N"]:
            score += 0.1
        elif season == "automne" and (npk["P"] or npk["K"]):
            score += 0.1

        return min(score, 1.0)

    @classmethod
    def _group_fertilizers_by_category(cls, fertilizers: List[Dict]) -> Dict[str, List[Dict]]:
        """Group fertilizers by their category"""
        categories = {}
        for fertilizer in fertilizers:
            category = fertilizer.get("fertilizer_category", "Autres")
            if category not in categories:
                categories[category] = []
            categories[category].append(fertilizer)
        return categories

    @classmethod
    def _get_crop_nutritional_needs(cls, crop: str) -> Dict[str, str]:
        """Get nutritional priorities for specific crops"""
        crop_needs = {
            "blé": {"nitrogen_priority": "high", "phosphorus_priority": "medium", "potassium_priority": "medium"},
            "maïs": {"nitrogen_priority": "very_high", "phosphorus_priority": "high", "potassium_priority": "high"},
            "colza": {"nitrogen_priority": "high", "phosphorus_priority": "medium", "potassium_priority": "high"},
            "betterave": {"nitrogen_priority": "medium", "phosphorus_priority": "high", "potassium_priority": "high"},
            "pomme de terre": {"nitrogen_priority": "medium", "phosphorus_priority": "medium",
                               "potassium_priority": "very_high"},
            "tournesol": {"nitrogen_priority": "medium", "phosphorus_priority": "high", "potassium_priority": "high"},
        }
        return crop_needs.get(crop.lower(), {"nitrogen_priority": "medium", "phosphorus_priority": "medium",
                                             "potassium_priority": "medium"})

    @classmethod
    def _generate_seasonal_application_recommendations(cls, crop: str, season: str) -> Dict[str, str]:
        """Generate seasonal application recommendations"""
        recommendations = {}

        if season == "printemps":
            recommendations["timing"] = "Apport de base au démarrage végétatif"
            recommendations["nutrients"] = "Privilégier l'azote pour la croissance"
            recommendations["precautions"] = "Surveiller les conditions météo pour éviter le lessivage"
        elif season == "été":
            recommendations["timing"] = "Apports fractionnés selon les besoins"
            recommendations["nutrients"] = "Azote et potassium pour soutenir la croissance"
            recommendations["precautions"] = "Éviter les périodes de forte chaleur"
        elif season == "automne":
            recommendations["timing"] = "Apport de fond après récolte"
            recommendations["nutrients"] = "Phosphore et potassium pour préparer la prochaine saison"
            recommendations["precautions"] = "Incorporer avant les gelées"
        else:
            recommendations["timing"] = "Selon le stade cultural de la plante"
            recommendations["nutrients"] = "Adapter selon les besoins spécifiques"
            recommendations["precautions"] = "Respecter les bonnes pratiques agricoles"

        return recommendations

    @classmethod
    def _generate_npk_formula(cls, n: float, p: float, k: float) -> str:
        """Generate NPK formula string"""
        if all(v is not None for v in [n, p, k]):
            return f"NPK {int(n)}-{int(p)}-{int(k)}"
        return "Composition variable"

    @classmethod
    async def find_compatible_products(cls,
                                       session: AsyncSession,
                                       product_identifier: str,
                                       product_type: Optional[str] = None,
                                       only_authorized: bool = True) -> Dict[str, Any]:
        """
        Find products compatible for tank mixing with enhanced safety guidance.

        Identifies products that can be safely mixed with the
        specified product. Provides detailed mixing instructions based on formulation
        types and chemical compatibility analysis.

        Args:
            product_identifier: Base product for compatibility search (registration number or name)
            product_type: Type of compatible product to find (e.g., "Herbicide", "Fongicide") 
            only_authorized: Whether to only include currently authorized products

        Returns:
            List of compatible products with mixing guidance and precautions
            
        Tips for AI agents:
        - Use specific product names or registration numbers for better results
        - Try French function names: 'Herbicide', 'Fongicide', 'Insecticide'
        - Consider formulation compatibility (SC, EC, WP, WG formulations)
        """

        # Find product
        product = await cls._find_product(session, product_identifier)

        if not product:
            # Enhanced error handling with suggestions
            product_suggestions = await cls._get_product_suggestions(session, product_identifier)
            return {
                "error": f"Product not found: {product_identifier}",
                "suggestions": product_suggestions,
                "tip": "Try specific product names like 'Prosaro' or registration numbers like '8800475'"
            }

        # Determine primary function
        primary_function = None
        for function in ['Herbicide', 'Fongicide', 'Insecticide', 'Régulateur']:
            if function.lower() in product.function_category.lower():
                primary_function = function
                break

        if not primary_function:
            primary_function = "Autre"

        # Get compatibility guidance based on product type
        compatibility_types = {}
        if primary_function in cls.compatibility_matrix:
            for other_type, compatibility in cls.compatibility_matrix[primary_function].items():
                compatibility_types[other_type] = compatibility

        # Find formulation compatibility guidance
        formulation_guidance = None
        if product.formulation_type:
            formulation_codes = ["SC", "EC", "WP", "WG", "SL", "OD", "CS"]
            for code in formulation_codes:
                if code in product.formulation_type:
                    if code == "SC":
                        formulation_guidance = "SC (Suspension Concentrée): Ajouter en premier dans la cuve."
                    elif code == "EC":
                        formulation_guidance = "EC (Concentré Émulsionnable): Ajouter après les SC et les WG/WP."
                    elif code == "WP":
                        formulation_guidance = "WP (Poudre Mouillable): Pré-mélanger avant d'ajouter au réservoir, après les SC."
                    elif code == "WG":
                        formulation_guidance = "WG (Granulés Dispersibles): Ajouter après les SC et avant les EC."
                    elif code == "SL":
                        formulation_guidance = "SL (Concentré Soluble): Ajouter après les WG/WP et avant les EC."
                    elif code == "OD":
                        formulation_guidance = "OD (Dispersion Huileuse): Vérifier compatibilité. Risque de séparation avec certains produits."
                    elif code == "CS":
                        formulation_guidance = "CS (Suspension de Capsules): Ajouter après les WG et avant les EC."
                    break

        # Build query for finding compatible products using SQLAlchemy 2.0
        compatible_products_stmt = select(Product)

        # Filter by authorization if requested
        if only_authorized:
            compatible_products_stmt = compatible_products_stmt.where(
                Product.is_currently_authorized == True
            )

        # Filter by product type if specified
        if product_type:
            type_variants = cls._generate_search_variants(product_type)
            type_conditions = []

            for variant in type_variants:
                type_conditions.append(
                    func.lower(Product.function_category).ilike(f'%{variant.lower()}%')
                )

            compatible_products_stmt = compatible_products_stmt.where(
                or_(*type_conditions)
            )

        # Exclude the base product itself
        compatible_products_stmt = compatible_products_stmt.where(
            Product.id != product.id
        )

        # Execute query with smart limiting
        compatible_products_result = await session.execute(compatible_products_stmt.limit(100))
        all_products = compatible_products_result.scalars().all()

        # Handle case where no products found
        if not all_products:
            # Progressive relaxation: try without product type filter
            if product_type:
                relaxed_stmt = select(Product).where(
                    Product.is_currently_authorized == True if only_authorized else True,
                    Product.id != product.id
                ).limit(20)

                relaxed_result = await session.execute(relaxed_stmt)
                all_products = relaxed_result.scalars().all()

                if all_products:
                    return {
                        "base_product": {
                            "product_name": product.product_name,
                            "registration_number": product.registration_number,
                            "function_category": product.function_category
                        },
                        "note": f"No products of type '{product_type}' found. Showing general products for reference.",
                        "general_products": [{
                            "product_name": p.product_name,
                            "registration_number": p.registration_number,
                            "function_category": p.function_category,
                            "formulation_type": p.formulation_type
                        } for p in all_products[:10]],
                        "tip": "Consider checking product labels for specific compatibility information."
                    }

            return {
                "error": f"No compatible products found for {product.product_name}",
                "base_product": {
                    "product_name": product.product_name,
                    "function_category": product.function_category
                },
                "tip": "Try searching for specific product types like 'Herbicide' or 'Fongicide'"
            }

        # Categorize products by compatibility
        compatible_products = []
        variable_compatibility_products = []
        incompatible_products = []

        for other_product in all_products:
            # Determine other product's function
            other_function = None
            for function in ['Herbicide', 'Fongicide', 'Insecticide', 'Régulateur', 'Engrais']:
                if function.lower() in other_product.function_category.lower():
                    other_function = function
                    break

            if not other_function:
                other_function = "Autre"

            # Determine compatibility
            compatibility = None
            if other_function in compatibility_types:
                compatibility = compatibility_types[other_function]

            # Add to appropriate list
            product_entry = {
                "product_name": other_product.product_name,
                "registration_number": other_product.registration_number,
                "function_category": other_product.function_category,
                "formulation_type": other_product.formulation_type,
                "is_currently_authorized": other_product.is_currently_authorized
            }

            if compatibility == "bonne":
                compatible_products.append(product_entry)
            elif compatibility == "variable":
                variable_compatibility_products.append(product_entry)
            elif compatibility == "déconseillée":
                incompatible_products.append(product_entry)
            else:
                # Default to variable compatibility
                variable_compatibility_products.append(product_entry)

        # Prepare response
        response = {
            "base_product": {
                "product_name": product.product_name,
                "registration_number": product.registration_number,
                "function_category": product.function_category,
                "formulation_type": product.formulation_type
            },
            "mixing_guidance": {
                "formulation_guidance": formulation_guidance,
                "general_precautions": [
                    "Toujours effectuer un test de compatibilité physique avant de procéder au mélange en cuve à grande échelle.",
                    "Respecter l'ordre d'incorporation des produits dans la cuve selon leur formulation.",
                    "Maintenir une agitation constante pendant la préparation et l'application."
                ]
            },
            "compatible_products": compatible_products,
            "variable_compatibility_products": variable_compatibility_products,
            "incompatible_products": incompatible_products
        }

        return response

    @classmethod
    async def get_application_timing_guidance(cls,
                                              session: AsyncSession,
                                              product_identifier: str,
                                              crop: str,
                                              region: Optional[str] = None) -> Dict[str, Any]:
        """
        Retrieve optimal application timing recommendations with seasonal context.

        Provides detailed guidance on when to apply a product based on crop
        growth stages, seasons, and environmental conditions. Includes buffer
        zone and weather condition recommendations.

        Args:
            product_identifier: Product registration number or name
            crop: Crop name for specific timing recommendations (French terms like 'blé', 'maïs', 'colza' work well)
            region: Optional region for climate-adapted recommendations

        Returns:
            Comprehensive application timing guidance with practical context
            
        Tips for AI agents:
        - Use specific French crop names: 'blé' (wheat), 'maïs' (corn), 'colza' (rapeseed)
        - Try both French and English terms if first attempt fails
        - Include registration numbers when known for better product matching
        """
        # Find product using enhanced search
        product = await cls._find_product(session, product_identifier)

        if not product:
            # Try enhanced product search with suggestions
            product_suggestions = await cls._get_product_suggestions(session, product_identifier)
            return {
                "error": f"Product not found: {product_identifier}",
                "suggestions": product_suggestions,
                "tip": "Try registration numbers like '8800475' or specific product names like 'Prosaro'"
            }

        # ENHANCED CROP SEARCH with multi-strategy approach
        crop_variants = cls._generate_search_variants(crop)
        matching_crops = []

        for variant in crop_variants:
            crops_stmt = select(Crop).where(
                or_(
                    func.lower(Crop.crop_name).ilike(f'%{variant.lower()}%'),
                    func.lower(Crop.normalized_name).ilike(f'%{variant.lower()}%'),
                    func.lower(Crop.common_synonyms).ilike(f'%{variant.lower()}%')
                )
            )
            crops_result = await session.execute(crops_stmt)
            found_crops = crops_result.scalars().all()

            if found_crops:
                matching_crops.extend(found_crops)
                break

        # Remove duplicates
        matching_crops = list({c.id: c for c in matching_crops}.values())

        if not matching_crops:
            # Get crop suggestions from database
            crop_suggestions = await cls._get_crop_suggestions(session, crop)
            return {
                "error": f"No crops found matching: {crop}",
                "suggestions": crop_suggestions[:5],
                "tip": "Try French crop names like 'blé', 'maïs', 'colza' or English equivalents"
            }

        # Get product uses for this crop
        uses_stmt = select(
            ProductUse,
            Crop,
            Target
        ).join(
            Crop, Crop.id == ProductUse.crop_id
        ).outerjoin(
            Target, Target.id == ProductUse.target_id
        ).where(
            ProductUse.product_id == product.id,
            Crop.id.in_([c.id for c in matching_crops]),
            ProductUse.is_currently_authorized.is_(True)
        )
        uses_result = await session.execute(uses_stmt)
        crop_uses = uses_result.all()

        if not crop_uses:
            # Progressive relaxation: find alternative uses for this product
            alt_crops_stmt = select(distinct(Crop.crop_name)).join(
                ProductUse, ProductUse.crop_id == Crop.id
            ).where(
                ProductUse.product_id == product.id,
                ProductUse.is_currently_authorized.is_(True)
            ).limit(5)
            alt_crops_result = await session.execute(alt_crops_stmt)
            alternative_crops = [row[0] for row in alt_crops_result.all()]

            return {
                "error": f"No authorized uses found for {product.product_name} on {crop}",
                "product_info": {
                    "name": product.product_name,
                    "registration_number": product.registration_number,
                    "function_category": product.function_category
                },
                "alternative_crops": alternative_crops,
                "tip": "This product is authorized for other crops. Consider checking product label for specific usage instructions."
            }

        # Process uses to extract timing information
        timing_by_target = {}

        for use, crop, target in crop_uses:
            target_key = target.target_name if target else "General"

            if target_key not in timing_by_target:
                timing_by_target[target_key] = {
                    "target_name": target_key,
                    "target_type": target.target_type if target else None,
                    "timing_info": []
                }

            # Extract growth stage information
            growth_stage_info = None
            if use.min_growth_stage is not None and use.max_growth_stage is not None:
                growth_stage_info = {
                    "bbch_range": f"BBCH {use.min_growth_stage}-{use.max_growth_stage}",
                    "description": cls._format_growth_stage(use.min_growth_stage, use.max_growth_stage)
                }

            # Extract seasonal information
            seasonal_info = None
            if use.application_season_min or use.application_season_max:
                seasonal_info = {
                    "season_min": use.application_season_min,
                    "season_max": use.application_season_max,
                    "comments": use.application_comments
                }

            # Add timing information
            timing_by_target[target_key]["timing_info"].append({
                "usage_id": use.usage_id,
                "usage_description": use.usage_description,
                "growth_stage": growth_stage_info,
                "seasonal_timing": seasonal_info,
                "application_part": use.application_part,
                "max_applications": use.max_applications,
                "min_interval_between_applications": use.min_interval_between_applications,
                "harvest_interval_days": use.harvest_interval_days
            })

        # Generate optimal conditions based on product type
        optimal_conditions = []

        # Check if product is a herbicide
        if "herbicide" in product.function_category.lower():
            optimal_conditions.extend([
                "Appliquer sur végétation sèche pour une meilleure absorption foliaire",
                "Éviter les applications par temps venteux (dérive) ou avant une pluie prévue dans les 6 heures",
                "Température idéale: 10-25°C (efficacité réduite en dessous de 8°C ou au-dessus de 28°C)"
            ])

        # Check if product is a fungicide
        elif "fongicide" in product.function_category.lower():
            optimal_conditions.extend([
                "Appliquer de préférence préventivement, avant l'apparition des symptômes",
                "Traiter par temps sec, éviter les périodes de rosée ou d'humidité prolongée sur le feuillage",
                "Assurer une bonne couverture de la végétation pour une protection optimale"
            ])

        # Check if product is an insecticide
        elif "insecticide" in product.function_category.lower():
            optimal_conditions.extend([
                "Traiter dès l'observation des premiers individus pour les insectes à multiplication rapide",
                "Éviter les heures les plus chaudes de la journée, préférer le matin ou le soir",
                "Surveiller les conditions d'irrigation qui peuvent affecter l'efficacité du produit"
            ])

        # Add buffer zone recommendations
        buffer_zones = [use.max_buffer_zone for use, _, _ in crop_uses if use.max_buffer_zone is not None]
        if buffer_zones:
            max_buffer = max(buffer_zones)
            optimal_conditions.append(
                f"Respecter une zone non traitée de {max_buffer} mètres par rapport aux points d'eau")

        # Add region-specific recommendations if available
        if region:
            region_lower = region.lower()
            if "nord" in region_lower:
                optimal_conditions.append(
                    "En région Nord, prévoir un délai supplémentaire pour les applications de printemps en raison des températures plus basses")
            elif "sud" in region_lower:
                optimal_conditions.append(
                    "En région Sud, surveiller les conditions de sécheresse qui peuvent réduire l'efficacité du traitement")
            elif "ouest" in region_lower:
                optimal_conditions.append(
                    "En région Ouest, tenir compte de l'humidité atmosphérique élevée qui peut influencer l'absorption du produit")
            elif "est" in region_lower:
                optimal_conditions.append(
                    "En région Est, attention aux amplitudes thermiques qui peuvent affecter la persistance du produit")

        # Generate timing summary
        timing_summary = f"Pour {product.product_name} sur {matching_crops[0].crop_name}, "

        growth_stages = []
        for target_data in timing_by_target.values():
            for timing in target_data["timing_info"]:
                if timing["growth_stage"]:
                    growth_stages.append(timing["growth_stage"]["bbch_range"])

        if growth_stages:
            unique_stages = list(set(growth_stages))
            timing_summary += f"appliquer aux stades {', '.join(unique_stages)}. "

        seasons = []
        for target_data in timing_by_target.values():
            for timing in target_data["timing_info"]:
                if timing["seasonal_timing"]:
                    if timing["seasonal_timing"]["season_min"]:
                        seasons.append(timing["seasonal_timing"]["season_min"])
                    if timing["seasonal_timing"]["season_max"]:
                        seasons.append(timing["seasonal_timing"]["season_max"])

        if seasons:
            unique_seasons = list(set(seasons))
            timing_summary += f"Périodes recommandées: {', '.join(unique_seasons)}. "

        # Add max applications info
        max_apps = []
        for target_data in timing_by_target.values():
            for timing in target_data["timing_info"]:
                if timing["max_applications"]:
                    max_apps.append(timing["max_applications"])

        if max_apps:
            max_app = max(max_apps)
            timing_summary += f"Maximum {max_app} applications par saison. "

        # Add harvest interval info
        harvest_intervals = []
        for target_data in timing_by_target.values():
            for timing in target_data["timing_info"]:
                if timing["harvest_interval_days"]:
                    harvest_intervals.append(timing["harvest_interval_days"])

        if harvest_intervals:
            max_interval = max(harvest_intervals)
            timing_summary += f"Respecter un délai avant récolte de {max_interval} jours."

        # Prepare response
        response = {
            "product_info": {
                "product_name": product.product_name,
                "registration_number": product.registration_number,
                "function_category": product.function_category
            },
            "crop_info": {
                "crop_name": matching_crops[0].crop_name,
                "crop_category": matching_crops[0].crop_category
            },
            "timing_by_target": list(timing_by_target.values()),
            "optimal_conditions": optimal_conditions,
            "timing_summary": timing_summary
        }

        return response

    @classmethod
    async def check_organic_farming_suitability(cls,
                                                session: AsyncSession,
                                                product_identifier: Optional[str] = None,
                                                crop: Optional[str] = None,
                                                pest: Optional[str] = None) -> Dict[str, Any]:
        """
        Determine suitability for organic farming with certification context.

        Analyzes product composition to assess potential suitability for
        organic farming. Can identify all organic-compatible solutions for
        a specific crop-pest combination.

        Args:
            product_identifier: Optional product to check
            crop: Optional crop to find organic solutions for
            pest: Optional pest to target with organic solutions

        Returns:
            Suitability assessment with relevant organic farming context
        """
        # Case 1: Check specific product
        if product_identifier:
            product = await cls._find_product(session, product_identifier)

            if not product:
                return {"error": f"Product not found: {product_identifier}"}

            # Get active substances for this product using SQLAlchemy 2.0 style
            substances_stmt = select(ActiveSubstance).join(
                ProductSubstance, ProductSubstance.substance_id == ActiveSubstance.id
            ).where(ProductSubstance.product_id == product.id)

            substances_result = await session.execute(substances_stmt)
            substances = substances_result.scalars().all()

            # Assess suitability
            assessment = cls._assess_organic_farming_suitability_enhanced(product, substances)

            response = {
                "product_info": {
                    "product_name": product.product_name,
                    "registration_number": product.registration_number,
                    "function_category": product.function_category,
                    "authorization_status": product.authorization_status,
                    "is_currently_authorized": product.is_currently_authorized
                },
                "organic_assessment": assessment,
                "_citation": {
                    "source": "E-Phy",
                    "url": "https://ephy.anses.fr"
                }
            }

            return response

        # Case 2: Find organic solutions for crop/pest
        elif crop or pest:
            # Find matching crops if specified
            matching_crops = None
            if crop:
                crop_query = crop.strip().lower()
                crops_stmt = select(Crop).where(
                    or_(
                        func.lower(Crop.crop_name).ilike(f'%{crop_query}%'),
                        func.lower(Crop.normalized_name).ilike(f'%{crop_query}%'),
                        func.lower(Crop.common_synonyms).ilike(f'%{crop_query}%')
                    )
                )
                crops_result = await session.execute(crops_stmt)
                matching_crops = crops_result.scalars().all()

                if not matching_crops:
                    return {"error": f"No crops found matching: {crop}"}

            # Find matching targets if specified
            matching_targets = None
            if pest:
                pest_query = pest.strip().lower()
                targets_stmt = select(Target).where(
                    or_(
                        func.lower(Target.target_name).ilike(f'%{pest_query}%'),
                        func.lower(Target.normalized_name).ilike(f'%{pest_query}%'),
                        func.lower(Target.common_synonyms).ilike(f'%{pest_query}%')
                    )
                )
                targets_result = await session.execute(targets_stmt)
                matching_targets = targets_result.scalars().all()

                if not matching_targets:
                    return {"error": f"No targets found matching: {pest}"}

            # Find all authorized products
            products_stmt = select(Product).where(
                Product.is_currently_authorized == True
            )

            # Apply crop filter if specified
            if matching_crops:
                crop_products_stmt = select(ProductUse.product_id).where(
                    ProductUse.crop_id.in_([c.id for c in matching_crops]),
                    ProductUse.is_currently_authorized == True
                ).distinct()

                crop_products_result = await session.execute(crop_products_stmt)
                crop_product_ids = [row[0] for row in crop_products_result.all()]

                products_stmt = products_stmt.where(Product.id.in_(crop_product_ids))

            # Apply pest filter if specified
            if matching_targets:
                pest_products_stmt = select(ProductUse.product_id).where(
                    ProductUse.target_id.in_([t.id for t in matching_targets]),
                    ProductUse.is_currently_authorized == True
                ).distinct()

                pest_products_result = await session.execute(pest_products_stmt)
                pest_product_ids = [row[0] for row in pest_products_result.all()]

                products_stmt = products_stmt.where(Product.id.in_(pest_product_ids))

            # Execute query
            products_result = await session.execute(products_stmt)
            all_products = products_result.scalars().all()

            # Assess each product
            organic_products = []

            for product in all_products:
                # Get active substances using SQLAlchemy 2.0 style
                substances_stmt = select(ActiveSubstance).join(
                    ProductSubstance, ProductSubstance.substance_id == ActiveSubstance.id
                ).where(ProductSubstance.product_id == product.id)

                substances_result = await session.execute(substances_stmt)
                substances = substances_result.scalars().all()

                # Assess suitability
                assessment = cls._assess_organic_farming_suitability_enhanced(product, substances)

                if assessment["potentially_suitable"]:
                    # Get usage details for this product using SQLAlchemy 2.0 style
                    usage_stmt = select(
                        ProductUse,
                        Crop,
                        Target
                    ).join(
                        Crop, Crop.id == ProductUse.crop_id
                    ).outerjoin(
                        Target, Target.id == ProductUse.target_id
                    ).where(
                        ProductUse.product_id == product.id,
                        ProductUse.is_currently_authorized == True
                    )

                    # Apply crop filter
                    if matching_crops:
                        usage_stmt = usage_stmt.where(
                            Crop.id.in_([c.id for c in matching_crops])
                        )

                    # Apply pest filter
                    if matching_targets:
                        usage_stmt = usage_stmt.where(
                            Target.id.in_([t.id for t in matching_targets])
                        )

                    # Execute usage query
                    usage_result = await session.execute(usage_stmt)
                    usages = usage_result.all()

                    if usages:
                        usage_details = []

                        for use, use_crop, use_target in usages:
                            usage_details.append({
                                "crop_name": use_crop.crop_name,
                                "target_name": use_target.target_name if use_target else "Non spécifié",
                                "min_dose": use.min_dose,
                                "max_dose": use.max_dose,
                                "dose_unit": use.dose_unit,
                                "application_part": use.application_part
                            })

                        organic_products.append({
                            "product_name": product.product_name,
                            "registration_number": product.registration_number,
                            "function_category": product.function_category,
                            "organic_assessment": assessment,
                            "usage_details": usage_details
                        })

            # Prepare response
            response = {
                "search_criteria": {
                    "crop": matching_crops[0].crop_name if matching_crops else None,
                    "pest": matching_targets[0].target_name if matching_targets else None
                },
                "organic_products": organic_products,
                "disclaimer": "Ces produits sont potentiellement utilisables en agriculture biologique selon leur composition, mais doivent être vérifiés auprès des organismes certificateurs.",
                "_citation": {
                    "source": "E-Phy",
                    "url": "https://ephy.anses.fr"
                }
            }

            return response

        # Case 3: No criteria provided
        else:
            return {
                "error": "Please provide either a product identifier, crop name, or pest name to assess organic farming suitability."
            }

    @classmethod
    async def get_environmental_impact_information(cls, session: AsyncSession, product_identifier: str) -> Dict[
        str, Any]:
        """
        Retrieve environmental impact information with protection measures.

        Provides detailed information on environmental risks and required
        protection measures including buffer zones, groundwater advisories,
        and ecological considerations.

        Args:
            product_identifier: Product registration number or name

        Returns:
            Environmental impact assessment with practical protection guidance
        """
        # Find product
        product = await cls._find_product(session, product_identifier)

        if not product:
            return {
                "error": f"Product not found: {product_identifier}",
                "_citation": {"source": "E-Phy", "url": "https://ephy.anses.fr"}
            }

        # Get hazard information
        hazards_query = select(ProductHazard).where(
            ProductHazard.product_id == product.id
        )
        hazards_result = await session.execute(hazards_query)
        hazards = hazards_result.scalars().all()

        # Get usage conditions
        conditions_query = select(UsageCondition).where(
            UsageCondition.product_id == product.id
        )
        conditions_result = await session.execute(conditions_query)
        conditions = conditions_result.scalars().all()

        # Get buffer zones from uses
        uses_query = select(ProductUse).where(
            ProductUse.product_id == product.id,
            ProductUse.is_currently_authorized == True
        )
        uses_result = await session.execute(uses_query)
        uses = uses_result.scalars().all()

        # Extract maximum buffer zones
        max_aquatic_zone = max([u.aquatic_buffer_zone for u in uses if u.aquatic_buffer_zone is not None], default=0)
        max_arthropod_zone = max([u.arthropod_buffer_zone for u in uses if u.arthropod_buffer_zone is not None],
                                 default=0)
        max_plant_zone = max([u.plant_buffer_zone for u in uses if u.plant_buffer_zone is not None], default=0)
        overall_max_zone = max([u.max_buffer_zone for u in uses if u.max_buffer_zone is not None], default=0)

        # Filter environmental hazards
        environmental_hazards = [h for h in hazards if h.hazard_category and (
                "environnement" in h.hazard_category.lower() or "aquatique" in h.hazard_category.lower())]

        # Filter environmental conditions
        environmental_conditions = []
        for condition in conditions:
            condition_text = (condition.condition_category + " " + condition.condition_description).lower()
            if any(keyword in condition_text for keyword in
                   ["environnement", "aquatique", "eau", "zone", "tampon", "znt", "écologique", "biodiversité"]):
                environmental_conditions.append(condition)

        # Find crops with special environmental requirements
        crops_with_requirements = {}

        for use in uses:
            if use.max_buffer_zone and use.max_buffer_zone > 0:
                crop_query = select(Crop).where(Crop.id == use.crop_id)
                crop_result = await session.execute(crop_query)
                crop = crop_result.scalar_one_or_none()
                if crop:
                    crop_name = crop.crop_name
                    if crop_name not in crops_with_requirements:
                        crops_with_requirements[crop_name] = {
                            "crop_name": crop_name,
                            "buffer_zone": use.max_buffer_zone,
                            "special_requirements": []
                        }

                    if use.application_conditions:
                        crops_with_requirements[crop_name]["special_requirements"].append(use.application_conditions)

        # Generate environmental summary
        environmental_summary = f"Le produit {product.product_name} "

        if environmental_hazards:
            environmental_summary += f"présente {len(environmental_hazards)} mentions de danger pour l'environnement. "
        else:
            environmental_summary += "ne présente pas de mentions de danger spécifiques pour l'environnement. "

        if overall_max_zone > 0:
            environmental_summary += f"Il nécessite une zone non traitée maximale de {overall_max_zone} mètres par rapport aux points d'eau. "

        if environmental_conditions:
            environmental_summary += f"Son utilisation est soumise à {len(environmental_conditions)} conditions spécifiques pour la protection de l'environnement."

        # Prepare response
        response = {
            "product_info": {
                "product_name": product.product_name,
                "registration_number": product.registration_number,
                "function_category": product.function_category,
                "authorization_status": product.authorization_status,
                "is_currently_authorized": product.is_currently_authorized
            },
            "environmental_hazards": [
                {
                    "hazard_code": h.hazard_code,
                    "hazard_description": h.hazard_description,
                    "hazard_category": h.hazard_category,
                    "hazard_severity": h.hazard_severity
                }
                for h in environmental_hazards
            ],
            "buffer_zones": {
                "aquatic_zone": {
                    "distance_meters": max_aquatic_zone,
                    "description": "Zone non traitée à respecter par rapport aux points d'eau"
                },
                "arthropod_zone": {
                    "distance_meters": max_arthropod_zone,
                    "description": "Zone non traitée à respecter par rapport aux zones d'arthropodes non cibles"
                },
                "plant_zone": {
                    "distance_meters": max_plant_zone,
                    "description": "Zone non traitée à respecter par rapport aux plantes non cibles"
                },
                "maximum_zone": {
                    "distance_meters": overall_max_zone,
                    "description": "Zone non traitée maximale tous types confondus"
                }
            },
            "environmental_conditions": [
                {
                    "condition_category": c.condition_category,
                    "condition_description": c.condition_description,
                    "condition_type": c.condition_type,
                    "condition_importance": c.condition_importance
                }
                for c in environmental_conditions
            ],
            "crops_with_special_requirements": list(crops_with_requirements.values()),
            "environmental_summary": environmental_summary,
            "_citation": {"source": "E-Phy", "url": "https://ephy.anses.fr"}
        }

        return response

    @classmethod
    async def find_alternatives_for_product(cls,
                                            session: AsyncSession,
                                            product_identifier: str,
                                            reason: Optional[str] = None,
                                            only_authorized: bool = True) -> Dict[str, Any]:
        """
        Find alternative products with similar efficacy profiles.

        Identifies alternative products with similar active ingredients
        or target efficacy. Particularly useful for finding replacements
        for withdrawn products or reducing resistance risk.

        Args:
            product_identifier: Product to find alternatives for
            reason: Reason for seeking alternatives (e.g., "withdrawal", "resistance management")
            only_authorized: Whether to only include currently authorized products

        Returns:
            List of alternative products with similarity analysis and transition guidance
        """
        # Find product
        product = await cls._find_product(session, product_identifier)

        if not product:
            return {
                "error": f"Product not found: {product_identifier}",
                "_citation": {"source": "E-Phy", "url": "https://ephy.anses.fr"}
            }

        # Get active substances for this product
        substances_stmt = select(ActiveSubstance).join(
            ProductSubstance, ProductSubstance.substance_id == ActiveSubstance.id
        ).where(
            ProductSubstance.product_id == product.id
        )
        substances_result = await session.execute(substances_stmt)
        substances = substances_result.scalars().all()

        substance_ids = [s.id for s in substances]
        substance_names = [s.name for s in substances]

        # Get crops this product is used on
        crops_stmt = select(Crop).join(
            ProductUse, ProductUse.crop_id == Crop.id
        ).where(
            ProductUse.product_id == product.id
        ).distinct()
        crops_result = await session.execute(crops_stmt)
        crops = crops_result.scalars().all()

        crop_ids = [c.id for c in crops]

        # Find products with similar active substances
        similar_substance_stmt = select(
            Product,
            func.count(distinct(ActiveSubstance.id)).label('matching_substances'),
            func.count(distinct(Product.id)).label('total')
        ).join(
            ProductSubstance, ProductSubstance.product_id == Product.id
        ).join(
            ActiveSubstance, ActiveSubstance.id == ProductSubstance.substance_id
        ).where(
            ActiveSubstance.id.in_(substance_ids),
            Product.id != product.id
        )

        # Apply authorization filter if requested
        if only_authorized:
            similar_substance_stmt = similar_substance_stmt.where(
                Product.is_currently_authorized == True
            )

        # Group by product and get substance match count
        similar_substance_stmt = similar_substance_stmt.group_by(
            Product.id
        ).order_by(
            func.count(distinct(ActiveSubstance.id)).desc()
        )

        # Execute query
        similar_substance_result = await session.execute(similar_substance_stmt)
        similar_substance_products = similar_substance_result.all()

        # Find products used on the same crops
        similar_crop_stmt = select(
            Product,
            func.count(distinct(Crop.id)).label('matching_crops'),
            func.count(distinct(Product.id)).label('total')
        ).join(
            ProductUse, ProductUse.product_id == Product.id
        ).join(
            Crop, Crop.id == ProductUse.crop_id
        ).where(
            Crop.id.in_(crop_ids),
            Product.id != product.id
        )

        # Apply authorization filter if requested
        if only_authorized:
            similar_crop_stmt = similar_crop_stmt.where(
                Product.is_currently_authorized == True,
                ProductUse.is_currently_authorized == True
            )

        # Apply function category filter to match original product
        if product.function_category:
            similar_crop_stmt = similar_crop_stmt.where(
                func.lower(Product.function_category).like(f"%{product.function_category.lower()}%")
            )

        # Group by product and get crop match count
        similar_crop_stmt = similar_crop_stmt.group_by(
            Product.id
        ).order_by(
            func.count(distinct(Crop.id)).desc()
        )

        # Execute query
        similar_crop_result = await session.execute(similar_crop_stmt)
        similar_crop_products = similar_crop_result.all()

        # Create a unified list of alternative products with similarity measures
        alternatives = {}

        # Add substance-similar products
        for product_obj, matching_substances, _ in similar_substance_products:
            similarity_score = matching_substances / len(substance_ids) if substance_ids else 0

            if product_obj.id not in alternatives:
                alternatives[product_obj.id] = {
                    "product": product_obj,
                    "similarity_reasons": {
                        "same_active_substances": [],
                        "common_uses": [],
                        "similar_formulation": False
                    },
                    "similarity_score": similarity_score
                }
            else:
                alternatives[product_obj.id]["similarity_score"] = max(
                    alternatives[product_obj.id]["similarity_score"],
                    similarity_score
                )

            # Get the shared substances
            shared_substances_stmt = select(ActiveSubstance).join(
                ProductSubstance, ProductSubstance.substance_id == ActiveSubstance.id
            ).where(
                ProductSubstance.product_id == product_obj.id,
                ActiveSubstance.id.in_(substance_ids)
            )
            shared_substances_result = await session.execute(shared_substances_stmt)
            shared_substances = shared_substances_result.scalars().all()

            alternatives[product_obj.id]["similarity_reasons"]["same_active_substances"] = [s.name for s in
                                                                                            shared_substances]

        # Add crop-similar products
        for product_obj, matching_crops, _ in similar_crop_products:
            similarity_score = matching_crops / len(crop_ids) if crop_ids else 0

            if product_obj.id not in alternatives:
                alternatives[product_obj.id] = {
                    "product": product_obj,
                    "similarity_reasons": {
                        "same_active_substances": [],
                        "common_uses": [],
                        "similar_formulation": False
                    },
                    "similarity_score": similarity_score * 0.8  # Slightly lower weight for crop similarity
                }
            else:
                # Update score if higher (with crop similarity weight)
                crop_similarity_score = similarity_score * 0.8
                alternatives[product_obj.id]["similarity_score"] = max(
                    alternatives[product_obj.id]["similarity_score"],
                    crop_similarity_score
                )

            # Get the common crops
            common_crops_stmt = select(Crop).join(
                ProductUse, ProductUse.crop_id == Crop.id
            ).where(
                ProductUse.product_id == product_obj.id,
                Crop.id.in_(crop_ids)
            ).distinct()
            common_crops_result = await session.execute(common_crops_stmt)
            common_crops = common_crops_result.scalars().all()

            # Get targets for these common crops
            common_uses = []
            for crop in common_crops:
                # Get targets for the original product on this crop
                original_targets_stmt = select(Target).join(
                    ProductUse, ProductUse.target_id == Target.id
                ).where(
                    ProductUse.product_id == product.id,
                    ProductUse.crop_id == crop.id
                )
                original_targets_result = await session.execute(original_targets_stmt)
                original_targets = original_targets_result.scalars().all()

                # Get targets for the alternative product on this crop
                alternative_targets_stmt = select(Target).join(
                    ProductUse, ProductUse.target_id == Target.id
                ).where(
                    ProductUse.product_id == product_obj.id,
                    ProductUse.crop_id == crop.id
                )
                alternative_targets_result = await session.execute(alternative_targets_stmt)
                alternative_targets = alternative_targets_result.scalars().all()

                # Find common targets
                common_targets = []
                for t1 in original_targets:
                    for t2 in alternative_targets:
                        if t1.id == t2.id:
                            common_targets.append(t2.target_name)

                if common_targets:
                    common_uses.append({
                        "crop_name": crop.crop_name,
                        "target_names": common_targets
                    })

            alternatives[product_obj.id]["similarity_reasons"]["common_uses"] = common_uses

        # Check for formulation similarity
        for alt_id, alt_data in alternatives.items():
            if product.formulation_type and alt_data["product"].formulation_type:
                if product.formulation_type == alt_data["product"].formulation_type:
                    alt_data["similarity_reasons"]["similar_formulation"] = True
                    alt_data["similarity_score"] += 0.1  # Small bonus for formulation similarity

        # Convert to list and sort by similarity score
        alternatives_list = list(alternatives.values())
        alternatives_list.sort(key=lambda x: x["similarity_score"], reverse=True)

        # Format results
        formatted_alternatives = []
        for alt in alternatives_list:
            formatted_alternatives.append({
                "product_name": alt["product"].product_name,
                "registration_number": alt["product"].registration_number,
                "similarity_score": round(alt["similarity_score"] * 100),  # Convert to percentage
                "similarity_reasons": {
                    "same_active_substances": alt["similarity_reasons"]["same_active_substances"],
                    "common_uses": alt["similarity_reasons"]["common_uses"],
                    "similar_formulation": alt["similarity_reasons"]["similar_formulation"]
                },
                "authorization_status": alt["product"].authorization_status,
                "is_currently_authorized": alt["product"].is_currently_authorized,
                "function_category": alt["product"].function_category
            })

        # Generate transition guidance based on reason
        transition_guidance = []

        if reason:
            reason_lower = reason.lower()

            if "withdrawal" in reason_lower or "retrait" in reason_lower:
                transition_guidance.extend([
                    "Vérifiez les dates limites d'utilisation des stocks existants du produit retiré",
                    "Assurez-vous que les alternatives sont homologuées pour les mêmes usages",
                    "Comparez les doses et les conditions d'emploi qui peuvent différer"
                ])
            elif "resistance" in reason_lower:
                transition_guidance.extend([
                    "Alternez les produits avec des modes d'action différents",
                    "Vérifiez les codes FRAC/HRAC/IRAC pour la gestion des résistances",
                    "Respectez le nombre maximal d'applications par saison pour chaque mode d'action"
                ])
            elif "cout" in reason_lower or "coût" in reason_lower or "prix" in reason_lower:
                transition_guidance.extend([
                    "Comparez les doses d'emploi qui peuvent influencer le coût à l'hectare",
                    "Prenez en compte le nombre d'applications nécessaires",
                    "Évaluez l'efficacité attendue qui peut justifier un prix plus élevé"
                ])
        else:
            # Default guidance
            transition_guidance.extend([
                "Vérifiez que l'alternative est homologuée pour les usages souhaités",
                "Comparez les doses d'emploi et les conditions d'utilisation",
                "Respectez les délais avant récolte qui peuvent différer"
            ])

        # Prepare response
        response = {
            "original_product": {
                "product_name": product.product_name,
                "registration_number": product.registration_number,
                "function_category": product.function_category,
                "active_substances": substance_names,
                "authorization_status": product.authorization_status,
                "is_currently_authorized": product.is_currently_authorized
            },
            "alternatives": formatted_alternatives,
            "transition_guidance": transition_guidance,
            "_citation": {"source": "E-Phy", "url": "https://ephy.anses.fr"}
        }

        return response

    @classmethod
    async def get_crop_pest_calendar(cls,
                                     session: AsyncSession,
                                     crop: str,
                                     include_products: bool = True) -> Dict[str, Any]:
        """
        Generate a comprehensive pest management calendar for a specific crop.

        Creates a season-by-season guide to potential pests and diseases
        with appropriate intervention timing and product recommendations.
        Integrates growth stage information for precise timing.

        Args:
            crop: Crop name to generate calendar for
            include_products: Whether to include product recommendations

        Returns:
            Seasonal calendar with pest emergence patterns and management guidance
        """
        # Find matching crops
        crop_query = crop.strip().lower()
        crops_query = select(Crop).where(
            or_(
                func.lower(Crop.crop_name).ilike(f'%{crop_query}%'),
                func.lower(Crop.normalized_name).ilike(f'%{crop_query}%'),
                func.lower(Crop.common_synonyms).ilike(f'%{crop_query}%')
            )
        )
        result = await session.execute(crops_query)
        matching_crops = result.scalars().all()

        if not matching_crops:
            return {"error": f"No crops found matching: {crop}"}

        selected_crop = matching_crops[0]

        # Get all authorized uses for this crop
        uses_query = select(
            ProductUse,
            Product,
            Target
        ).join(
            Product, Product.id == ProductUse.product_id
        ).outerjoin(
            Target, Target.id == ProductUse.target_id
        ).where(
            ProductUse.crop_id == selected_crop.id,
            ProductUse.is_currently_authorized == True,
            Product.is_currently_authorized == True
        )
        result = await session.execute(uses_query)
        uses = result.all()

        if not uses:
            return {
                "error": f"No authorized uses found for {selected_crop.crop_name}"
            }

        # Group by target and season
        calendar_data = {}

        # Define seasons
        seasons = ["Printemps", "Été", "Automne", "Hiver"]

        # Map months to seasons (simplified)
        month_to_season = {
            "janvier": "Hiver",
            "février": "Hiver",
            "mars": "Printemps",
            "avril": "Printemps",
            "mai": "Printemps",
            "juin": "Été",
            "juillet": "Été",
            "août": "Été",
            "septembre": "Automne",
            "octobre": "Automne",
            "novembre": "Automne",
            "décembre": "Hiver"
        }

        # Initialize calendar structure
        for season in seasons:
            calendar_data[season] = {
                "pests": [],
                "diseases": [],
                "weeds": [],
                "others": []
            }

        # Process each use
        for use, product, target in uses:
            if not target:
                continue

            # Determine appropriate season
            assigned_season = None

            # Check specific season information in the use
            if use.application_season_min:
                season_lower = use.application_season_min.lower()
                for month, season in month_to_season.items():
                    if month in season_lower:
                        assigned_season = season
                        break

                # Direct season match
                if not assigned_season:
                    for season in seasons:
                        if season.lower() in season_lower:
                            assigned_season = season
                            break

            # Check growth stage for season hints if no direct season info
            if not assigned_season and use.min_growth_stage is not None and use.max_growth_stage is not None:
                avg_stage = (use.min_growth_stage + use.max_growth_stage) / 2
                if avg_stage < 30:
                    assigned_season = "Printemps"  # Early growth usually in spring
                elif 30 <= avg_stage < 70:
                    assigned_season = "Été"  # Flowering usually in summer
                elif avg_stage >= 70:
                    assigned_season = "Automne"  # Maturation usually in autumn

            # Default to spring if no season information
            if not assigned_season:
                assigned_season = "Printemps"

            # Determine target category
            target_category = "others"
            target_type = target.target_type or "Autre"

            if target_type in ["Insecte", "Acarien", "Mollusque"]:
                target_category = "pests"
            elif target_type in ["Champignon", "Bactérie", "Virus"]:
                target_category = "diseases"
            elif target_type == "Adventice":
                target_category = "weeds"

            # Create target entry if it doesn't exist yet
            target_entry = None
            for existing_entry in calendar_data[assigned_season][target_category]:
                if existing_entry["target_name"] == target.target_name:
                    target_entry = existing_entry
                    break

            if not target_entry:
                target_entry = {
                    "target_name": target.target_name,
                    "target_type": target_type,
                    "growth_stages": set(),
                    "products": []
                }
                calendar_data[assigned_season][target_category].append(target_entry)

            # Add growth stage information
            if use.min_growth_stage is not None and use.max_growth_stage is not None:
                for (stage_min, stage_max), stage_desc in cls.growth_stage_map.items():
                    if use.min_growth_stage <= stage_max and use.max_growth_stage >= stage_min:
                        stage_info = f"BBCH {stage_min}-{stage_max} ({stage_desc})"
                        target_entry["growth_stages"].add(stage_info)

            # Add product if requested
            if include_products:
                # Check if product already exists
                product_exists = False
                for existing_product in target_entry["products"]:
                    if existing_product["product_name"] == product.product_name:
                        product_exists = True
                        break

                if not product_exists:
                    target_entry["products"].append({
                        "product_name": product.product_name,
                        "registration_number": product.registration_number,
                        "function_category": product.function_category,
                        "min_dose": use.min_dose,
                        "max_dose": use.max_dose,
                        "dose_unit": use.dose_unit
                    })

        # Convert growth stage sets to lists
        for season in seasons:
            for category in ["pests", "diseases", "weeds", "others"]:
                for target in calendar_data[season][category]:
                    target["growth_stages"] = list(target["growth_stages"])

        # Generate calendar summary
        calendar_summary = f"Calendrier de protection pour {selected_crop.crop_name}:\n"

        for season in seasons:
            season_data = calendar_data[season]
            total_targets = sum(len(targets) for targets in season_data.values())

            if total_targets > 0:
                calendar_summary += f"\n{season}: {total_targets} bio-agresseurs à surveiller"

                if season_data["pests"]:
                    calendar_summary += f" ({len(season_data['pests'])} ravageurs"

                    if season_data["diseases"]:
                        calendar_summary += f", {len(season_data['diseases'])} maladies"

                    if season_data["weeds"]:
                        calendar_summary += f", {len(season_data['weeds'])} adventices"

                    calendar_summary += ")"

                elif season_data["diseases"]:
                    calendar_summary += f" ({len(season_data['diseases'])} maladies"

                    if season_data["weeds"]:
                        calendar_summary += f", {len(season_data['weeds'])} adventices"

                    calendar_summary += ")"

                elif season_data["weeds"]:
                    calendar_summary += f" ({len(season_data['weeds'])} adventices)"

        # Prepare response
        response = {
            "crop_info": {
                "crop_name": selected_crop.crop_name,
                "crop_category": selected_crop.crop_category
            },
            "calendar": calendar_data,
            "calendar_summary": calendar_summary,
            "_citation": {
                "source": "E-Phy",
                "url": "https://ephy.anses.fr"
            }
        }

        return response

    @classmethod
    async def analyze_pest_resistance_risk(cls,
                                           session: AsyncSession,
                                           product_identifier: Optional[str] = None,
                                           substance_name: Optional[str] = None,
                                           target: Optional[str] = None) -> Dict[str, Any]:
        """
        Analyze pest resistance risk with management strategies.

        Assesses the risk of resistance development based on active
        substance mode of action and usage patterns. Provides rotation and
        management recommendations to prevent resistance.

        Args:
            product_identifier: Optional product to analyze
            substance_name: Optional active substance to analyze
            target: Optional pest/disease to analyze resistance risk for

        Returns:
            Resistance risk assessment with practical management strategies
        """
        # session parameter is passed to function

        # Define mode of action groups and resistance risk
        # This is simplified; in a real system you would have a complete database
        moa_groups = {
            # Herbicides (HRAC groups)
            "glyphosate": {"group": "G", "risk": "high", "type": "herbicide"},
            "s-métolachlore": {"group": "K3", "risk": "high", "type": "herbicide"},
            "pendiméthaline": {"group": "K1", "risk": "medium", "type": "herbicide"},
            "bentazone": {"group": "C3", "risk": "low", "type": "herbicide"},
            "mésotrione": {"group": "F2", "risk": "medium", "type": "herbicide"},

            # Fungicides (FRAC groups)
            "azoxystrobine": {"group": "11", "risk": "high", "type": "fungicide"},
            "cyproconazole": {"group": "3", "risk": "medium", "type": "fungicide"},
            "mancozèbe": {"group": "M3", "risk": "low", "type": "fungicide"},
            "boscalid": {"group": "7", "risk": "medium", "type": "fungicide"},
            "folpet": {"group": "M4", "risk": "low", "type": "fungicide"},

            # Insecticides (IRAC groups)
            "lambda-cyhalothrine": {"group": "3A", "risk": "high", "type": "insecticide"},
            "imidaclopride": {"group": "4A", "risk": "high", "type": "insecticide"},
            "spinosad": {"group": "5", "risk": "medium", "type": "insecticide"},
            "pyriproxyfène": {"group": "7C", "risk": "medium", "type": "insecticide"},
            "bacillus thuringiensis": {"group": "11A", "risk": "low", "type": "insecticide"}
        }

        # Case 1: Product analysis
        if product_identifier:
            product = await cls._find_product(session, product_identifier)

            if not product:
                return {"error": f"Product not found: {product_identifier}"}

            # Get active substances
            substances_stmt = select(ActiveSubstance).join(
                ProductSubstance, ProductSubstance.substance_id == ActiveSubstance.id
            ).where(
                ProductSubstance.product_id == product.id
            )
            substances_result = await session.execute(substances_stmt)
            substances = substances_result.scalars().all()

            # Analyze substance risk
            substance_risks = []
            overall_risk = "low"

            for substance in substances:
                substance_name_lower = substance.name.lower()
                matched_substance = None

                # Try to match substance with known MOA groups
                for known_substance, moa_data in moa_groups.items():
                    if known_substance in substance_name_lower:
                        matched_substance = known_substance

                        substance_risk = {
                            "substance_name": substance.name,
                            "mode_of_action": moa_data["group"],
                            "risk_level": moa_data["risk"],
                            "type": moa_data["type"]
                        }

                        substance_risks.append(substance_risk)

                        # Update overall risk level
                        if moa_data["risk"] == "high" and overall_risk != "high":
                            overall_risk = "high"
                        elif moa_data["risk"] == "medium" and overall_risk == "low":
                            overall_risk = "medium"

                        break

                # If no match found, add with unknown risk
                if not matched_substance:
                    substance_risk = {
                        "substance_name": substance.name,
                        "mode_of_action": "Unknown",
                        "risk_level": "unknown",
                        "type": product.function_category
                    }

                    substance_risks.append(substance_risk)

            # Get usage patterns
            product_types = []
            if "herbicide" in product.function_category.lower():
                product_types.append("herbicide")
            if "fongicide" in product.function_category.lower():
                product_types.append("fungicide")
            if "insecticide" in product.function_category.lower():
                product_types.append("insecticide")

            # Generate management strategies
            management_strategies = []

            # General strategies
            management_strategies.append("Respecter strictement les doses recommandées sur l'étiquette")
            management_strategies.append("Éviter les applications répétées du même produit ou mode d'action")

            # Type-specific strategies
            if "herbicide" in product_types:
                management_strategies.append("Alterner les herbicides de différents groupes HRAC")
                management_strategies.append(
                    "Incorporer des méthodes non chimiques (désherbage mécanique, rotation des cultures)")

            if "fungicide" in product_types:
                management_strategies.append("Alterner les fongicides de différents groupes FRAC")
                management_strategies.append("Utiliser des variétés résistantes quand disponibles")
                management_strategies.append(
                    "Appliquer des fongicides protectants multi-sites en mélange avec des produits à site d'action unique")

            if "insecticide" in product_types:
                management_strategies.append("Alterner les insecticides de différents groupes IRAC")
                management_strategies.append("Favoriser la présence d'auxiliaires naturels")
                management_strategies.append("Appliquer des seuils d'intervention pour limiter les traitements")

            # Risk-specific strategies
            if overall_risk == "high":
                management_strategies.append(
                    "Ce produit présente un risque élevé de résistance, limiter à 1-2 applications par saison")
                management_strategies.append(
                    "Surveiller l'efficacité après traitement pour détecter rapidement toute baisse d'efficacité")

            # Prepare response
            response = {
                "product_info": {
                    "product_name": product.product_name,
                    "registration_number": product.registration_number,
                    "function_category": product.function_category
                },
                "resistance_assessment": {
                    "substance_risks": substance_risks,
                    "overall_risk": overall_risk
                },
                "management_strategies": management_strategies,
                "_citation": {
                    "source": "E-Phy",
                    "url": "https://ephy.anses.fr"
                }
            }

            return response

        # Case 2: Substance analysis
        elif substance_name:
            substance_query = substance_name.strip().lower()
            matching_substances_stmt = select(ActiveSubstance).where(
                or_(
                    func.lower(ActiveSubstance.name).ilike(f'%{substance_query}%'),
                    func.lower(ActiveSubstance.normalized_name).ilike(f'%{substance_query}%'),
                    func.lower(ActiveSubstance.variants).ilike(f'%{substance_query}%')
                )
            )
            matching_substances_result = await session.execute(matching_substances_stmt)
            matching_substances = matching_substances_result.scalars().all()

            if not matching_substances:
                return {"error": f"No active substances found matching: {substance_name}"}

            selected_substance = matching_substances[0]
            substance_name_lower = selected_substance.name.lower()

            # Find MOA group and risk
            substance_risk = None
            for known_substance, moa_data in moa_groups.items():
                if known_substance in substance_name_lower:
                    substance_risk = {
                        "substance_name": selected_substance.name,
                        "mode_of_action": moa_data["group"],
                        "risk_level": moa_data["risk"],
                        "type": moa_data["type"]
                    }
                    break

            # If no match found, use unknown risk
            if not substance_risk:
                substance_risk = {
                    "substance_name": selected_substance.name,
                    "mode_of_action": "Unknown",
                    "risk_level": "unknown",
                    "type": "unknown"
                }

            # Find products containing this substance
            products_stmt = select(Product).join(
                ProductSubstance, ProductSubstance.product_id == Product.id
            ).where(
                ProductSubstance.substance_id == selected_substance.id,
                Product.is_currently_authorized == True
            )
            products_result = await session.execute(products_stmt)
            products = products_result.scalars().all()

            # Generate management strategies based on substance risk
            management_strategies = []

            # General strategies
            management_strategies.append("Respecter strictement les doses recommandées sur l'étiquette")
            management_strategies.append("Éviter les applications répétées de la même substance active")

            # Type-specific strategies
            if substance_risk["type"] == "herbicide":
                management_strategies.append(
                    f"Ce herbicide appartient au groupe HRAC {substance_risk['mode_of_action']}")
                management_strategies.append("Alterner avec des herbicides de différents groupes HRAC")
                management_strategies.append(
                    "Incorporer des méthodes non chimiques (désherbage mécanique, rotation des cultures)")

            elif substance_risk["type"] == "fungicide":
                management_strategies.append(
                    f"Ce fongicide appartient au groupe FRAC {substance_risk['mode_of_action']}")
                management_strategies.append("Alterner avec des fongicides de différents groupes FRAC")
                management_strategies.append("Utiliser des variétés résistantes quand disponibles")

            elif substance_risk["type"] == "insecticide":
                management_strategies.append(
                    f"Cet insecticide appartient au groupe IRAC {substance_risk['mode_of_action']}")
                management_strategies.append("Alterner avec des insecticides de différents groupes IRAC")
                management_strategies.append("Favoriser la présence d'auxiliaires naturels")

            # Risk-specific strategies
            if substance_risk["risk_level"] == "high":
                management_strategies.append(
                    "Cette substance présente un risque élevé de résistance, limiter à 1-2 applications par saison")
                management_strategies.append(
                    "Surveiller l'efficacité après traitement pour détecter rapidement toute baisse d'efficacité")

            # Prepare response
            response = {
                "substance_info": {
                    "substance_name": selected_substance.name,
                    "cas_number": selected_substance.cas_number,
                    "authorization_status": selected_substance.authorization_status
                },
                "resistance_assessment": {
                    "substance_risk": substance_risk
                },
                "management_strategies": management_strategies,
                "products_containing_substance": [
                    {
                        "product_name": p.product_name,
                        "registration_number": p.registration_number,
                        "function_category": p.function_category
                    }
                    for p in products
                ],
                "_citation": {
                    "source": "E-Phy",
                    "url": "https://ephy.anses.fr"
                }
            }

            return response

        # Case 3: Target analysis
        elif target:
            target_query = target.strip().lower()
            matching_targets_stmt = select(Target).where(
                or_(
                    func.lower(Target.target_name).ilike(f'%{target_query}%'),
                    func.lower(Target.normalized_name).ilike(f'%{target_query}%'),
                    func.lower(Target.common_synonyms).ilike(f'%{target_query}%')
                )
            )
            matching_targets_result = await session.execute(matching_targets_stmt)
            matching_targets = matching_targets_result.scalars().all()

            if not matching_targets:
                return {"error": f"No targets found matching: {target}"}

            selected_target = matching_targets[0]
            target_type = selected_target.target_type or "Autre"

            # Find products authorized for this target
            products_stmt = select(
                Product,
                ProductUse
            ).join(
                ProductUse, ProductUse.product_id == Product.id
            ).where(
                ProductUse.target_id == selected_target.id,
                ProductUse.is_currently_authorized == True,
                Product.is_currently_authorized == True
            )
            products_result = await session.execute(products_stmt)
            products = products_result.all()

            # Analyze substance diversity
            substances_by_moa = {}
            product_substances = {}

            for product, use in products:
                # Get substances for this product
                substances_stmt = select(ActiveSubstance).join(
                    ProductSubstance, ProductSubstance.substance_id == ActiveSubstance.id
                ).where(
                    ProductSubstance.product_id == product.id
                )
                substances_result = await session.execute(substances_stmt)
                substances = substances_result.scalars().all()

                product_substances[product.id] = []

                for substance in substances:
                    substance_name_lower = substance.name.lower()

                    # Try to match substance with known MOA groups
                    for known_substance, moa_data in moa_groups.items():
                        if known_substance in substance_name_lower:
                            moa_group = moa_data["group"]

                            if moa_group not in substances_by_moa:
                                substances_by_moa[moa_group] = {
                                    "group": moa_group,
                                    "type": moa_data["type"],
                                    "risk_level": moa_data["risk"],
                                    "substances": [],
                                    "products": []
                                }

                            if substance.name not in substances_by_moa[moa_group]["substances"]:
                                substances_by_moa[moa_group]["substances"].append(substance.name)

                            if product.product_name not in substances_by_moa[moa_group]["products"]:
                                substances_by_moa[moa_group]["products"].append(product.product_name)

                            product_substances[product.id].append({
                                "substance_name": substance.name,
                                "moa_group": moa_group,
                                "risk_level": moa_data["risk"]
                            })

                            break
                    else:
                        # If no match found, add to unknown group
                        if "Unknown" not in substances_by_moa:
                            substances_by_moa["Unknown"] = {
                                "group": "Unknown",
                                "type": "unknown",
                                "risk_level": "unknown",
                                "substances": [],
                                "products": []
                            }

                        if substance.name not in substances_by_moa["Unknown"]["substances"]:
                            substances_by_moa["Unknown"]["substances"].append(substance.name)

                        if product.product_name not in substances_by_moa["Unknown"]["products"]:
                            substances_by_moa["Unknown"]["products"].append(product.product_name)

                        product_substances[product.id].append({
                            "substance_name": substance.name,
                            "moa_group": "Unknown",
                            "risk_level": "unknown"
                        })

            # Assess overall resistance risk
            resistance_risk = "low"

            # Count number of different MOA groups
            moa_groups_count = len([g for g in substances_by_moa.keys() if g != "Unknown"])

            if moa_groups_count <= 1:
                resistance_risk = "high"
            elif moa_groups_count <= 3:
                resistance_risk = "medium"

            # Count high-risk MOA groups
            high_risk_groups = len([g for g, data in substances_by_moa.items() if data["risk_level"] == "high"])

            if high_risk_groups >= 2:
                resistance_risk = "high"
            elif high_risk_groups == 1 and resistance_risk == "low":
                resistance_risk = "medium"

            # Generate management strategies based on target type
            management_strategies = []

            # General strategies
            management_strategies.append("Alterner les produits de différents modes d'action")
            management_strategies.append("Respecter les doses recommandées")

            # Target-specific strategies
            if target_type == "Insecte":
                management_strategies.append("Favoriser les auxiliaires naturels")
                management_strategies.append("Utiliser des méthodes alternatives (confusion sexuelle, pièges, etc.)")
                management_strategies.append(
                    "Limiter les applications d'insecticides au seuil économique de nuisibilité")

            elif target_type == "Champignon":
                management_strategies.append("Utiliser des fongicides préventifs multi-sites")
                management_strategies.append(
                    "Améliorer l'aération des cultures pour réduire l'humidité favorable aux maladies")
                management_strategies.append("Choisir des variétés résistantes ou tolérantes")

            elif target_type == "Adventice":
                management_strategies.append("Combiner désherbage chimique et mécanique")
                management_strategies.append("Pratiquer la rotation des cultures")
                management_strategies.append("Utiliser des couverts végétaux concurrentiels")

            # Risk-specific strategies
            if resistance_risk == "high":
                management_strategies.append(
                    f"Risque élevé de résistance pour {selected_target.target_name}, surveiller attentivement l'efficacité des traitements")
                management_strategies.append("Alterner au minimum 3 modes d'action différents")

            # Rotation recommendations
            rotation_recommendations = []

            if len(substances_by_moa) > 1:
                for i, (group1, data1) in enumerate(substances_by_moa.items()):
                    if group1 == "Unknown":
                        continue

                    for group2, data2 in list(substances_by_moa.items())[i + 1:]:
                        if group2 == "Unknown":
                            continue

                        if data1["products"] and data2["products"]:
                            rotation_recommendations.append(
                                f"Alterner les produits du groupe {group1} ({data1['products'][0]}) "
                                f"avec ceux du groupe {group2} ({data2['products'][0]})"
                            )

            # Prepare response
            response = {
                "target_info": {
                    "target_name": selected_target.target_name,
                    "target_type": target_type
                },
                "resistance_assessment": {
                    "available_moa_groups": len(substances_by_moa),
                    "moa_diversity": list(substances_by_moa.values()),
                    "overall_risk": resistance_risk
                },
                "management_strategies": management_strategies,
                "rotation_recommendations": rotation_recommendations,
                "_citation": {
                    "source": "E-Phy",
                    "url": "https://ephy.anses.fr"
                }
            }

            return response

        # Case 4: No criteria provided
        else:
            return {
                "error": "Please provide either a product identifier, substance name, or target name to analyze resistance risk.",
                "_citation": {
                    "source": "E-Phy",
                    "url": "https://ephy.anses.fr"
                }
            }

    @classmethod
    async def get_product_efficacy_profile(cls,
                                           session: AsyncSession,
                                           product_identifier: str,
                                           target: Optional[str] = None) -> Dict[str, Any]:
        """
        Retrieve product efficacy information with practical context.

        Provides detailed information on a product's effectiveness against
        various targets based on authorized uses and application patterns.
        Includes optimal conditions for maximum efficacy.

        Args:
            product_identifier: Product registration number or name
            target: Optional specific target to focus on

        Returns:
            Comprehensive efficacy profile with practical application insights
        """

        # Find product
        product = await cls._find_product(session, product_identifier)

        if not product:
            return {"error": f"Product not found: {product_identifier}"}

        # Get all authorized uses for this product
        uses_query = select(
            ProductUse,
            Crop,
            Target
        ).join(
            Crop, Crop.id == ProductUse.crop_id
        ).outerjoin(
            Target, Target.id == ProductUse.target_id
        ).where(
            ProductUse.product_id == product.id,
            ProductUse.is_currently_authorized == True
        )

        # Apply target filter if specified
        if target:
            target_query = target.strip().lower()
            target_filter = or_(
                func.lower(Target.target_name).ilike(f'%{target_query}%'),
                func.lower(Target.normalized_name).ilike(f'%{target_query}%'),
                func.lower(Target.common_synonyms).ilike(f'%{target_query}%')
            )

            uses_query = uses_query.where(target_filter)

        # Execute query
        uses_result = await session.execute(uses_query)
        uses = uses_result.all()

        if not uses:
            return {
                "error": f"No {'authorized uses found matching the target' if target else 'authorized uses found'} for {product.product_name}"
            }

        # Get active substances for this product
        substances_stmt = select(
            ActiveSubstance,
            ProductSubstance.concentration,
            ProductSubstance.concentration_unit
        ).join(
            ProductSubstance, ProductSubstance.substance_id == ActiveSubstance.id
        ).where(
            ProductSubstance.product_id == product.id
        )
        substances_result = await session.execute(substances_stmt)
        substances = substances_result.all()

        substance_list = [
            {
                "name": s.ActiveSubstance.name,
                "concentration": s.concentration,
                "unit": s.concentration_unit
            }
            for s in substances
        ]

        # Group uses by target type
        efficacy_by_target_type = {}

        for use, crop, target_obj in uses:
            if not target_obj:
                continue

            target_type = target_obj.target_type or "Autre"

            if target_type not in efficacy_by_target_type:
                efficacy_by_target_type[target_type] = []

            # Extract application conditions that can affect efficacy
            efficacy_factors = []

            # Growth stage effects
            if use.min_growth_stage is not None and use.max_growth_stage is not None:
                stage_info = None
                for (stage_min, stage_max), stage_desc in cls.growth_stage_map.items():
                    if use.min_growth_stage <= stage_max and use.max_growth_stage >= stage_min:
                        stage_info = f"Efficace au stade {stage_desc} (BBCH {use.min_growth_stage}-{use.max_growth_stage})"
                        break

                if stage_info:
                    efficacy_factors.append(stage_info)

            # Seasonal effects
            if use.application_season_min or use.application_season_max:
                season_info = "Efficacité optimale en "
                if use.application_season_min:
                    season_info += use.application_season_min
                if use.application_season_min and use.application_season_max:
                    season_info += " à "
                if use.application_season_max:
                    season_info += use.application_season_max

                efficacy_factors.append(season_info)

            # Application part effects
            if use.application_part:
                efficacy_factors.append(f"Application sur {use.application_part}")

            # Special conditions
            if use.application_conditions:
                efficacy_factors.append(f"Conditions spécifiques: {use.application_conditions}")

            # Add to target type
            efficacy_by_target_type[target_type].append({
                "target_name": target_obj.target_name,
                "crop_name": crop.crop_name,
                "application_details": {
                    "min_dose": use.min_dose,
                    "max_dose": use.max_dose,
                    "dose_unit": use.dose_unit,
                    "max_applications": use.max_applications
                },
                "efficacy_factors": efficacy_factors
            })

        # Generate optimal conditions based on product type
        optimal_conditions = []

        # Check product type for conditions
        if "herbicide" in product.function_category.lower():
            optimal_conditions.extend([
                "Efficacité maximale sur adventices jeunes et en croissance active",
                "Appliquer sur végétation sèche mais sol humide pour une meilleure absorption",
                "Température idéale: 10-25°C (efficacité réduite en dessous de 8°C)",
                "Éviter les applications par temps venteux ou avant une pluie prévue dans les 6 heures"
            ])

        elif "fongicide" in product.function_category.lower():
            optimal_conditions.extend([
                "Efficacité maximale en application préventive, avant développement de la maladie",
                "Assurer une bonne couverture de la végétation, notamment sur les nouvelles pousses",
                "Respecter les cadences de traitement en période de forte pression",
                "Température idéale: 10-25°C (la plupart des fongicides sont moins efficaces à basse température)",
                "Éviter l'application sur feuillage mouillé ou avant une pluie prévue"
            ])

        elif "insecticide" in product.function_category.lower():
            optimal_conditions.extend([
                "Traiter dès l'observation des premiers individus pour les ravageurs à multiplication rapide",
                "Assurer une bonne couverture, y compris la face inférieure des feuilles pour certains ravageurs",
                "Vérifier le stade de développement du ravageur (certains produits sont efficaces uniquement sur certains stades)",
                "Application préférable le matin ou en soirée, éviter les heures les plus chaudes",
                "Température idéale: 15-25°C (efficacité réduite en dessous de 10°C)"
            ])

        # Generate efficacy summary
        efficacy_summary = f"Le produit {product.product_name} "

        target_type_count = len(efficacy_by_target_type)
        if target_type_count > 0:
            target_types = list(efficacy_by_target_type.keys())

            if target_type_count == 1:
                target_type = target_types[0]
                target_count = len(efficacy_by_target_type[target_type])

                if target_type == "Champignon":
                    efficacy_summary += f"est efficace contre {target_count} maladies"
                elif target_type == "Insecte":
                    efficacy_summary += f"est efficace contre {target_count} insectes ravageurs"
                elif target_type == "Adventice":
                    efficacy_summary += f"est efficace contre {target_count} adventices"
                else:
                    efficacy_summary += f"est efficace contre {target_count} bio-agresseurs de type {target_type}"
            else:
                efficacy_summary += "est efficace contre plusieurs types de bio-agresseurs: "

                type_descriptions = []
                for target_type in target_types:
                    target_count = len(efficacy_by_target_type[target_type])

                    if target_type == "Champignon":
                        type_descriptions.append(f"{target_count} maladies")
                    elif target_type == "Insecte":
                        type_descriptions.append(f"{target_count} insectes ravageurs")
                    elif target_type == "Adventice":
                        type_descriptions.append(f"{target_count} adventices")
                    else:
                        type_descriptions.append(f"{target_count} {target_type.lower()}s")

                efficacy_summary += ", ".join(type_descriptions)

        efficacy_summary += "."

        # Add substance information
        if substance_list:
            if len(substance_list) == 1:
                efficacy_summary += f" Il contient la substance active {substance_list[0]['name']}"

                if substance_list[0]['concentration']:
                    efficacy_summary += f" à {substance_list[0]['concentration']} {substance_list[0]['unit']}"

                efficacy_summary += "."
            else:
                efficacy_summary += f" Il associe {len(substance_list)} substances actives pour une meilleure efficacité."

        # Prepare response
        response = {
            "product_info": {
                "product_name": product.product_name,
                "registration_number": product.registration_number,
                "function_category": product.function_category,
                "active_substances": substance_list
            },
            "efficacy_by_target_type": efficacy_by_target_type,
            "optimal_conditions": optimal_conditions,
            "efficacy_summary": efficacy_summary,
            "_citation": {
                "source": "E-Phy",
                "url": "https://ephy.anses.fr"
            }
        }

        return response

    @classmethod
    async def _get_product_suggestions(cls, session: AsyncSession, failed_identifier: str) -> List[str]:
        """Get database-driven product suggestions for failed searches."""
        suggestions = []

        # Find products with similar prefixes or names
        if len(failed_identifier) >= 3:
            prefix_query = select(Product.product_name, Product.registration_number).filter(
                or_(
                    Product.product_name.ilike(f'{failed_identifier[:3]}%'),
                    Product.registration_number.ilike(f'{failed_identifier[:3]}%')
                ),
                Product.is_currently_authorized == True
            ).limit(5)
            try:
                prefix_result = await session.execute(prefix_query)
                suggestions.extend([f"{row[0]} ({row[1]})" for row in prefix_result.all()])
            except:
                pass

        # Get popular products if no prefix matches
        if not suggestions:
            try:
                popular_query = select(Product.product_name, Product.registration_number).filter(
                    Product.is_currently_authorized == True
                ).limit(5)
                popular_result = await session.execute(popular_query)
                suggestions.extend([f"{row[0]} ({row[1]})" for row in popular_result.all()])
            except:
                suggestions = []

        return suggestions

    @classmethod
    async def _get_crop_suggestions(cls, session: AsyncSession, failed_crop: str) -> List[str]:
        """Get database-driven crop suggestions for failed searches."""
        suggestions = []

        # Find crops with similar prefixes
        if len(failed_crop) >= 2:
            prefix_query = select(Crop.crop_name).filter(
                Crop.crop_name.ilike(f'{failed_crop[:2]}%')
            ).limit(5)
            try:
                prefix_result = await session.execute(prefix_query)
                suggestions.extend([row[0] for row in prefix_result.all()])
            except:
                pass

        # Get popular crops if no prefix matches
        if not suggestions:
            try:
                popular_query = select(Crop.crop_name).join(
                    ProductUse, ProductUse.crop_id == Crop.id
                ).group_by(Crop.crop_name).order_by(
                    func.count().desc()
                ).limit(5)
                popular_result = await session.execute(popular_query)
                suggestions.extend([row[0] for row in popular_result.all()])
            except:
                suggestions = []

        return suggestions
