"""
Base document processor interface and common functionality.
All document processors inherit from this base class.
"""
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import List, Dict, Any, Optional
from uuid import UUID


@dataclass
class ProcessingResult:
    """Result from document processing."""
    success: bool
    content: str = ""
    title: Optional[str] = None
    language: Optional[str] = None
    total_pages: Optional[int] = None
    crop_categories: Optional[List[str]] = None
    topics: Optional[List[str]] = None
    geographic_scope: Optional[List[str]] = None
    seasonal_relevance: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class BaseDocumentProcessor(ABC):
    """
    Abstract base class for document processors.
    Each file type has its own processor implementation.
    """
    
    def __init__(self):
        self.supported_extensions = set()
    
    @abstractmethod
    async def process(self, file_content: bytes, document_id: UUID) -> ProcessingResult:
        """
        Process document content and extract text and metadata.
        
        Args:
            file_content: Raw file content as bytes
            document_id: Document ID for reference
            
        Returns:
            ProcessingResult with extracted content and metadata
        """
        pass
    
    def supports_extension(self, extension: str) -> bool:
        """Check if processor supports given file extension."""
        return extension.lower() in self.supported_extensions
    
    async def _extract_agricultural_metadata(self, content: str) -> Dict[str, List[str]]:
        """
        Extract agricultural domain metadata from content.
        Can be overridden by specific processors for better accuracy.
        """
        # Basic keyword-based extraction
        crops = self._extract_crops(content)
        topics = self._extract_topics(content)
        
        return {
            'crop_categories': crops,
            'topics': topics,
            'geographic_scope': [],  # Can be enhanced with NER
            'seasonal_relevance': []  # Can be enhanced with temporal extraction
        }
    
    def _extract_crops(self, content: str) -> List[str]:
        """Extract crop names from content using keyword matching."""
        # Common crop keywords - can be expanded
        crop_keywords = {
            'wheat': ['wheat', 'triticum'],
            'corn': ['corn', 'maize', 'zea mays'],
            'rice': ['rice', 'oryza'],
            'soybean': ['soybean', 'soya', 'glycine max'],
            'barley': ['barley', 'hordeum'],
            'oats': ['oats', 'avena'],
            'cotton': ['cotton', 'gossypium'],
            'tomato': ['tomato', 'solanum lycopersicum'],
            'potato': ['potato', 'solanum tuberosum'],
            'apple': ['apple', 'malus'],
            'grape': ['grape', 'vitis'],
            'citrus': ['citrus', 'orange', 'lemon', 'lime'],
            'lettuce': ['lettuce', 'lactuca'],
            'carrot': ['carrot', 'daucus'],
            'onion': ['onion', 'allium']
        }
        
        content_lower = content.lower()
        detected_crops = []
        
        for crop, keywords in crop_keywords.items():
            if any(keyword in content_lower for keyword in keywords):
                detected_crops.append(crop)
        
        return detected_crops
    
    def _extract_topics(self, content: str) -> List[str]:
        """Extract agricultural topics from content."""
        topic_keywords = {
            'pest_management': ['pest', 'insect', 'bug', 'aphid', 'beetle', 'control'],
            'disease_control': ['disease', 'fungus', 'bacteria', 'virus', 'pathogen', 'blight', 'rot'],
            'fertilization': ['fertilizer', 'nutrient', 'nitrogen', 'phosphorus', 'potassium', 'npk'],
            'irrigation': ['irrigation', 'water', 'watering', 'moisture', 'drought'],
            'soil_management': ['soil', 'tillage', 'cultivation', 'erosion', 'pH'],
            'harvest': ['harvest', 'harvesting', 'yield', 'picking'],
            'planting': ['planting', 'seeding', 'sowing', 'germination'],
            'weather': ['weather', 'climate', 'temperature', 'rainfall', 'frost'],
            'organic': ['organic', 'biological', 'sustainable', 'eco-friendly'],
            'herbicide': ['herbicide', 'weed', 'glyphosate'],
            'fungicide': ['fungicide', 'antifungal'],
            'insecticide': ['insecticide', 'pesticide']
        }
        
        content_lower = content.lower()
        detected_topics = []
        
        for topic, keywords in topic_keywords.items():
            if any(keyword in content_lower for keyword in keywords):
                detected_topics.append(topic)
        
        return detected_topics
    
    def _detect_language(self, content: str) -> Optional[str]:
        """
        Basic language detection.
        Can be enhanced with proper language detection libraries.
        """
        # Simple heuristic - check for common English words
        english_indicators = ['the', 'and', 'or', 'of', 'to', 'in', 'for', 'with', 'by']
        content_lower = content.lower()
        
        english_count = sum(1 for word in english_indicators if f' {word} ' in content_lower)
        
        if english_count >= 3:
            return 'en'
        
        return None  # Unknown language
    
    def _extract_title(self, content: str) -> Optional[str]:
        """
        Extract document title from content.
        Default implementation takes first non-empty line.
        """
        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if line and len(line) < 200:  # Reasonable title length
                return line
        
        return None