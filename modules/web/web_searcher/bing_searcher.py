from enum import Enum
from typing import Optional, Union, List, Dict, Any

import aiohttp

from modules.web.web_searcher.abc_searcher import AbstractSearcher, ResultType, TextFormat, FreshnessOption, \
    SafeSearchLevel


class BingWebSearcher(AbstractSearcher):
    """
    Implementation of AbstractSearcher for Bing Search API.
    """

    class ResponseFilterOptions(str, Enum):
        COMPUTATION = "Computation"
        ENTITIES = "Entities"
        IMAGES = "Images"
        NEWS = "News"
        PLACES = "Places"
        RELATED_SEARCHES = "RelatedSearches"
        SPELL_SUGGESTIONS = "SpellSuggestions"
        TIMEZONE = "TimeZone"
        TRANSLATIONS = "Translations"
        VIDEOS = "Videos"
        WEBPAGES = "Webpages"

    class PromoteOptions(str, Enum):
        COMPUTATION = "Computation"
        ENTITIES = "Entities"
        IMAGES = "Images"
        NEWS = "News"
        RELATED_SEARCHES = "RelatedSearches"
        SPELL_SUGGESTIONS = "SpellSuggestions"
        TIMEZONE = "TimeZone"
        VIDEOS = "Videos"
        WEBPAGES = "Webpages"

    def __init__(self, api_key: Optional[str] = None):
        self.endpoint = "https://api.bing.microsoft.com/v7.0/search"
        super().__init__(api_key)

    async def search(
            self,
            query: str,
            count: Optional[int] = 10,
            offset: Optional[int] = 0,
            safe_search: Optional[SafeSearchLevel] = SafeSearchLevel.MODERATE,
            language: Optional[str] = "en",
            country: Optional[str] = "US",
            freshness: Optional[Union[FreshnessOption, str]] = None,
            timeout: Optional[float] = 10.0,
            answer_count: Optional[int] = None,
            promote: Optional[Union[PromoteOptions, str, List[Union[PromoteOptions, str]]]] = None,
            response_filter: Optional[
                Union[ResponseFilterOptions, str, List[Union[ResponseFilterOptions, str]]]] = None,
            text_decorations: Optional[bool] = False,
            text_format: Optional[TextFormat] = TextFormat.RAW
    ) -> List[Dict[str, Any]]:
        """
        Perform a web search using Bing API.

        Args:
            query: The search query term
            count: Number of search results to return (max 50)
            offset: Number of results to skip (for pagination)
            safe_search: Filter for adult content (Off, Moderate, Strict)
            language: Language for results (maps to setLang)
            country: Country code (maps to cc)
            freshness: Filter results by age (Day, Week, Month)
            timeout: Request timeout in seconds
            answer_count: Number of answers to include
            promote: List of answers to include regardless of ranking
            response_filter: List of answers to include in response
            text_decorations: Whether to include decoration markers
            text_format: Type of markers for text decorations (Raw or Html)

        Returns:
            List of search result dictionaries
        """
        if not self.api_key:
            raise ValueError("Bing API key is required.")

        if not query:
            raise ValueError("Search query cannot be empty.")

        # Build query parameters
        params: Dict[str, Any] = {
            "q": query,
            "count": min(count, 50),  # Bing max is 50
            "offset": offset,
            "safeSearch": safe_search.value.capitalize() if isinstance(safe_search, SafeSearchLevel)
            else safe_search.capitalize() if safe_search
            else SafeSearchLevel.MODERATE.value.capitalize(),
            "setLang": language,
            "cc": country
        }

        # Add other parameters if provided
        if freshness is not None:
            params["freshness"] = freshness.value if isinstance(freshness, FreshnessOption) else freshness

        if answer_count is not None:
            params["answerCount"] = answer_count

        if promote is not None:
            if isinstance(promote, list):
                promote_values = [p.value if isinstance(p, Enum) else p for p in promote]
                params["promote"] = ",".join(promote_values)
            else:
                params["promote"] = promote.value if isinstance(promote, Enum) else promote

        if response_filter is not None:
            if isinstance(response_filter, list):
                filter_values = [f.value if isinstance(f, Enum) else f for f in response_filter]
                params["responseFilter"] = ",".join(filter_values)
            else:
                params["responseFilter"] = response_filter.value if isinstance(response_filter,
                                                                               Enum) else response_filter

        if text_decorations is not None:
            params["textDecorations"] = str(text_decorations).lower()

        if text_format is not None:
            params["textFormat"] = text_format.value if isinstance(text_format, TextFormat) else text_format

        headers = {
            "Ocp-Apim-Subscription-Key": self.api_key,
            "Accept": "application/json"
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                        self.endpoint,
                        params=params,
                        headers=headers,
                        timeout=timeout
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"Bing search failed: {response.status}, {error_text}")

                    result = await response.json()
                    return self._process_response(result)
        except Exception as e:
            raise Exception(f"Error during Bing search: {str(e)}")

    def _process_response(self, response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Process the API response and extract relevant information.

        Args:
            response: The raw API response.

        Returns:
            Processed list of search results.
        """
        results: List[Dict[str, Any]] = []

        # Process web pages
        if "webPages" in response and "value" in response["webPages"]:
            for page in response["webPages"]["value"]:
                results.append({
                    "type": ResultType.WEBPAGE,
                    "title": page.get("name", ""),
                    "snippet": page.get("snippet", ""),
                    "url": page.get("url", ""),
                    "display_url": page.get("displayUrl", ""),
                    "date_published": page.get("datePublished", "")
                })

        # Process news
        if "news" in response and "value" in response["news"]:
            for item in response["news"]["value"]:
                results.append({
                    "type": ResultType.NEWS,
                    "title": item.get("name", ""),
                    "description": item.get("description", ""),
                    "url": item.get("url", ""),
                    "publisher": item.get("provider", [{}])[0].get("name", "") if item.get("provider") else "",
                    "date_published": item.get("datePublished", "")
                })

        # Process videos
        if "videos" in response and "value" in response["videos"]:
            for video in response["videos"]["value"]:
                results.append({
                    "type": ResultType.VIDEO,
                    "title": video.get("name", ""),
                    "description": video.get("description", ""),
                    "url": video.get("hostPageUrl", ""),
                    "thumbnail": video.get("thumbnailUrl", ""),
                    "publisher": video.get("publisher", [{}])[0].get("name", "") if video.get("publisher") else "",
                    "duration": video.get("duration", "")
                })

        # Process images
        if "images" in response and "value" in response["images"]:
            for image in response["images"]["value"]:
                results.append({
                    "type": ResultType.IMAGE,
                    "title": image.get("name", ""),
                    "url": image.get("contentUrl", ""),
                    "thumbnail": image.get("thumbnailUrl", ""),
                    "source_page": image.get("hostPageUrl", "")
                })

        return results
