from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from .types import EmbeddingResult, EmbeddingConfig


class EmbeddingAdapter(ABC):
    """Abstract base class for embedding adapters."""
    
    def __init__(self, config: EmbeddingConfig):
        self.config = config
        self.model = config.model
    
    @abstractmethod
    async def embed_texts(self, texts: List[str], **kwargs) -> EmbeddingResult:
        """Embed multiple texts and return embeddings with metadata."""
        pass
    
    @abstractmethod
    async def embed_query(self, query: str, **kwargs) -> List[float]:
        """Embed a single query text and return embedding vector."""
        pass
    
    @property
    @abstractmethod
    def max_batch_size(self) -> int:
        """Maximum number of texts that can be embedded in a single batch."""
        pass
    
    @property
    @abstractmethod
    def dimensions(self) -> int:
        """Dimensions of the embedding vectors."""
        pass
    
    @abstractmethod
    async def _embed_batch(self, texts: List[str], **kwargs) -> EmbeddingResult:
        """Internal method to embed a batch of texts."""
        pass
    
    async def embed_texts_batched(self, texts: List[str], **kwargs) -> List[List[float]]:
        """Embed texts in batches respecting max_batch_size."""
        if len(texts) <= self.max_batch_size:
            result = await self._embed_batch(texts, **kwargs)
            return result.embeddings
        
        all_embeddings = []
        for i in range(0, len(texts), self.max_batch_size):
            batch = texts[i:i + self.max_batch_size]
            result = await self._embed_batch(batch, **kwargs)
            all_embeddings.extend(result.embeddings)
        
        return all_embeddings