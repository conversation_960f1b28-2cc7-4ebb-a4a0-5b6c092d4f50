services:
  app:
    build:
      context: .
      target: development
      args:
        BUILDKIT_INLINE_CACHE: 1
    ports:
      - "8000:8000"
    volumes:
      - .:/app
      - /app/venv  # Don't sync virtual environment if created inside container
      - /app/__pycache__  # Don't sync pycache
    environment:
      - ENV=dev
      - DB_HOST=host.docker.internal
      - DOCKER_BUILDKIT=1
    env_file:
      - .env
    restart: unless-stopped
    extra_hosts:
      - "host.docker.internal:host-gateway"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3