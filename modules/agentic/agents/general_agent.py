import asyncio

from pydantic_ai import Agent
from pydantic_ai.messages import FunctionToolCallEvent, FunctionToolResultEvent, \
    FinalResultEvent, ToolCallPartDelta, PartStartEvent, PartDeltaEvent, TextPartDelta, ModelMessage
from pydantic_ai.settings import ModelSettings
from pydantic_ai.tools import Tool, RunContext

from const import EConnectionState, EAIStyle
from modules.agentic.agents.abc_agent import ABCAgent
from modules.agentic.prompts.general_agent import general_deep_prompt, GeneralAgentInputs, general_concise_prompt
from modules.agentic.tools import with_event, with_db_session
from modules.agentic.tools.e_phy.alternative_products import find_alternative_products
from modules.agentic.tools.e_phy.application_guidelines import get_application_guidelines
from modules.agentic.tools.e_phy.crop_protection_advisor import find_crop_protection_solutions
from modules.agentic.tools.e_phy.integrated_management import create_integrated_management_plan
from modules.agentic.tools.e_phy.product_authorization import get_product_authorization_info
from modules.agentic.tools.e_phy.product_details import get_product_comprehensive_info
# E-Phy agricultural tools
from modules.agentic.tools.e_phy.product_search import search_agricultural_products
from modules.agentic.tools.e_phy.regulatory_compliance import check_regulatory_compliance
from modules.agentic.tools.e_phy.substance_intelligence import analyze_active_substances
from modules.agentic.tools.e_phy_web_search import search_e_phy_data
# Knowledge base search tools for RAG
from modules.agentic.tools.knowledge_base_search import search_knowledge_base, get_document_chunks
from modules.agentic.tools.mes_parcelles import (
    get_farm_overview, analyze_parcels, analyze_farm_activities, get_farm_insights,
    DetailLevel, ParcelScope, SortOption, TimeScope, ActivityFocus, GroupingOption, InsightType
)
from modules.agentic.tools.personal_data import get_purchase_orders_history, get_farm_raw_data
from modules.agentic.tools.weather import get_current_agricultural_weather, get_agricultural_forecast, \
    get_precipitation_forecast, get_agricultural_historical_weather, check_frost_risk, get_growing_condition_assessment
from modules.agentic.utils.helper import is_advisor, is_farmer
from modules.agentic.utils.response_processor import ResponseProcessor
from schemas.agent import SAgentDeps
from schemas.assistant import SChatRequest
from utils.logger import logger


# Agent class that processes user queries and generates responses
class GeneralAgent(ABCAgent):
    """
    General purpose agent_type for handling various types of queries.
    Extends the ABCAgent base class.
    """

    def __init__(self, style: EAIStyle = EAIStyle.deep):
        self.style = style
        super().__init__()

    def _configure_agent(self, agent: Agent) -> Agent:
        @agent.tool(prepare=is_advisor)
        async def _get_purchase_orders_history(ctx: RunContext[SAgentDeps[SChatRequest]]):
            """
            Access farmer's agricultural supply purchase history for cost analysis and planning.
            
            Provides comprehensive overview of previous purchases including products, suppliers, 
            costs, and timing patterns. Critical for budget planning, supplier relationship 
            management, and identifying cost optimization opportunities. Use when farmers 
            discuss purchasing decisions, budget concerns, or supplier comparisons.
            """
            logger.debug(f"Fetching purchase orders for user: {ctx.deps.data.personal_context.id}")
            if ctx.deps.data.personal_context:
                user_id = ctx.deps.data.personal_context.id
                raw_data = await get_purchase_orders_history(user_id)
                return raw_data
            else:
                return "No purchase orders history available."

        @agent.tool(prepare=is_farmer)
        async def get_farm_overview_and_statistics(ctx: RunContext[SAgentDeps[SChatRequest]],
                                                   detail_level: DetailLevel = "summary") -> str:
            """
            Essential farm overview analyzer - Get complete statistics, crop distribution, and operational context.
            
            This is the PRIMARY farm intelligence tool that provides comprehensive operational context
            including total area, parcel count, crop distribution with percentages, intervention summaries,
            and farmer/SIRET identifiers. Essential foundation for all agricultural recommendations.
            
            RETURNS STRUCTURED DATA:
            - Farm summary with total hectares and parcel count
            - Detailed crop distribution with percentages and parcel counts
            - Intervention type summary with occurrence counts
            - Farmer ID, SIRET
            - Optional: Individual parcel breakdown or detailed intervention history
            
            CRITICAL: Use this tool FIRST for any farm-related query to establish operational context
            before providing recommendations. This replaces multiple individual stat queries.
            
            WHEN TO USE:
            - "Tell me about my farm" / "Farm overview" / "What's my operation like?"
            - Before providing agricultural advice or recommendations
            - "How much land do I have?" / "What crops am I growing?" / "Farm statistics"
            - "Show me my parcels" / "What's my total area?" / "Crop breakdown"
            
            Args:
                detail_level: Output depth - "summary" (stats + crops + interventions), 
                            "detailed" (+ individual parcels + recent work), "parcels_only" (parcel focus)
            
            Returns: Formatted farm intelligence report with hectares, crops, percentages, intervention counts,
            and MesParcelles source citations for comprehensive operational understanding.
            
            Example Usage:
                - "How big is my farm?" → detail_level="summary"
                - "Tell me about my operation" → detail_level="summary"
                - "Show me all my fields" → detail_level="parcels_only"
                - "Complete farm breakdown" → detail_level="detailed"
            """
            if ctx.deps.data.personal_context:
                user_id = ctx.deps.data.personal_context.id
                # Map string parameter to the expected type
                detail_map: dict[str, DetailLevel] = {
                    "summary": "summary",
                    "detailed": "detailed",
                    "parcels_only": "parcels_only"
                }
                mapped_detail = detail_map.get(detail_level, "summary")
                return await get_farm_overview(user_id, mapped_detail)
            else:
                return ("ACCOUNT NOT CONNECTED: I need access to your farm data to provide insights. "
                        "Please ensure your MesParcelles account is properly connected.")

        @agent.tool(prepare=is_farmer)
        async def analyze_field_parcels_and_crops(ctx: RunContext[SAgentDeps[SChatRequest]],
                                                  scope: ParcelScope = "all",
                                                  search_criteria: str = None,
                                                  sort_by: SortOption = "area") -> str:
            """
            Intelligent parcel analysis tool for detailed field-specific insights and planning.
            
            Essential for answering questions about specific fields, crop locations, or parcel-based
            planning decisions. Provides comprehensive parcel data with flexible filtering to support
            precision agriculture and field-specific recommendations.
            
            Use when farmers ask about:
            - Specific parcel names or field locations
            - Crop distribution across parcels
            - Field-specific management decisions
            - Parcel performance comparisons
            
            Args:
                scope: "all" for all parcels, "single" for specific parcel, "by_crop" for crop-based, 
                      "by_location" for location-based analysis
                filter_by: Filter criteria - parcel name, crop name, or location code depending on scope
                sort_by: "area", "activity", "crop", "location", or "name" for result ordering
            
            Returns: Detailed parcel analysis with areas, crops, locations, and intervention histories
            enabling precise field-specific recommendations and management decisions.
            """
            if ctx.deps.data.personal_context:
                user_id = ctx.deps.data.personal_context.id
                # Map string parameters to expected types
                scope_map: dict[str, ParcelScope] = {
                    "all": "all", "single": "single",
                    "by_crop": "by_crop", "by_location": "by_location"
                }
                sort_map: dict[str, SortOption] = {
                    "area": "area", "activity": "activity", "crop": "crop",
                    "location": "location", "name": "name"
                }
                mapped_scope = scope_map.get(scope, "all")
                mapped_sort = sort_map.get(sort_by, "area")
                return await analyze_parcels(user_id, mapped_scope, search_criteria, mapped_sort)
            else:
                return "ACCOUNT NOT CONNECTED: Cannot access parcel data without MesParcelles connection."

        @agent.tool(prepare=is_farmer)
        async def analyze_farm_operations(ctx: RunContext[SAgentDeps[SChatRequest]],
                                          time_scope: str = "recent",
                                          focus: str = "all",
                                          grouping: str = "chronological",
                                          limit: int = 10) -> str:
            """
            Comprehensive farm activity analysis for operational insights and strategic planning.
            
            Critical tool for understanding farming patterns, tracking operations, and identifying
            opportunities for optimization. Analyzes intervention timing, frequency, and effectiveness
            to support evidence-based agricultural decision making and operational planning.
            
            Use when farmers ask about:
            - Recent farm work or operational timeline
            - Specific intervention types (fertilization, planting, harvesting)
            - Seasonal activity patterns or planning
            - Operational efficiency and workload analysis
            
            Args:
                time_scope: "recent" for latest activities, "this_season" for 2025 season, 
                          "date_range" or "specific_date" (requires focus with dates)
                focus: "all", "fertilization", "planting", "seeding", "treatment", "harvest", 
                      or specific crop/parcel name for targeted analysis
                grouping: "chronological", "by_crop", "by_parcel", "by_type", "by_location"
                limit: Maximum activities to return for recent scope (default: 10)
            
            Returns: Detailed operational analysis with dates, intervention types, crops, parcels,
            and areas worked to enable data-driven farming decisions and planning optimization.
            """
            if ctx.deps.data.personal_context:
                user_id = ctx.deps.data.personal_context.id
                # Map string parameters to expected types
                time_map: dict[str, TimeScope] = {
                    "recent": "recent", "this_season": "this_season",
                    "date_range": "date_range", "specific_date": "specific_date"
                }
                focus_map: dict[str, ActivityFocus] = {
                    "all": "all", "fertilization": "fertilization", "planting": "planting",
                    "seeding": "seeding", "treatment": "treatment", "harvest": "harvest"
                }
                group_map: dict[str, GroupingOption] = {
                    "chronological": "chronological", "by_crop": "by_crop", "by_parcel": "by_parcel",
                    "by_type": "by_type", "by_location": "by_location"
                }
                mapped_time = time_map.get(time_scope, "recent")
                mapped_focus = focus_map.get(focus, focus)  # Allow custom values for crops/parcels
                mapped_group = group_map.get(grouping, "chronological")
                return await analyze_farm_activities(user_id, mapped_time, mapped_focus, mapped_group, limit)
            else:
                return "ACCOUNT NOT CONNECTED: Cannot access activity data without MesParcelles connection."

        @agent.tool(prepare=is_farmer)
        async def generate_farm_insights(ctx: RunContext[SAgentDeps[SChatRequest]],
                                         insight_type: str = "patterns",
                                         focus_area: str = None,
                                         timeframe_days: int = 30) -> str:
            """
            Advanced agricultural intelligence tool providing actionable insights and strategic recommendations.
            
            Analyzes farm data patterns to identify optimization opportunities, management gaps, and
            strategic next steps. Uses agricultural expertise and data analysis to provide intelligent
            recommendations that improve farm efficiency, productivity, and decision-making.
            
            CRITICAL: Use this tool when farmers need strategic advice, planning guidance, or when
            identifying optimization opportunities and management improvements.
            
            Args:
                insight_type: "patterns" for trends analysis, "gaps" for missed opportunities,
                            "efficiency" for operational optimization, "next_steps" for recommendations,
                            "comparison" for performance analysis, "workload" for planning optimization
                focus_area: Specific crop, parcel, or intervention type for targeted insights (optional)
                timeframe_days: Analysis window in days (default: 30)
            
            Returns: Intelligent agricultural insights with actionable recommendations based on
            pattern recognition, best practices, and farm-specific data analysis to optimize
            farming operations and strategic decision-making.
            """
            if ctx.deps.data.personal_context:
                user_id = ctx.deps.data.personal_context.id
                # Map string parameter to expected type
                insight_map: dict[str, InsightType] = {
                    "patterns": "patterns", "gaps": "gaps", "efficiency": "efficiency",
                    "next_steps": "next_steps", "comparison": "comparison", "workload": "workload"
                }
                mapped_insight = insight_map.get(insight_type, "patterns")
                return await get_farm_insights(user_id, mapped_insight, focus_area, timeframe_days)
            else:
                return "ACCOUNT NOT CONNECTED: Cannot generate insights without MesParcelles farm data."

        @agent.tool(prepare=is_farmer)
        async def get_all_farms_related_rawdata(ctx: RunContext[SAgentDeps[SChatRequest]]) -> str:
            """
            Get current farm data from MesParcelles for personalized agricultural insights.

            This tool retrieves and processes the current farmer's MesParcelles data, providing
            structured information about their specific parcels, crops, and recent agricultural
            activities. Use this tool when needing context about the farmer's actual fields,
            planting history, fertilization schedule, or crop distribution to provide tailored
            farming recommendations.

            The data includes:
            - Farm overview (total area, number of parcels)
            - Current crop distribution with percentages
            - Detailed parcel information (size, current crops, location)
            - Recent interventions (plantings, fertilization, etc.)
            - Agricultural calendar showing seasonal activities

            When to use:
                Use when other tools don't provide enough context or specific information about the farmer's data.

            Returns:
               Structured farm management report specific to the current user
            """
            logger.debug(f"Fetching farm insights for user: {ctx.deps.data.personal_context.id}")
            if ctx.deps.data.personal_context:
                user_id = ctx.deps.data.personal_context.id
                raw_data = await get_farm_raw_data(user_id)
                return raw_data
            else:
                return "No farm insights available."

        @agent.instructions
        async def system_prompt(ctx: RunContext[SAgentDeps[SChatRequest]]) -> str:
            # Build context more efficiently
            messages = ""
            if ctx.deps.data.message_chain and len(ctx.deps.data.message_chain) > 0:
                # Limit message history to last 5 messages for better performance
                recent_messages = ctx.deps.data.message_chain[-10:] if len(
                    ctx.deps.data.message_chain) > 10 else ctx.deps.data.message_chain
                messages = self.get_history_context(recent_messages)

            additional_context = ""

            # Generate prompt with optimized inputs
            prompt_inputs = GeneralAgentInputs(
                additional_context=additional_context,
                previous_messages=messages,
                attachments=bool(ctx.deps.data.attachments)
            )

            prompt = self._get_styled_prompt().format(prompt_inputs)

            # Log prompt length for optimization insights
            logger.debug(f"Generated prompt length: {len(prompt)} characters")

            return prompt

        return agent

    def _get_styled_prompt(self):
        if self.style == EAIStyle.deep:
            return general_deep_prompt
        else:
            return general_concise_prompt

    def _get_system_prompt(self):
        return ()

    def _get_tools(self):
        return [
            # Weather tools
            Tool(with_event(get_current_agricultural_weather, "Analysing weather data..."), takes_ctx=True),
            Tool(with_event(get_agricultural_forecast, "Analysing weather forecast..."), takes_ctx=True),
            Tool(with_event(get_precipitation_forecast, "Analysing precipitation forecast..."), takes_ctx=True),
            Tool(with_event(get_agricultural_historical_weather, "Analysing historical weather data..."),
                 takes_ctx=True),
            Tool(with_event(check_frost_risk, "Analysing frost risk..."), takes_ctx=True),
            Tool(with_event(get_growing_condition_assessment, "Analysing growing conditions..."), takes_ctx=True),

            # Knowledge Base / RAG tools
            Tool(with_event(search_knowledge_base, "🔍 Searching knowledge base"), takes_ctx=True),
            Tool(with_event(get_document_chunks, "📋 Getting document info"), takes_ctx=True),

            # E-Phy agricultural product tools
            Tool(with_db_session(search_agricultural_products, "Searching agricultural products..."),
                 takes_ctx=True),
            Tool(with_db_session(get_product_comprehensive_info, "Getting comprehensive product information..."),
                 takes_ctx=True),
            Tool(with_db_session(find_crop_protection_solutions, "Finding crop protection solutions..."),
                 takes_ctx=True),
            Tool(with_db_session(find_alternative_products, "Finding alternative products..."),
                 takes_ctx=True),
            Tool(with_db_session(get_application_guidelines, "Getting application guidelines..."),
                 takes_ctx=True),
            Tool(with_db_session(create_integrated_management_plan, "Creating integrated management plan..."),
                 takes_ctx=True),
            Tool(with_db_session(check_regulatory_compliance, "Checking regulatory compliance..."),
                 takes_ctx=True),
            Tool(with_db_session(analyze_active_substances, "Analyzing active substances..."),
                 takes_ctx=True),
            Tool(with_db_session(get_product_authorization_info, "Getting product authorization information..."),
                 takes_ctx=True),
            Tool(with_event(search_e_phy_data, "Searching E-Phy data..."))
        ]

    def _get_model(self) -> str:
        """Map internal model names to actual API model names"""
        model_mapping = {
            # EAIStyle.deep: "mistral:mistral-large-latest",
            EAIStyle.deep: "openai:gpt-4o",
            EAIStyle.shallow: "openai:gpt-4o-mini",  # Can be changed to a different model
        }
        return model_mapping.get(self.style, "openai:gpt-4o")

    def _get_model_settings(self) -> ModelSettings:
        return ModelSettings(
            temperature=0.2,  # Balanced temperature for consistency while preserving natural language
            parallel_tool_calls=True,  # Enable parallel tool execution for faster responses
            # max_tokens=3000,  # Reduced tokens to encourage concise, focused responses
            top_p=0.8,  # Balanced top_p for natural vocabulary selection
            timeout=60.0,  # Reasonable timeout to prevent hanging requests
        )

    #
    async def run(self, request: SChatRequest):
        pass
        # return await self.agent.run(request.query)

    # Process a user query and generate a streaming response
    async def run_stream(self, request: SChatRequest, processor: ResponseProcessor,
                         message_history: list[ModelMessage] = None, attachments: list = None):
        """Process a user query and generate a streaming response"""

        try:

            # Update processing state
            await processor.add_event(
                processor.create_connection_event(EConnectionState.processing, "Processing your request..."))

            output_messages: list[str] = []

            # Prepare input for the agent - combine query with file content items if provided
            agent_input = [request.query]
            if attachments:
                agent_input.extend(attachments)
                logger.info(f"Including {len(attachments)} file content items in agent input")

            # Use iter() for granular control over streaming
            async with self.agent.iter(agent_input, deps=SAgentDeps(data=request, meta=processor),
                                       message_history=message_history) as run:

                user_response_started = False

                async for node in run:
                    # Check if response processor has encountered a formatting error
                    if processor.error_occurred:
                        logger.warning("Stopping agent iteration due to response formatting error")
                        break

                    # Handle user prompt node
                    if Agent.is_user_prompt_node(node):
                        if node.user_prompt:
                            logger.debug(f"Processing user prompt: {node.user_prompt[0]}...")
                            output_messages.append(f'=== UserPromptNode: {node.user_prompt[0]} ===')

                    # Handle model request nodes - but be selective about what we stream
                    elif Agent.is_model_request_node(node):
                        logger.debug("Model processing...")
                        output_messages.append(
                            '=== ModelRequestNode: streaming partial request tokens ==='
                        )
                        try:
                            async with node.stream(run.ctx) as request_stream:
                                part_types = {}  # Track type of each part by index
                                stream_timeout_count = 0
                                max_stream_timeout = 3

                                async for event in request_stream:
                                    # Check for processor errors during stream processing
                                    if processor.error_occurred:
                                        logger.warning("Stopping request stream due to formatting error")
                                        break

                                    if isinstance(event, PartStartEvent):
                                        # Track the type of each part by its index
                                        is_tool_call = hasattr(event.part, 'tool_name') and event.part.tool_name
                                        part_types[event.index] = is_tool_call

                                        if is_tool_call:
                                            logger.debug(f"Starting tool call: {event.part.tool_name}")
                                        else:
                                            logger.debug(f"Starting text response part")
                                            # Capture successful messages before breaking out
                                            self._capture_successful_messages(run, processor)
                                            # Check if PartStartEvent contains initial content
                                            if hasattr(event.part, 'content') and event.part.content:
                                                initial_content = event.part.content
                                                logger.debug(
                                                    f"Found initial content in PartStartEvent: {initial_content[:100]}...")

                                                if not user_response_started:
                                                    await processor.add_event(
                                                        processor.create_connection_event(EConnectionState.responding,
                                                                                          "Generating response..."))
                                                    user_response_started = True

                                                # Process the initial content chunk
                                                try:
                                                    await asyncio.wait_for(
                                                        processor.process_chunk(initial_content),
                                                        timeout=10.0
                                                    )
                                                except asyncio.TimeoutError:
                                                    logger.warning("Initial content processing timeout")
                                                    processor.error_occurred = True
                                                    break
                                                except Exception as e:
                                                    logger.error(f"Error processing initial content: {str(e)}")
                                                    processor.error_occurred = True
                                                    break

                                                # Check for errors after processing initial content
                                                if processor.error_occurred:
                                                    logger.warning(
                                                        "Format error detected during initial content processing")
                                                    break

                                        output_messages.append(
                                            f'[Request] Starting part {event.index}: {event.part!r}'
                                        )
                                    elif isinstance(event, PartDeltaEvent):
                                        if isinstance(event.delta, TextPartDelta):
                                            # Only stream text if this part is not a tool call
                                            is_current_part_tool_call = part_types.get(event.index, False)
                                            if not is_current_part_tool_call:
                                                if not user_response_started:
                                                    await processor.add_event(
                                                        processor.create_connection_event(EConnectionState.responding,
                                                                                          "Generating response..."))
                                                    user_response_started = True

                                                chunk = event.delta.content_delta
                                                if chunk is not None:
                                                    try:
                                                        await asyncio.wait_for(
                                                            processor.process_chunk(chunk),
                                                            timeout=10.0
                                                        )
                                                        stream_timeout_count = 0  # Reset on success
                                                    except asyncio.TimeoutError:
                                                        stream_timeout_count += 1
                                                        logger.warning(
                                                            f"Chunk processing timeout {stream_timeout_count}/{max_stream_timeout}")
                                                        if stream_timeout_count >= max_stream_timeout:
                                                            logger.error("Maximum chunk processing timeouts reached")
                                                            processor.error_occurred = True
                                                            break

                                                    # Check for errors after processing chunk
                                                    if processor.error_occurred:
                                                        logger.warning("Format error detected during chunk processing")

                                                        break
                                            else:
                                                # This is tool call JSON/text, don't stream to user
                                                logger.debug(
                                                    f"Skipping tool call text: {event.delta.content_delta}...")

                                            # output_messages.append(
                                            #     f'[Request] Part {event.index} text delta: {event.delta.content_delta!r}'
                                            # )
                                        elif isinstance(event.delta, ToolCallPartDelta):
                                            # This is definitely tool call args, never stream to user
                                            # Mark this part as a tool call in case it wasn't detected earlier
                                            part_types[event.index] = True
                                            # logger.debug(f"Tool call args delta: {event.delta.args_delta}")
                                            # output_messages.append(
                                            #     f'[Request] Part {event.index} args_delta={event.delta.args_delta}'
                                            # )

                                    elif isinstance(event, FinalResultEvent):
                                        if event.tool_name:
                                            logger.debug(f"Tool call part completed: {event.tool_name}")
                                        else:
                                            logger.debug("Text response part completed")
                                        output_messages.append(
                                            f'[Result] The model produced a final output (tool_name={event.tool_name})'
                                        )

                        except AssertionError as e:
                            if "ReferenceChunk" in str(e):
                                logger.warning(f"Skipping unsupported ReferenceChunk: {str(e)}")
                            else:
                                logger.error(f"Assertion error in model request processing: {str(e)}")
                                processor.error_occurred = True

                        except asyncio.TimeoutError:
                            logger.error("Timeout during model request processing")
                            processor.error_occurred = True

                        except Exception as e:
                            logger.error(f"Error processing model request node: {str(e)}", exc_info=True)
                            processor.error_occurred = True
                    # Handle tool execution nodes
                    elif Agent.is_call_tools_node(node):
                        logger.debug("Executing tools (not streamed to user)...")
                        # A handle-response node => The model returned some data, potentially calls a tool
                        output_messages.append(
                            '=== CallToolsNode: streaming partial response & tool usage ==='
                        )
                        # Process tool execution but don't stream results to user
                        async with node.stream(run.ctx) as tool_stream:
                            async for event in tool_stream:
                                if isinstance(event, FunctionToolCallEvent):
                                    # Enhanced logging for tool call parameters
                                    tool_name = event.part.tool_name
                                    tool_args = event.part.args
                                    tool_call_id = event.part.tool_call_id

                                    logger.info(f"TOOL CALL: name={tool_name}, args={tool_args}, id={tool_call_id}")
                                    output_messages.append(
                                        f'[Tools] The LLM calls tool={tool_name!r} with args={tool_args} (tool_call_id={tool_call_id!r})'
                                    )
                                elif isinstance(event, FunctionToolResultEvent):
                                    # Enhanced logging for tool results
                                    tool_call_id = event.tool_call_id
                                    tool_result = event.result.content

                                    logger.info(f"TOOL RESPONSE: id={tool_call_id}, result={tool_result}")
                                    output_messages.append(
                                        f'[Tools] Tool call {tool_call_id!r} returned => {tool_result}'
                                    )
                    # Handle end node
                    elif Agent.is_end_node(node):
                        logger.debug("Agent run completed")
                        output_messages.append(
                            f'=== Final Agent Output: {run.result.output} ==='
                        )

                # Log usage information and capture successful messages for retry
                if hasattr(run, 'result') and run.result:
                    logger.debug(f"Usage: {run.result.usage()}")

                elif hasattr(run, 'ctx') and run.ctx:
                    logger.debug(f"Usage: {run.ctx.state.usage}")

                # print("\n".join(output_messages))
                # Process any remaining content in the buffer
                await processor.finalize()


        except Exception as e:
            logger.error(f"Error processing query: {str(e)}", exc_info=True)
            # Mark error occurred when the LLM failure happens - let retry handler manage error events
            processor.error_occurred = True

    def _capture_successful_messages(self, run, processor: ResponseProcessor):
        """Capture successful messages from the current run for potential retry"""
        if run is None:
            logger.debug("No run context available for capturing successful messages")
            return

        try:
            if hasattr(run, 'result') and run.result:
                processor.successful_messages = run.result.all_messages()
                logger.info(f"Captured {len(processor.successful_messages)} successful messages from run.result")
            elif hasattr(run, 'ctx') and run.ctx:
                processor.successful_messages = run.ctx.state.message_history
                logger.info(f"Captured {len(processor.successful_messages)} successful messages from run.ctx")
            else:
                logger.warning("No message history available in run context for capture")
        except Exception as e:
            logger.warning(f"Failed to capture successful messages: {str(e)}")
