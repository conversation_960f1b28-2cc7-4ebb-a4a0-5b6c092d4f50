"""
Smart Product Search Tool for E-Phy Agricultural Database.
Provides intelligent product discovery with fuzzy matching, quality scoring, and multilingual support.


FIXES APPLIED:
1. Function category filtering: Fixed to use exact matching to prevent false positives
2. Search vector query: Fixed to use proper PostgreSQL syntax with text() and @@
3. Alternative names logic: Fixed conditional logic to properly build OR conditions
4. List conversion: Ensured scalars().all() results are converted to lists for extend()
"""
from dataclasses import dataclass
from typing import List, Optional, Dict, Any

from sqlalchemy import select, text, or_, and_, desc, asc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from models.e_phy import Product, ProductSubstance
from .types import (
    AuthorizationStatus, FunctionCategory, ProductType, SortBy, SearchConfidence
)


@dataclass
class ProductSearchResult:
    """Result structure for product search."""
    product_id: int
    product_name: str
    registration_number: str
    function_category: Optional[str]
    authorization_status: str
    is_currently_authorized: bool
    holder: str
    data_quality_score: float
    alternative_names: List[str]
    key_substances: List[str]
    usage_summary: Optional[str]
    search_confidence: float
    match_reasons: List[str]


@dataclass
class ProductSearchResponse:
    """Complete response for product search."""
    results: List[ProductSearchResult]
    total_found: int
    search_confidence: float
    query_suggestions: List[str]
    filters_applied: Dict[str, Any]
    data_gaps: List[str]


async def search_agricultural_products(
        session: AsyncSession,
        query: str,
        function_category: Optional[List[FunctionCategory]] = None,
        authorization_status: AuthorizationStatus = "authorized_only",
        product_type: ProductType = "PPP",
        include_alternatives: bool = True,
        limit: int = 20,
        sort_by: SortBy = "relevance"
) -> ProductSearchResponse:
    """
    Universal product discovery with intelligent ranking and gap mitigation.
    
    Args:
        query: Product name, partial name, or description
        function_category: Filter by function categories
        authorization_status: Filter by authorization status
        product_type: Filter by product type
        include_alternatives: Include alternative names in search
        limit: Maximum number of results
        sort_by: Sort results by specified criteria
    
    Returns:
        ProductSearchResponse with ranked results and metadata
    """

    # Build base query with necessary joins
    base_query = select(Product).options(
        selectinload(Product.substances).selectinload(ProductSubstance.substance)
    )

    # Apply filters
    filters = []

    # Authorization filter
    if authorization_status == "authorized_only":
        filters.append(Product.is_currently_authorized == True)
    elif authorization_status == "withdrawn":
        filters.append(Product.is_currently_authorized == False)

    # Product type filter
    if product_type == "PPP":
        filters.append(Product.product_type == "PPP")

    # Function category filter
    if function_category:
        category_filters = []
        for cat in function_category:
            # Exact match or pipe-separated exact match to avoid false positives
            category_filters.append(
                or_(
                    Product.function_category == cat,
                    Product.function_category.like(f"{cat} | %"),
                    Product.function_category.like(f"% | {cat}"),
                    Product.function_category.like(f"% | {cat} | %")
                )
            )
        filters.append(or_(*category_filters))

    # Apply filters to base query
    if filters:
        base_query = base_query.where(and_(*filters))

    # Search logic
    search_results = []
    query_suggestions = []
    data_gaps = []

    # Primary search: Full-text search
    if query.strip():
        # Format query for to_tsquery by joining words with &
        formatted_query = ' & '.join(query.strip().split())
        fts_query = base_query.where(
            text("search_vector @@ to_tsquery('french', :query)")
        ).params(query=formatted_query).order_by(
            desc(text("ts_rank_cd(search_vector, to_tsquery('french', :query))"))
        ).params(query=formatted_query)

        fts_results = await session.execute(fts_query.limit(limit))
        products = list(fts_results.scalars().all())

        # If limited results, try alternative approaches
        if len(products) < limit // 2:
            # Fuzzy name matching
            fuzzy_conditions = [
                Product.product_name.ilike(f"%{query}%"),
                Product.holder.ilike(f"%{query}%"),
                Product.registration_number.ilike(f"%{query}%")
            ]

            if include_alternatives:
                fuzzy_conditions.append(Product.alternative_names.ilike(f"%{query}%"))

            fuzzy_query = base_query.where(
                or_(*fuzzy_conditions)
            ).limit(limit - len(products))

            fuzzy_results = await session.execute(fuzzy_query)
            additional_products = fuzzy_results.scalars().all()

            # Combine results, avoiding duplicates
            existing_ids = {p.id for p in products}
            products.extend([p for p in additional_products if p.id not in existing_ids])
    else:
        # No query provided, return top products by criteria
        ordered_query = base_query.order_by(desc(Product.is_currently_authorized))
        if sort_by == "name":
            ordered_query = ordered_query.order_by(asc(Product.product_name))

        results = await session.execute(ordered_query.limit(limit))
        products = list(results.scalars().all())

    # Process results and calculate scores
    for product in products:
        # Calculate data quality score
        quality_score = await _calculate_data_quality_score(session, product)

        # Determine search confidence
        confidence = _calculate_search_confidence(product, query)

        # Get key substances
        substances = [ps.substance.name for ps in product.substances if ps.substance] if product.substances else []

        # Parse alternative names
        alt_names = []
        if product.alternative_names:
            alt_names = [name.strip() for name in product.alternative_names.split('|') if name.strip()]

        # Determine match reasons
        match_reasons = _get_match_reasons(product, query, include_alternatives)

        # Generate usage summary if missing
        usage_summary = product.product_summary
        if not usage_summary and product.function_category:
            usage_summary = f"{product.function_category} - {product.product_name}"

        result = ProductSearchResult(
            product_id=product.id,
            product_name=product.product_name,
            registration_number=product.registration_number,
            function_category=product.function_category,
            authorization_status=product.authorization_status or "Unknown",
            is_currently_authorized=product.is_currently_authorized or False,
            holder=product.holder or "Unknown",
            data_quality_score=quality_score,
            alternative_names=alt_names,
            key_substances=substances[:3],  # Limit to top 3
            usage_summary=usage_summary,
            search_confidence=confidence,
            match_reasons=match_reasons
        )

        search_results.append(result)

    # Sort results based on criteria
    search_results = _sort_results(search_results, sort_by)

    # Generate query suggestions and identify data gaps
    if len(search_results) < 5:
        query_suggestions = await _generate_query_suggestions(session, query, function_category)

    data_gaps = _identify_data_gaps(search_results)

    # Calculate overall search confidence
    overall_confidence = sum(r.search_confidence for r in search_results) / len(
        search_results) if search_results else 0.0

    return ProductSearchResponse(
        results=search_results,
        total_found=len(search_results),
        search_confidence=overall_confidence,
        query_suggestions=query_suggestions,
        filters_applied={
            "authorization_status": authorization_status,
            "function_category": function_category,
            "product_type": product_type,
            "include_alternatives": include_alternatives
        },
        data_gaps=data_gaps
    )


async def _calculate_data_quality_score(session: AsyncSession, product: Product) -> float:
    """Calculate data completeness score for a product."""
    score = 0.0
    weights = {
        'has_function_category': 0.2,
        'has_usage_description': 0.3,
        'has_authorization_status': 0.15,
        'is_currently_authorized': 0.1,
        'has_holder': 0.1,
        'has_alternative_names': 0.1,
        'has_substances': 0.05
    }

    # Check each criterion
    if product.function_category and product.function_category.strip():
        score += weights['has_function_category']

    if product.product_summary and product.product_summary.strip():
        score += weights['has_usage_description']

    if product.authorization_status and product.authorization_status.strip():
        score += weights['has_authorization_status']

    if product.is_currently_authorized is not None:
        score += weights['is_currently_authorized']

    if product.holder and product.holder.strip():
        score += weights['has_holder']

    if product.alternative_names and product.alternative_names.strip():
        score += weights['has_alternative_names']

    if product.substances:
        score += weights['has_substances']

    return min(score, 1.0)


def _calculate_search_confidence(product: Product, query: str) -> float:
    """Calculate search confidence based on match quality."""
    if not query.strip():
        return 0.5  # Default confidence for browsing

    query_lower = query.lower()

    # Exact name match
    if product.product_name.lower() == query_lower:
        return SearchConfidence.EXACT.value

    # Registration number match
    if product.registration_number.lower() == query_lower:
        return SearchConfidence.EXACT.value

    # Partial name match
    if query_lower in product.product_name.lower():
        return SearchConfidence.HIGH.value

    # Alternative name match
    if product.alternative_names and query_lower in product.alternative_names.lower():
        return SearchConfidence.HIGH.value

    # Holder match
    if product.holder and query_lower in product.holder.lower():
        return SearchConfidence.MEDIUM.value

    # Function category match
    if product.function_category and any(
            word in product.function_category.lower()
            for word in query_lower.split()
    ):
        return SearchConfidence.MEDIUM.value

    return SearchConfidence.LOW.value


def _get_match_reasons(product: Product, query: str, include_alternatives: bool) -> List[str]:
    """Identify why a product matched the search query."""
    reasons = []
    if not query.strip():
        return ["Listed in results"]

    query_lower = query.lower()

    if query_lower in product.product_name.lower():
        reasons.append("Product name match")

    if query_lower == product.registration_number.lower():
        reasons.append("Registration number match")

    if include_alternatives and product.alternative_names and query_lower in product.alternative_names.lower():
        reasons.append("Alternative name match")

    if product.holder and query_lower in product.holder.lower():
        reasons.append("Company name match")

    if product.function_category and any(
            word in product.function_category.lower()
            for word in query_lower.split()
    ):
        reasons.append("Function category match")

    if not reasons:
        reasons.append("Full-text search match")

    return reasons


def _sort_results(results: List[ProductSearchResult], sort_by: SortBy) -> List[ProductSearchResult]:
    """Sort results based on specified criteria."""
    if sort_by == "relevance":
        return sorted(results, key=lambda x: (x.search_confidence, x.data_quality_score), reverse=True)
    elif sort_by == "name":
        return sorted(results, key=lambda x: x.product_name)
    elif sort_by == "authorization_date":
        return sorted(results, key=lambda x: x.is_currently_authorized, reverse=True)
    else:
        return results


async def _generate_query_suggestions(
        session: AsyncSession,
        query: str,
        function_category: Optional[List[FunctionCategory]]
) -> List[str]:
    """Generate helpful query suggestions when results are limited."""
    suggestions = []

    if not query.strip():
        suggestions.append("Try searching for a specific product name")
        suggestions.append("Search by active substance (e.g., 'glyphosate')")
        suggestions.append("Use function category filters")
        return suggestions

    # Suggest broader terms
    if len(query) > 10:
        suggestions.append("Try shorter, more general terms")

    # Suggest function categories if not used
    if not function_category:
        suggestions.append("Consider adding function category filters")

    # Suggest alternative spellings
    suggestions.append("Check spelling or try alternative names")
    suggestions.append("Search by company name instead")

    return suggestions[:3]  # Limit suggestions


def _identify_data_gaps(results: List[ProductSearchResult]) -> List[str]:
    """Identify common data quality issues in results."""
    gaps = []

    if not results:
        return ["No products found matching criteria"]

    # Check for common data gaps
    missing_function = sum(1 for r in results if not r.function_category)
    missing_substances = sum(1 for r in results if not r.key_substances)
    low_quality = sum(1 for r in results if r.data_quality_score < 0.6)

    if missing_function / len(results) > 0.3:
        gaps.append("Function category information missing for some products")

    if missing_substances / len(results) > 0.5:
        gaps.append("Active substance information limited")

    if low_quality / len(results) > 0.4:
        gaps.append("Some products have incomplete data")

    return gaps
