from typing import Optional, List, Dict, Any, Tuple

from sqlalchemy import func, distinct, select
from sqlalchemy.ext.asyncio import AsyncSession

from models.e_phy import Product, ProductSubstance, ActiveSubstance, ProductUse, Crop, Target


class EPhyToolHelper:
    # Define soil type adjustments for dose recommendations
    soil_adjustments = {
        'sable': 0.9,  # Sandy soils might need lower doses
        'argile': 1.2,  # Clay soils might need higher doses
        'limon': 1.0,  # Loamy soils are standard
        'calcaire': 1.1,  # Calcareous soils might need higher doses
        'tourbe': 1.3  # Peaty soils might need higher doses
    }

    # Define climate adjustments for dose recommendations
    climate_adjustments = {
        'sec': 0.9,  # Dry conditions might need lower doses
        'humide': 1.1,  # Humid conditions might need higher doses
        'froid': 0.9,  # Cold conditions might need lower doses
        'chaud': 1.1  # Hot conditions might need higher doses
    }

    # Define growth stage map
    growth_stage_map = {
        (0, 9): 'germination/levée',
        (10, 19): 'développement des feuilles',
        (20, 29): 'tallage/formation de pousses',
        (30, 39): 'élongation de la tige',
        (40, 49): 'développement des parties végétatives de récolte',
        (50, 59): 'émergence de l\'inflorescence/épiaison',
        (60, 69): 'floraison',
        (70, 79): 'développement du fruit',
        (80, 89): 'maturation',
        (90, 99): 'sénescence'
    }

    # Define compatibility matrix between different product types
    compatibility_matrix = {
        'Herbicide': {'Herbicide': 'variable', 'Fongicide': 'bonne', 'Insecticide': 'bonne',
                      'Régulateur': 'variable', 'Engrais': 'bonne'},
        'Fongicide': {'Herbicide': 'bonne', 'Fongicide': 'bonne', 'Insecticide': 'bonne', 'Régulateur': 'bonne',
                      'Engrais': 'bonne'},
        'Insecticide': {'Herbicide': 'bonne', 'Fongicide': 'bonne', 'Insecticide': 'variable',
                        'Régulateur': 'bonne', 'Engrais': 'bonne'},
        'Régulateur': {'Herbicide': 'variable', 'Fongicide': 'bonne', 'Insecticide': 'bonne',
                       'Régulateur': 'déconseillée', 'Engrais': 'bonne'},
        'Engrais': {'Herbicide': 'bonne', 'Fongicide': 'bonne', 'Insecticide': 'bonne', 'Régulateur': 'bonne',
                    'Engrais': 'bonne'}
    }

    # Define organic farming allowed substances
    organic_allowed_substances = [
        'cuivre', 'soufre', 'bacillus', 'spinosad', 'azadirachtine', 'pyrèthre',
        'huile', 'argile', 'bicarbonate', 'phosphate', 'potassium', 'calcium',
        'magnésium', 'fer', 'zinc', 'manganèse', 'bore', 'silice'
    ]

    # Define substances not allowed in organic farming
    non_organic_keywords = [
        'glyphosate', 'pendiméthaline', 's-métolachlore', 'bentazone', 'bromoxynil',
        'mésotrione', 'métazachlore', 'diflufénican', 'prosulfocarbe', 'nicosulfuron'
    ]

    @classmethod
    def _format_concentration(cls, concentration: float, unit: str) -> str:
        """Format concentration with proper unit display"""
        if concentration is None:
            return "Non spécifié"

        if unit:
            return f"{concentration} {unit}"
        else:
            return f"{concentration}"

    @classmethod
    def _format_composition_range(cls, min_val: float, max_val: float, unit: str) -> str:
        """Format composition range display"""
        if min_val is not None and max_val is not None:
            if min_val == max_val:
                return f"{min_val} {unit}" if unit else f"{min_val}"
            else:
                return f"{min_val}-{max_val} {unit}" if unit else f"{min_val}-{max_val}"
        elif min_val is not None:
            return f"≥{min_val} {unit}" if unit else f"≥{min_val}"
        elif max_val is not None:
            return f"≤{max_val} {unit}" if unit else f"≤{max_val}"
        else:
            return "Non spécifié"

    @classmethod
    def _format_dose_range(cls, min_dose: float, max_dose: float, unit: str) -> str:
        """Format dose range display"""
        if min_dose is not None and max_dose is not None:
            if min_dose == max_dose:
                return f"{min_dose} {unit}" if unit else f"{min_dose}"
            else:
                return f"{min_dose}-{max_dose} {unit}" if unit else f"{min_dose}-{max_dose}"
        elif min_dose is not None:
            return f"≥{min_dose} {unit}" if unit else f"≥{min_dose}"
        elif max_dose is not None:
            return f"≤{max_dose} {unit}" if unit else f"≤{max_dose}"
        else:
            return "Non spécifié"

    @classmethod
    def _validate_dose_range(cls, min_dose: float, max_dose: float) -> dict:
        """Validate dose range for consistency"""
        if min_dose is not None and max_dose is not None:
            if min_dose > max_dose:
                return {"valid": False, "issue": "Dose minimale supérieure à la dose maximale"}
            elif min_dose == max_dose:
                return {"valid": True, "note": "Dose fixe"}
            else:
                return {"valid": True, "note": "Plage de doses valide"}
        else:
            return {"valid": True, "note": "Dose unique ou non spécifiée"}

    @classmethod
    def _format_growth_stage(cls, min_stage: str, max_stage: str) -> str:
        """Format growth stage display"""
        if min_stage is not None and max_stage is not None:
            if min_stage == max_stage:
                return f"BBCH {min_stage}"
            else:
                return f"BBCH {min_stage}-{max_stage}"
        elif min_stage is not None:
            return f"BBCH ≥{min_stage}"
        elif max_stage is not None:
            return f"BBCH ≤{max_stage}"
        else:
            return "Non spécifié"

    @classmethod
    def _categorize_severity(cls, severity) -> str:
        """Categorize hazard severity level"""
        if not severity:
            return "unknown"

        # Handle both string and numeric severity values
        if isinstance(severity, (int, float)):
            severity = str(severity)
        
        severity_lower = str(severity).lower()
        if any(word in severity_lower for word in ["élevé", "grave", "sévère", "majeur"]):
            return "high"
        elif any(word in severity_lower for word in ["moyen", "modéré", "intermédiaire"]):
            return "medium"
        elif any(word in severity_lower for word in ["faible", "léger", "mineur"]):
            return "low"
        else:
            return "unknown"

    @classmethod
    def _is_mandatory_condition(cls, importance: str) -> bool:
        """Check if a condition is mandatory"""
        if not importance:
            return False
        return importance.lower() in ["high", "élevé", "obligatoire", "mandatory"]

    @classmethod
    def _assess_data_quality(cls, response: dict) -> dict:
        """Assess overall data quality of the response"""
        basic_info = response.get("basic_info", {})

        completeness_score = 0
        total_fields = 10

        # Check basic info completeness
        if basic_info.get("product_name"):
            completeness_score += 1
        if basic_info.get("registration_number"):
            completeness_score += 1
        if basic_info.get("holder"):
            completeness_score += 1
        if basic_info.get("authorization_status"):
            completeness_score += 1
        if basic_info.get("function_category"):
            completeness_score += 1

        # Check other sections
        if response.get("active_substances"):
            completeness_score += 1
        if response.get("authorized_uses"):
            completeness_score += 1
        if response.get("safety_info"):
            completeness_score += 1
        if response.get("usage_conditions"):
            completeness_score += 1
        if response.get("organic_farming"):
            completeness_score += 1

        completeness_percentage = (completeness_score / total_fields) * 100

        # Determine quality level
        if completeness_percentage >= 80:
            quality_level = "excellent"
        elif completeness_percentage >= 60:
            quality_level = "good"
        elif completeness_percentage >= 40:
            quality_level = "fair"
        else:
            quality_level = "poor"

        return {
            "completeness_score": completeness_score,
            "total_fields": total_fields,
            "completeness_percentage": round(completeness_percentage, 1),
            "quality_level": quality_level,
            "recommendations": cls._generate_quality_recommendations(completeness_score, total_fields)
        }

    @classmethod
    def _generate_quality_recommendations(cls, score: int, total: int) -> list:
        """Generate recommendations to improve data quality"""
        recommendations = []

        if score < total * 0.5:
            recommendations.append("Données incomplètes - vérifier l'enregistrement du produit")
        if score < total * 0.7:
            recommendations.append("Compléter les informations manquantes pour une utilisation optimale")
        if score >= total * 0.8:
            recommendations.append("Données de bonne qualité - prêtes pour l'usage")

        return recommendations

    @classmethod
    def _generate_usage_recommendations(cls, product, uses, hazards) -> list:
        """Generate practical usage recommendations"""
        recommendations = []

        if not product.is_currently_authorized:
            recommendations.append("⚠️ Produit non autorisé - ne pas utiliser")
            return recommendations

        if uses:
            recommendations.append("✅ Produit autorisé avec usages définis")

            # Check for high-risk hazards
            high_risk_hazards = [h for h in hazards if cls._categorize_severity(h.hazard_severity) == "high"]
            if high_risk_hazards:
                recommendations.append("⚠️ Produit à risque élevé - respecter strictement les consignes de sécurité")

            # Check for buffer zones
            max_buffer = max([u.max_buffer_zone for u in uses if u.max_buffer_zone is not None], default=0)
            if max_buffer > 0:
                recommendations.append(f"📏 Respecter une zone non traitée de {max_buffer}m minimum")

            # Check for harvest intervals
            max_harvest_interval = max([u.harvest_interval_days for u in uses if u.harvest_interval_days is not None],
                                       default=0)
            if max_harvest_interval > 0:
                recommendations.append(f"⏰ Délai avant récolte: {max_harvest_interval} jours minimum")

        return recommendations

    @classmethod
    def _assess_organic_farming_suitability_enhanced(cls, product, substances) -> dict:
        """Enhanced organic farming assessment with better accuracy"""
        # Call the sync version of organic farming assessment since we have substances already
        substance_names = [s.name.lower() if hasattr(s, 'name') else s.ActiveSubstance.name.lower()
                          for s in substances]

        # Count matches with allowed substances
        organic_matches = sum(1 for allowed in cls.organic_allowed_substances
                             if any(allowed in s for s in substance_names))

        # Count matches with non-organic substances
        non_organic_matches = sum(1 for non_organic in cls.non_organic_keywords
                                 if any(non_organic in s for s in substance_names))

        # Determine potential suitability
        potentially_suitable = organic_matches > 0 and non_organic_matches == 0

        # Identify matching organic substances
        matching_organic_substances = []
        for substance in substance_names:
            for allowed in cls.organic_allowed_substances:
                if allowed in substance:
                    matching_organic_substances.append(substance)
                    break

        # Generate basic assessment
        basic_assessment = {
            "potentially_suitable": potentially_suitable,
            "organic_substance_count": organic_matches,
            "non_organic_substance_count": non_organic_matches,
            "matching_organic_substances": matching_organic_substances,
            "confidence": "low" if organic_matches == 0 else ("medium" if non_organic_matches > 0 else "high"),
            "note": "Cette évaluation est indicative et ne remplace pas la vérification auprès d'un organisme certificateur."
        }

        # Enhanced analysis
        substance_details = []
        for s in substances:
            substance_name = s.ActiveSubstance.name if hasattr(s, 'ActiveSubstance') else s.name
            substance_details.append({
                "name": substance_name,
                "potentially_organic": any(
                    allowed in substance_name.lower() for allowed in cls.organic_allowed_substances),
                "clearly_non_organic": any(
                    forbidden in substance_name.lower() for forbidden in cls.non_organic_keywords)
            })

        # Add enhanced assessment
        enhanced_assessment = basic_assessment.copy()
        enhanced_assessment.update({
            "substance_details": substance_details,
            "recommendation": cls._generate_organic_recommendation(basic_assessment),
            "verification_needed": "Toujours vérifier auprès d'un organisme certificateur avant utilisation en agriculture biologique"
        })

        return enhanced_assessment

    @classmethod
    def _generate_organic_recommendation(cls, assessment: dict) -> str:
        """Generate organic farming recommendation"""
        if assessment["potentially_suitable"]:
            if assessment["confidence"] == "high":
                return "Potentiellement adapté à l'agriculture biologique avec forte probabilité"
            elif assessment["confidence"] == "medium":
                return "Possiblement adapté à l'agriculture biologique - vérification recommandée"
            else:
                return "Statut incertain pour l'agriculture biologique"
        else:
            return "Probablement non adapté à l'agriculture biologique"

    @classmethod
    def _generate_compatibility_insights_enhanced(cls, product) -> dict:
        """Enhanced compatibility insights with validation"""
        basic_insights = cls._generate_compatibility_insights(product)

        # Add validation warnings
        warnings = []
        if not product.formulation_type:
            warnings.append("Type de formulation non spécifié - prudence pour les mélanges")

        if not product.function_category:
            warnings.append("Catégorie fonctionnelle non spécifiée - compatibilité difficile à évaluer")

        enhanced_insights = basic_insights.copy()
        enhanced_insights.update({
            "compatibility_warnings": warnings,
            "mixing_order_critical": bool(product.formulation_type and any(
                code in product.formulation_type for code in ["SC", "EC", "WP", "WG"])),
            "test_required": True
        })

        return enhanced_insights

    # Helper methods
    @classmethod
    async def _find_product(cls, session: AsyncSession, product_identifier: str) -> Optional[Product]:
        """Find a product by registration number or name"""
        if not product_identifier:
            return None
            
        product_identifier = product_identifier.strip()
        if not product_identifier:
            return None

        # Try to find by registration number first
        reg_query = select(Product).filter(
            Product.registration_number == product_identifier
        )
        result = await session.execute(reg_query)
        product = result.scalar_one_or_none()

        # If not found, try by name (partial match)
        if not product:
            # Handle NULL product names by checking for NOT NULL
            name_query = select(Product).filter(
                Product.product_name.isnot(None),
                func.lower(Product.product_name).ilike(f'%{product_identifier.lower()}%')
            ).limit(10)  # Get more results to find best match
            result = await session.execute(name_query)
            products = result.scalars().all()
            
            if products:
                # Find the best match
                query_lower = product_identifier.lower()
                
                # Look for exact matches first
                for p in products:
                    if p.product_name and query_lower == p.product_name.lower():
                        return p
                
                # Look for close matches (like "Karate Zeon" matching "KARATE AVEC TECHNOLOGIE ZEON")
                for p in products:
                    if p.product_name:
                        product_name_lower = p.product_name.lower()
                        # Check if all words in query are in product name
                        query_words = query_lower.split()
                        if all(word in product_name_lower for word in query_words):
                            return p
                
                # Return first result if no close match
                return products[0]

        return product

    @classmethod
    async def _assess_organic_farming_suitability(cls,
                                                  product: Product,
                                                  substances: Optional[List] = None,
                                                  session: Optional[AsyncSession] = None) -> Dict[str, Any]:
        """Assess if a product is potentially suitable for organic farming"""
        # Get substances if not provided
        if substances is None and session is not None:
            substances_query = select(ActiveSubstance).join(ProductSubstance).filter(
                ProductSubstance.product_id == product.id
            )
            result = await session.execute(substances_query)
            substances = result.scalars().all()

        substance_names = [s.name.lower() if isinstance(s, ActiveSubstance) else s.ActiveSubstance.name.lower()
                           for s in substances]

        # Count matches with allowed substances
        organic_matches = sum(1 for allowed in cls.organic_allowed_substances
                              if any(allowed in s for s in substance_names))

        # Count matches with non-organic substances
        non_organic_matches = sum(1 for non_organic in cls.non_organic_keywords
                                  if any(non_organic in s for s in substance_names))

        # Determine potential suitability
        potentially_suitable = organic_matches > 0 and non_organic_matches == 0

        # Identify matching organic substances
        matching_organic_substances = []
        for substance in substance_names:
            for allowed in cls.organic_allowed_substances:
                if allowed in substance:
                    matching_organic_substances.append(substance)
                    break

        # Generate assessment
        assessment = {
            "potentially_suitable": potentially_suitable,
            "organic_substance_count": organic_matches,
            "non_organic_substance_count": non_organic_matches,
            "matching_organic_substances": matching_organic_substances,
            "confidence": "low" if organic_matches == 0 else ("medium" if non_organic_matches > 0 else "high"),
            "note": "Cette évaluation est indicative et ne remplace pas la vérification auprès d'un organisme certificateur."
        }

        return assessment

    @classmethod
    def _generate_compatibility_insights(cls, product: Product) -> Dict[str, Any]:
        """Generate compatibility insights for tank mixing"""
        # Determine primary function
        primary_function = None
        
        # FIXED: Handle None or non-string function_category safely
        function_category = product.function_category
        if function_category is None:
            function_category = ""
        elif not isinstance(function_category, str):
            function_category = str(function_category)
        
        for function in ['Herbicide', 'Fongicide', 'Insecticide', 'Régulateur']:
            if function.lower() in function_category.lower():
                primary_function = function
                break

        if not primary_function:
            primary_function = "Autre"

        # Get compatibility guidance based on product type
        compatibility_types = {}
        if primary_function in cls.compatibility_matrix:
            for other_type, compatibility in cls.compatibility_matrix[primary_function].items():
                compatibility_types[other_type] = compatibility

        # Find formulation compatibility guidance
        formulation_guidance = None
        if product.formulation_type:
            formulation_codes = ["SC", "EC", "WP", "WG", "SL", "OD", "CS"]
            for code in formulation_codes:
                if code in product.formulation_type:
                    if code == "SC":
                        formulation_guidance = "SC (Suspension Concentrée): Ajouter en premier dans la cuve."
                    elif code == "EC":
                        formulation_guidance = "EC (Concentré Émulsionnable): Ajouter après les SC et les WG/WP."
                    elif code == "WP":
                        formulation_guidance = "WP (Poudre Mouillable): Pré-mélanger avant d'ajouter au réservoir, après les SC."
                    elif code == "WG":
                        formulation_guidance = "WG (Granulés Dispersibles): Ajouter après les SC et avant les EC."
                    elif code == "SL":
                        formulation_guidance = "SL (Concentré Soluble): Ajouter après les WG/WP et avant les EC."
                    elif code == "OD":
                        formulation_guidance = "OD (Dispersion Huileuse): Vérifier compatibilité. Risque de séparation avec certains produits."
                    elif code == "CS":
                        formulation_guidance = "CS (Suspension de Capsules): Ajouter après les WG et avant les EC."
                    break

        # Generate insights
        insights = {
            "formulation_guidance": formulation_guidance,
            "compatibility_types": compatibility_types,
            "general_precautions": [
                "Toujours effectuer un test de compatibilité physique avant de procéder au mélange en cuve à grande échelle.",
                "Respecter l'ordre d'incorporation des produits dans la cuve selon leur formulation.",
                "Maintenir une agitation constante pendant la préparation et l'application."
            ]
        }

        return insights

    @classmethod
    async def _generate_application_guidance(cls,
                                             session: AsyncSession,
                                             product: Product,
                                             uses: Optional[List[Tuple]] = None) -> Dict[str, Any]:
        """Generate application guidance based on uses"""
        # Get uses if not provided
        if uses is None:
            uses_query = select(
                ProductUse,
                Crop,
                Target
            ).join(
                Crop, Crop.id == ProductUse.crop_id
            ).outerjoin(
                Target, Target.id == ProductUse.target_id
            ).filter(
                ProductUse.product_id == product.id,
                ProductUse.is_currently_authorized == True
            )

            result = await session.execute(uses_query)
            uses = result.all()

        if not uses:
            return {
                "note": "Pas d'usages autorisés trouvés pour ce produit"
            }

        # Extract application parts
        application_parts = set()
        doses = []
        max_applications = []
        harvest_intervals = []
        buffer_zones = []

        for use, _, _ in uses:
            if use.application_part:
                application_parts.add(use.application_part)

            if use.min_dose is not None:
                doses.append(use.min_dose)

            if use.max_applications is not None:
                max_applications.append(use.max_applications)

            if use.harvest_interval_days is not None:
                harvest_intervals.append(use.harvest_interval_days)

            if use.max_buffer_zone is not None:
                buffer_zones.append(use.max_buffer_zone)

        # Generate application guidance
        application_guidance = {
            "application_parts": list(application_parts),
            "doses": {
                "min": min(doses) if doses else None,
                "max": max(doses) if doses else None,
                "average": sum(doses) / len(doses) if doses else None
            },
            "applications": {
                "max": max(max_applications) if max_applications else None
            },
            "harvest_intervals": {
                "min": min(harvest_intervals) if harvest_intervals else None,
                "max": max(harvest_intervals) if harvest_intervals else None
            },
            "buffer_zones": {
                "max": max(buffer_zones) if buffer_zones else None
            }
        }

        # Generate application guidance text
        guidance_text = f"Pour appliquer {product.product_name}, "

        if application_parts:
            if len(application_parts) == 1:
                guidance_text += f"traiter {list(application_parts)[0].lower()}. "
            else:
                parts_list = [p.lower() for p in application_parts]
                guidance_text += f"traiter {', '.join(parts_list[:-1])} ou {parts_list[-1]}. "

        if doses and application_guidance["doses"]["average"]:
            guidance_text += f"Dose moyenne: {application_guidance['doses']['average']:.2f} "

            # Try to find a common dose unit
            dose_units = {}
            for use, _, _ in uses:
                if use.dose_unit:
                    dose_units[use.dose_unit] = dose_units.get(use.dose_unit, 0) + 1

            most_common_unit = max(dose_units.items(), key=lambda x: x[1])[0] if dose_units else "???"
            guidance_text += f"{most_common_unit}. "

        if max_applications and application_guidance["applications"]["max"]:
            guidance_text += f"Maximum {application_guidance['applications']['max']} applications par saison. "

        if harvest_intervals and application_guidance["harvest_intervals"]["max"]:
            guidance_text += f"Respecter un DAR de {application_guidance['harvest_intervals']['max']} jours avant récolte. "

        if buffer_zones and application_guidance["buffer_zones"]["max"]:
            guidance_text += f"Respecter une ZNT de {application_guidance['buffer_zones']['max']} mètres."

        application_guidance["guidance_text"] = guidance_text

        return application_guidance

    @classmethod
    async def _find_alternative_products_for_crop(cls,
                                                  session: AsyncSession,
                                                  product: Product,
                                                  crops: List[Crop],
                                                  targets: Optional[List[Target]] = None) -> List[Dict[str, Any]]:
        """Find alternative products for specific crops and targets"""
        crop_ids = [c.id for c in crops]

        # Find products that are used on these crops
        alternatives_query = select(
            Product,
            func.count(distinct(ProductUse.id)).label('use_count')
        ).join(
            ProductUse, ProductUse.product_id == Product.id
        ).filter(
            ProductUse.crop_id.in_(crop_ids),
            ProductUse.is_currently_authorized == True,
            Product.is_currently_authorized == True,
            Product.id != product.id
        )

        # If targets specified, filter by them
        if targets:
            target_ids = [t.id for t in targets]
            alternatives_query = alternatives_query.filter(
                ProductUse.target_id.in_(target_ids)
            )

        # Apply function category filter to match original product
        if product.function_category:
            alternatives_query = alternatives_query.filter(
                func.lower(Product.function_category).like(f"%{product.function_category.lower()}%")
            )

        # Group by product and get use count
        alternatives_query = alternatives_query.group_by(
            Product.id
        ).order_by(
            func.count(distinct(ProductUse.id)).desc()
        ).limit(5)

        # Execute query to get alternatives
        result = await session.execute(alternatives_query)
        alternatives = result.all()

        # Format results
        results = []

        for alt_product, use_count in alternatives:
            # Get active substances
            substances_query = select(
                ActiveSubstance.name,
                ProductSubstance.concentration,
                ProductSubstance.concentration_unit
            ).join(
                ProductSubstance, ProductSubstance.substance_id == ActiveSubstance.id
            ).filter(
                ProductSubstance.product_id == alt_product.id
            )

            substances_result = await session.execute(substances_query)
            substances = substances_result.all()

            substance_list = [
                {
                    "name": s.name,
                    "concentration": s.concentration,
                    "unit": s.concentration_unit
                }
                for s in substances
            ]

            results.append({
                "product_name": alt_product.product_name,
                "registration_number": alt_product.registration_number,
                "function_category": alt_product.function_category,
                "active_substances": substance_list,
                "use_count": use_count
            })

        return results

    @classmethod
    def _generate_safety_assessment(cls, product: Product, hazards: List, buffer_zones: Dict, equipment: List) -> str:
        """Generate comprehensive safety assessment summary"""
        assessment = f"ÉVALUATION SÉCURITAIRE - {product.product_name}\n\n"

        # Authorization status
        if product.is_currently_authorized:
            assessment += "✅ Produit actuellement autorisé pour usage commercial.\n"
        else:
            assessment += "⚠️ Produit retiré du marché - utilisation interdite.\n"
            return assessment

        # Hazard level assessment
        critical_hazards = len([h for h in hazards if cls._categorize_severity(h.hazard_severity) == "high"])
        if critical_hazards > 0:
            assessment += f"🔴 NIVEAU DE DANGER ÉLEVÉ ({critical_hazards} mentions critiques)\n"
        else:
            assessment += "🟡 Niveau de danger modéré - précautions standard requises\n"

        # Equipment requirements
        high_priority_equipment = len([e for e in equipment if e.get("priority") == "high"])
        if high_priority_equipment > 0:
            assessment += f"🛡️ {high_priority_equipment} équipements de protection obligatoires\n"

        # Buffer zone requirements
        max_buffer = buffer_zones.get("maximum", 0)
        if max_buffer > 0:
            assessment += f"📏 Zone de sécurité requise: {max_buffer} mètres minimum\n"

        assessment += "\nConsulter les conditions d'usage détaillées avant application."
        return assessment

    @classmethod
    def _validate_application_parameters(cls, details: Dict[str, Any], authorized_uses: List[Dict]) -> List[Dict]:
        """Validate specific application parameters against authorized ranges"""
        validations = []

        # Validate dose if provided
        if "dose" in details and details["dose"] is not None:
            try:
                proposed_dose = float(details["dose"])
                for use in authorized_uses:
                    dose_info = use["application_parameters"]["dosage"]
                    min_dose = dose_info["min_dose"]
                    max_dose = dose_info["max_dose"]

                    if min_dose is not None and max_dose is not None:
                        if proposed_dose < min_dose:
                            validations.append({
                                "parameter": "Dosage",
                                "is_valid": False,
                                "proposed_value": proposed_dose,
                                "authorized_range": f"{min_dose}-{max_dose} {dose_info['unit']}",
                                "message": f"Dose insuffisante: {proposed_dose} < {min_dose} {dose_info['unit']}"
                            })
                        elif proposed_dose > max_dose:
                            validations.append({
                                "parameter": "Dosage",
                                "is_valid": False,
                                "proposed_value": proposed_dose,
                                "authorized_range": f"{min_dose}-{max_dose} {dose_info['unit']}",
                                "message": f"Surdosage détecté: {proposed_dose} > {max_dose} {dose_info['unit']}"
                            })
                        else:
                            validations.append({
                                "parameter": "Dosage",
                                "is_valid": True,
                                "proposed_value": proposed_dose,
                                "authorized_range": f"{min_dose}-{max_dose} {dose_info['unit']}",
                                "message": "Dosage conforme aux autorisations"
                            })
            except ValueError:
                validations.append({
                    "parameter": "Dosage",
                    "is_valid": False,
                    "message": "Valeur de dose invalide"
                })

        # Validate growth stage if provided
        if "growth_stage" in details and details["growth_stage"] is not None:
            try:
                proposed_stage = int(details["growth_stage"])
                for use in authorized_uses:
                    stage_info = use["application_parameters"]["growth_stage_requirements"]
                    min_stage = stage_info["min_bbch_stage"]
                    max_stage = stage_info["max_bbch_stage"]

                    if min_stage is not None and max_stage is not None:
                        if proposed_stage < min_stage:
                            validations.append({
                                "parameter": "Stade BBCH",
                                "is_valid": False,
                                "proposed_value": proposed_stage,
                                "authorized_range": f"BBCH {min_stage}-{max_stage}",
                                "message": f"Stade trop précoce: BBCH {proposed_stage} < BBCH {min_stage}"
                            })
                        elif proposed_stage > max_stage:
                            validations.append({
                                "parameter": "Stade BBCH",
                                "is_valid": False,
                                "proposed_value": proposed_stage,
                                "authorized_range": f"BBCH {min_stage}-{max_stage}",
                                "message": f"Stade trop tardif: BBCH {proposed_stage} > BBCH {max_stage}"
                            })
                        else:
                            validations.append({
                                "parameter": "Stade BBCH",
                                "is_valid": True,
                                "proposed_value": proposed_stage,
                                "authorized_range": f"BBCH {min_stage}-{max_stage}",
                                "message": "Stade d'application conforme"
                            })
            except ValueError:
                validations.append({
                    "parameter": "Stade BBCH",
                    "is_valid": False,
                    "message": "Valeur de stade BBCH invalide"
                })

        return validations

    @classmethod
    def _generate_usage_compliance_assessment(cls, product: Product, authorized_uses: List, validations: List) -> str:
        """Generate comprehensive compliance assessment"""
        assessment = f"ÉVALUATION DE CONFORMITÉ - {product.product_name}\n\n"

        if not product.is_currently_authorized:
            assessment += "❌ PRODUIT NON AUTORISÉ - Usage interdit\n"
            return assessment

        if not authorized_uses:
            assessment += "❌ USAGE NON AUTORISÉ pour cette combinaison culture/cible\n"
            return assessment

        assessment += f"✅ {len(authorized_uses)} usage(s) autorisé(s) identifié(s)\n"

        # Parameter validation summary
        if validations:
            valid_params = len([v for v in validations if v["is_valid"]])
            total_params = len(validations)
            if valid_params == total_params:
                assessment += f"✅ Tous les paramètres d'application sont conformes ({valid_params}/{total_params})\n"
            else:
                assessment += f"⚠️ {total_params - valid_params} paramètre(s) non conforme(s) sur {total_params}\n"

        # Application guidance
        if authorized_uses:
            use = authorized_uses[0]  # Use first authorized use for guidance
            params = use["application_parameters"]

            # Dose guidance
            if params["dosage"]["min_dose"] and params["dosage"]["max_dose"]:
                assessment += f"📊 Dose recommandée: {params['dosage']['formatted_range']}\n"

            # Timing guidance
            if params["growth_stage_requirements"]["min_bbch_stage"]:
                assessment += f"⏰ {params['growth_stage_requirements']['formatted_stage_range']}\n"

            # Safety zones
            max_buffer = params["safety_zones"]["maximum_buffer_meters"]
            if max_buffer > 0:
                assessment += f"🛡️ Zone de sécurité: {max_buffer} mètres minimum\n"

        assessment += "\nVérifier les conditions d'usage spécifiques avant application."
        return assessment
