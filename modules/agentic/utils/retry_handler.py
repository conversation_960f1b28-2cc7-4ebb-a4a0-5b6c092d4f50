import asyncio
from typing import List

from pydantic_ai.messages import ModelMessage, ModelRequest, ToolCallPart

from utils.logger import logger


class RetryState:
    """Manages retry state and successful messages for continuation"""

    def __init__(self, max_retries: int = 1):
        self.max_retries = max_retries
        self.retry_count = 0
        self.successful_messages: List[ModelMessage] = []
        self.successful_events: List = []
        self.should_retry = False
        self.error_occurred = False

    def can_retry(self) -> bool:
        """Check if retry is possible"""
        return self.retry_count < self.max_retries and self.should_retry

    def prepare_retry(self, successful_messages: List[ModelMessage], successful_events: List) -> bool:
        """Prepare for retry by storing successful state"""
        if not self.can_retry():
            return False

        self.retry_count += 1
        self.successful_messages = successful_messages or []
        self.successful_events = successful_events or []
        self.should_retry = False
        self.error_occurred = False

        logger.info(
            f"Preparing retry {self.retry_count}/{self.max_retries} with {len(self.successful_messages)} successful messages")
        return True

    def mark_error(self):
        """Mark that an error occurred and retry is needed"""
        self.error_occurred = True
        self.should_retry = True

    def is_final_attempt(self) -> bool:
        """Check if this is the final retry attempt"""
        return self.retry_count >= self.max_retries


class RetryHandler:
    """Handles retry logic for GeneralAgent responses"""

    def __init__(self, max_retries: int = 1):
        self.state = RetryState(max_retries)

    async def execute_with_retry(self, agent, request, processor, attachments: List = None):
        """Execute agent with automatic retry on JSON format errors"""

        while True:
            try:
                # Reset processor state for new attempt (clear contaminated buffers)
                processor.error_occurred = False
                processor.completed = False
                processor.text_buffer = ""  # Clear leftover chunks from failed attempt
                processor.full_response = ""  # Clear accumulated response
                processor.response = ""  # Clear response alias

                # Clear event queue of any remaining events from previous attempt
                while not processor.response_queue.empty():
                    try:
                        processor.response_queue.get_nowait()
                    except asyncio.QueueEmpty:
                        break

                # Prepare message history for retry
                message_history = self.state.successful_messages if self.state.retry_count > 0 else []

                # Modify request for retry attempts
                if self.state.retry_count > 0:
                    # Add continuation prompt for retry
                    request.query = 'Complete the response with strictly following given JSON line templates.'

                    # Create partial response message if we have successful events
                    partial_message = self._create_partial_response_message(processor)
                    if partial_message:
                        message_history.append(partial_message)
                        logger.info("Added partial response to history for continuation")

                # Execute agent
                background_task = asyncio.create_task(
                    agent.run_stream(request, processor, message_history, attachments)
                )

                # Yield events from processor
                async for event_json in self._process_events(processor, background_task):
                    yield event_json

                # Check if we completed successfully or need retry
                if processor.completed:
                    logger.info("Agent execution completed successfully")
                    break
                elif processor.error_occurred:
                    # Cancel current task before retry
                    if background_task and not background_task.done():
                        background_task.cancel()
                        try:
                            await background_task
                        except (asyncio.CancelledError, Exception):
                            pass

                    # Mark that an error occurred and retry is needed
                    self.state.mark_error()

                    if self.state.can_retry():
                        # Prepare for retry
                        successful_messages = self._extract_successful_messages(agent, processor)
                        if self.state.prepare_retry(successful_messages, processor.successful_events):
                            logger.warning(
                                f"Retrying due to format error (attempt {self.state.retry_count}/{self.state.max_retries})")
                            continue  # Retry the loop
                        else:
                            logger.error("Failed to prepare retry")
                            break
                    else:
                        # No more retries available - now emit error event
                        logger.error("Maximum retries reached, unable to get valid JSON response")
                        try:
                            # Import here to avoid circular imports
                            from const import EConnectionState
                            error_event = processor.create_connection_event(
                                EConnectionState.error,
                                "Unable to generate valid response format after retries"
                            )
                            yield error_event.model_dump_json(exclude_none=True)
                        except Exception as emit_error:
                            logger.error(f"Failed to emit final error event: {str(emit_error)}")
                        break
                else:
                    # Agent execution finished without completion or error (shouldn't happen)
                    logger.warning("Agent execution finished without completion or error state")
                    break

            except Exception as e:
                logger.error(f"Error in retry handler: {str(e)}", exc_info=True)
                break

    async def _process_events(self, processor, background_task):
        """Process events from the response queue"""
        try:
            while True:
                try:
                    # Wait for events with timeout
                    event = await asyncio.wait_for(processor.response_queue.get(), timeout=230.0)
                    yield event.model_dump_json(exclude_none=True)

                    # Check for completion or error
                    if processor.completed or processor.error_occurred:
                        # Process any remaining events
                        while not processor.response_queue.empty():
                            try:
                                final_event = processor.response_queue.get_nowait()
                                yield final_event.model_dump_json(exclude_none=True)
                            except asyncio.QueueEmpty:
                                break
                        break

                except asyncio.TimeoutError:
                    logger.warning("Timeout while waiting for response")
                    processor.error_occurred = True
                    break

        except Exception as e:
            logger.error(f"Error processing events: {str(e)}", exc_info=True)
            processor.error_occurred = True

    def _extract_successful_messages(self, agent, processor) -> List[ModelMessage]:
        """Extract successful messages from processor for retry"""
        try:
            # Use the processor's successful_messages if available
            if hasattr(processor, 'successful_messages') and processor.successful_messages:
                # Validate and fix the structure before returning
                validated_messages = validate_and_fix_message_structure(processor.successful_messages)
                return validated_messages

            # Return empty list if no successful messages found
            return []
        except Exception as e:
            logger.warning(f"Could not extract successful messages: {str(e)}")
            return []

    def _create_partial_response_message(self, processor):
        """Create partial response message for retry continuation"""
        return processor.create_partial_response_message() if hasattr(processor,
                                                                      'create_partial_response_message') else None


def validate_and_fix_message_structure(successful_messages: List) -> List:
    """
    Validate and fix successful_messages structure ensuring ToolCallPart/ToolReturnPart pairs.
    If the latest object is ModelRequest with ToolCallPart, remove the entire ModelRequest.
    
    Args:
        successful_messages: List of ModelRequest/ModelResponse objects from processor
        
    Returns:
        List: Fixed messages with proper ToolCallPart/ToolReturnPart pairing
    """
    if not successful_messages:
        return successful_messages

    # Check if the latest message is a ModelRequest with ToolCallPart
    last_message = successful_messages[-1]

    if isinstance(last_message, ModelRequest):
        # Check if this ModelRequest contains ToolCallPart
        has_tool_call = any(isinstance(part, ToolCallPart) for part in last_message.parts)

        if has_tool_call:
            # Remove the entire last ModelRequest since it contains ToolCallPart
            logger.warning("Removing last ModelRequest containing ToolCallPart")
            fixed_messages = successful_messages[:-1]  # Remove last message

            logger.info(f"Fixed successful_messages: {len(successful_messages)} -> {len(fixed_messages)} messages")
            return fixed_messages

    # No orphaned ToolCallPart at the end, return original messages
    logger.debug(f"Validated {len(successful_messages)} messages - structure is valid")
    return successful_messages
