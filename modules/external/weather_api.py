from datetime import datetime, timed<PERSON>ta
from typing import Optional, Dict, Any, TypedDict, cast
import logging
import asyncio
from contextlib import asynccontextmanager

import aiohttp

from config import CONFIG
from schemas.weather import WeatherAPIException

# Disable noisy aiohttp default logs
logging.getLogger('aiohttp').setLevel(logging.WARNING)
logging.getLogger('aiohttp.client').setLevel(logging.WARNING)


class CurrentWeather(TypedDict):
    location: Dict[str, Any]
    current: Dict[str, Any]


class ForecastWeather(TypedDict):
    location: Dict[str, Any]
    current: Dict[str, Any]
    forecast: Dict[str, Any]
    alerts: Optional[Dict[str, Any]]


class HistoricalWeather(TypedDict):
    location: Dict[str, Any]
    forecast: Dict[str, Any]


class WeatherAPI:
    """General-purpose client for WeatherAPI.com"""

    def __init__(
            self,
            api_key: Optional[str] = None,
            timeout: float = 10.0,
            max_connections: int = 10,
            request_retries: int = 3
    ):
        """
        Initialize the Weather API client.

        Args:
            api_key: WeatherAPI.com API key (defaults to WEATHER_API_KEY environment variable)
            timeout: Request timeout in seconds (default 10s)
            max_connections: Maximum number of connections in the pool
            request_retries: Number of times to retry failed requests
        """
        self.api_key = api_key or CONFIG.WEATHER_API_KEY
        if not self.api_key:
            raise WeatherAPIException("Weather API key not provided and not found in environment variables")

        self.base_url = "http://api.weatherapi.com/v1"
        self.timeout = aiohttp.ClientTimeout(total=timeout)
        self.max_connections = max_connections
        self.retries = request_retries
        self.session = None

    async def __aenter__(self):
        """Support for async context manager pattern"""
        if self.session is None or self.session.closed:
            connector = aiohttp.TCPConnector(limit=self.max_connections, ttl_dns_cache=300)
            self.session = aiohttp.ClientSession(connector=connector, timeout=self.timeout)
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Clean up resources when exiting context manager"""
        await self.close()

    async def close(self):
        """Close the aiohttp session"""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None

    @asynccontextmanager
    async def _get_session(self):
        """Get or create an aiohttp session using context manager"""
        session_created = False
        if self.session is None or self.session.closed:
            connector = aiohttp.TCPConnector(limit=self.max_connections, ttl_dns_cache=300)
            self.session = aiohttp.ClientSession(connector=connector, timeout=self.timeout)
            session_created = True

        try:
            yield self.session
        finally:
            if session_created:
                await self.session.close()
                self.session = None

    async def make_request(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Make a request to the Weather API with retry logic.

        Args:
            endpoint: API endpoint path
            params: Query parameters

        Returns:
            JSON response data

        Raises:
            WeatherAPIException: If the request fails after retries
        """
        params["key"] = self.api_key
        url = f"{self.base_url}/{endpoint}"

        async with self._get_session() as session:
            for attempt in range(self.retries):
                try:
                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            return await response.json()

                        # Only read error text for small responses to avoid memory issues
                        error_text = await response.text(
                            errors='replace') if response.content_length is None or response.content_length < 10000 else f"[Error response too large: {response.content_length} bytes]"

                        # Determine if we should retry based on status code
                        if response.status in (429, 500, 502, 503, 504) and attempt < self.retries - 1:
                            retry_after = response.headers.get('Retry-After')
                            wait_time = int(retry_after) if retry_after and retry_after.isdigit() else min(2 ** attempt,
                                                                                                           30)
                            await asyncio.sleep(wait_time)
                            continue

                        raise WeatherAPIException(
                            f"API request to {endpoint} failed with status {response.status}: {error_text}"
                        )

                except (aiohttp.ClientError, asyncio.TimeoutError) as e:
                    if attempt < self.retries - 1:
                        # Exponential backoff
                        await asyncio.sleep(min(2 ** attempt, 30))
                        continue
                    raise WeatherAPIException(f"Failed to connect to Weather API: {str(e)}")

        # This should never be reached due to the exception handling above
        raise WeatherAPIException("Unexpected error in make_request")

    async def get_current_weather(self, location: str, aqi: bool = False) -> CurrentWeather:
        """
        Get current weather for a location.

        Args:
            location: City name, ZIP code, or GPS coordinates (lat,lon)
            aqi: Include air quality data

        Returns:
            Current weather data
        """
        result = await self.make_request("current.json", {
            "q": location,
            "aqi": "yes" if aqi else "no"
        })
        return cast(CurrentWeather, result)

    async def get_forecast(
            self,
            location: str,
            days: int = 3,
            aqi: bool = False,
            alerts: bool = False
    ) -> ForecastWeather:
        """
        Get weather forecast for a location.

        Args:
            location: City name, ZIP code, or GPS coordinates (lat,lon)
            days: Number of forecast days (1-10)
            aqi: Include air quality data
            alerts: Include weather alerts

        Returns:
            Forecast weather data

        Raises:
            WeatherAPIException: If days is not between 1 and 10
        """
        if not 1 <= days <= 10:
            raise WeatherAPIException("Days parameter must be between 1 and 10")

        result = await self.make_request("forecast.json", {
            "q": location,
            "days": days,
            "aqi": "yes" if aqi else "no",
            "alerts": "yes" if alerts else "no"
        })
        return cast(ForecastWeather, result)

    async def get_historical_weather(self, location: str, date: str) -> HistoricalWeather:
        """
        Get historical weather for a specific date.

        Args:
            location: City name, ZIP code, or GPS coordinates (lat,lon)
            date: Date in YYYY-MM-DD format (up to 1 year in the past)

        Returns:
            Historical weather data

        Raises:
            WeatherAPIException: If date format is invalid or date is more than 1 year ago
        """
        # Validate date format
        try:
            date_obj = datetime.strptime(date, "%Y-%m-%d")
        except ValueError:
            raise WeatherAPIException("Invalid date format. Use YYYY-MM-DD")

        # Check if date is not more than 1 year ago
        if date_obj < (datetime.now() - timedelta(days=365)):
            raise WeatherAPIException("Historical data only available for the past year")

        result = await self.make_request("history.json", {
            "q": location,
            "dt": date
        })
        return cast(HistoricalWeather, result)