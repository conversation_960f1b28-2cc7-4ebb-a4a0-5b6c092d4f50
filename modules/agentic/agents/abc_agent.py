import json
from abc import abstractmethod, <PERSON>
from typing import List, Type, Union

from pydantic_ai import Agent
from pydantic_ai.messages import ModelRequest, ModelResponse, TextPart, UserPromptPart
from pydantic_ai.models.anthropic import AnthropicModel
from pydantic_ai.models.mistral import MistralModel
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.settings import ModelSettings

from const import EChatRole
from schemas import BaseModel
from schemas.assistant import SChatMessage
from utils.logger import logger

model_base_MS = MistralModel('mistral-large-latest')
model_base_ML = MistralModel('mistral-large-latest')
model_base_MN = MistralModel('open-mistral-nemo')
model_base_GPT4 = OpenAIModel('gpt-4o')
model_base_GPT4M = OpenAIModel('gpt-4o-mini')
model_base_C35 = AnthropicModel('claude-3-7-sonnet-latest')


class ABCAgent(ABC):
    """
        Abstract base class for all agent_type implementations.
        Provides common functionality and defines the interface that all agents must implement.
    """

    def __init__(self):
        """Initialize the agent_type with its configuration"""

        agent_base = Agent(
            model=self._get_model_base(self._get_model()),
            instructions=self._get_system_prompt(),
            tools=self._get_tools(),
            model_settings=self._get_model_settings(),
            output_type=self._get_response_model(),
            instrument=True
        )
        self.agent = self._configure_agent(agent_base)

    @staticmethod
    def _get_model_base(model: str):
        match model:
            case 'mistral:mistral-small-latest':
                return model_base_MS
            case 'mistral:mistral-large-latest':
                return model_base_ML
            case 'mistral:open-mistral-nemo':
                return model_base_MN
            case 'openai:gpt-4o':
                return model_base_GPT4
            case 'openai:gpt-4o-mini':
                return model_base_GPT4M
            case 'anthropic:claude-3-5-sonnet-latest':
                return model_base_C35
            case _:
                return model_base_MS

    @staticmethod
    def _get_tools():
        return ()

    @abstractmethod
    def run(self, **kwargs):
        pass

    #
    # async def _run(self, **kwargs):
    #     return self.agent.run(**kwargs,
    #             model=self._get_model(),
    #             # system_prompt=self._get_system_prompt(),
    #             model_settings=self._get_model_settings(),
    #             output_type=self._get_response_model()
    #     )
    #
    #
    # async def _run_stream(self, **kwargs):
    #     return self.agent.run_stream(**kwargs,
    #             model=self._get_model(),
    #             # system_prompt=self._get_system_prompt(),
    #             model_settings=self._get_model_settings(),
    #             output_type=self._get_response_model()
    #     )

    @abstractmethod
    async def run_stream(self, **kwargs):
        pass

    # add methods to get and set history by formatting message chain
    @staticmethod
    def format_message_chain(messages: List[SChatMessage]) -> List:
        """
        Convert app message format to model API format.

        Args:
            messages: List of chat messages in the application format

        Returns:
            List of messages formatted for the model API
        """
        result = []
        for msg in messages:
            content = msg.content
            # Simple string manipulation to remove wrapping array if present
            if isinstance(content, str):
                content = content.strip()
                if content.startswith('[{') and content.endswith('}]'):
                    logger.debug("Removing wrapping array from content")
                    # Remove the outer brackets from "[{...}]" to get "{...}"
                    content = content[1:-1].strip()

            if msg.role == EChatRole.user:
                result.append(ModelRequest(parts=[UserPromptPart(content=content)]))
            elif msg.role == EChatRole.assistant:
                result.append(ModelResponse(parts=[TextPart(content=content)]))
        return result

    @staticmethod
    def get_history_context(messages):
        """
        Format conversation history from messages into a string representation.

        Args:
            messages: List of message objects with role and content attributes

        Returns:
            str: Formatted conversation history
        """
        if not messages:
            return ""

        result = []

        for message in messages:
            if hasattr(message, 'role'):
                if message.role == EChatRole.user:
                    result.append(f"User: {message.content}")
                elif message.role == EChatRole.assistant:
                    result.append(f"Assistant: {message.content}")
        result_str = "\n".join(result)
        return f'<conversation_history>\n{result_str}</conversation_history>'

    @abstractmethod
    def _get_system_prompt(self) -> str:
        """
        Get the system prompt for the agent_type.

        Returns:
            The formatted system prompt string
        """
        pass

    @abstractmethod
    def _get_model(self) -> str:
        """
        Map internal model style to actual API model names.
        Returns:
            The model identifier string
        """
        pass

    @abstractmethod
    def _get_model_settings(self) -> ModelSettings:
        """
        Get the model settings for the agent_type.

        Returns:
            ModelSettings configuration
        """
        pass

    @abstractmethod
    def _configure_agent(self, agent: Agent) -> Agent:
        """
        Configure the agent_type with additional middleware or context handlers.

        Args:
            agent: The agent_type to configure
        """
        pass

    def _get_response_model(self) -> Union[Type[BaseModel], Type[str]]:
        """
        Get the response model for the agent_type.

        Returns:
            The Pydantic model class for the agent_type's responses
        """
        return str
