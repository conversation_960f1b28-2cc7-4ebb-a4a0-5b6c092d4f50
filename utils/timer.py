import time
from contextlib import contextmanager

from typing import Dict, Optional
from utils.logger import logger


class Timer:
    """
    A versatile timer class for measuring code execution time.

    Supports multiple usage patterns:
    1. As a context manager
    2. As a traditional start/stop method
    3. As a decorator
    """

    def __init__(self, name='default'):
        """
        Initialize the timer.

        :param name: Optional name to identify the timer
        """
        self.name = name
        self.start_time = None
        self.end_time = None
        self.elapsed_time = None
        self.checkpoints: Dict[str, float] = {}
        self.last_checkpoint_time: Optional[float] = None

    def start(self):
        """Start the timer."""
        self.start_time = time.time()
        return self

    def stop(self):
        """
        Stop the timer and calculate elapsed time.

        :return: Elapsed time in seconds
        """
        if self.start_time is None:
            raise RuntimeError("Timer not started. Call start() first.")

        self.end_time = time.time()
        self.elapsed_time = self.end_time - self.start_time

        logger.debug(f"Timer '{self.name}': {self.elapsed_time:.4f} seconds")

        return self.elapsed_time

    @contextmanager
    def measure(self):
        """
        Context manager for measuring time.

        Usage:
        with Timer('my_operation').measure():
            # code to measure
        """
        self.start()
        try:
            yield self
        finally:
            self.stop()

    @staticmethod
    def decorator(name=None):
        """
        Decorator to measure function execution time.

        Usage:
        @Timer.decorator('function_name')
        def my_function():
            # function code
        """

        def decorator_timer(func):
            def wrapper(*args, **kwargs):
                timer = Timer(name or func.__name__)
                timer.start()
                try:
                    return func(*args, **kwargs)
                finally:
                    timer.stop()

            return wrapper

        return decorator_timer

    def checkpoint(self, name: str = '') -> float:
        current_time = time.time()

        if self.start_time is None:
            self.start()

        if self.last_checkpoint_time is None:
            self.last_checkpoint_time = self.start_time

        elapsed = current_time - self.last_checkpoint_time
        self.checkpoints[name] = elapsed
        self.last_checkpoint_time = current_time

        logger.debug(f"Timer '{self.name}' checkpoint '{name}': {elapsed:.4f} seconds")
        return elapsed

    def get_checkpoint_times(self) -> Dict[str, float]:
        return self.checkpoints

    def reset_checkpoints(self):
        self.checkpoints.clear()
        self.last_checkpoint_time = None


if __name__ == "__main__":
    # Method 1: Context Manager
    print("Context Manager Usage:")
    with Timer('context_demo').measure():
        # Simulate some work
        time.sleep(1)

    # Method 2: Traditional Start/Stop
    print("\nTraditional Start/Stop:")
    timer = Timer('traditional_demo')
    timer.start()
    time.sleep(0.5)
    timer.stop()

    # Method 3: Decorator
    print("\nDecorator Usage:")


    @Timer.decorator('decorated_function')
    def slow_function():
        time.sleep(0.75)


    slow_function()

    print("Checkpoint Usage:")
    timer = Timer('checkpoint_demo')

    time.sleep(0.5)
    timer.checkpoint("first_operation")

    time.sleep(0.75)
    timer.checkpoint("second_operation")

    time.sleep(0.25)
    timer.checkpoint("final_operation")

    print("\nCheckpoint intervals:")
    for point, interval in timer.get_checkpoint_times().items():
        print(f"{point}: {interval:.4f} seconds")
