"""
JSON processor for structured data files.
Converts JSON to readable text while preserving structure.
"""
import json
from typing import List, Dict, Any, Optional
from uuid import UUID

from modules.knowledge_base.processors.base_processor import BaseDocumentProcessor, ProcessingResult
from utils.logger import logger


class JSONProcessor(BaseDocumentProcessor):
    """Processor for JSON files containing structured data."""
    
    def __init__(self):
        super().__init__()
        self.supported_extensions = {'.json'}
    
    async def process(self, file_content: bytes, document_id: UUID) -> ProcessingResult:
        """
        Process JSON file and convert to readable text format.
        
        Args:
            file_content: JSON file content as bytes
            document_id: Document ID for reference
            
        Returns:
            ProcessingResult with structured text representation
        """
        try:
            # Decode JSON content
            try:
                text_content = file_content.decode('utf-8')
            except UnicodeDecodeError:
                text_content = file_content.decode('latin-1', errors='replace')
            
            # Parse JSON
            json_data = json.loads(text_content)
            
            # Convert to readable text format
            readable_text = self._convert_json_to_text(json_data)
            
            if not readable_text:
                return ProcessingResult(
                    success=False,
                    error="JSON file contains no readable content"
                )
            
            # Extract metadata
            title = self._extract_json_title(json_data)
            agricultural_metadata = await self._extract_agricultural_metadata(readable_text)
            
            # Estimate pages based on content length
            estimated_pages = max(1, len(readable_text) // 2000)
            
            return ProcessingResult(
                success=True,
                content=readable_text,
                title=title,
                language='en',  # Assume English for JSON data
                total_pages=estimated_pages,
                crop_categories=agricultural_metadata.get('crop_categories', []),
                topics=agricultural_metadata.get('topics', []),
                geographic_scope=agricultural_metadata.get('geographic_scope', []),
                seasonal_relevance=agricultural_metadata.get('seasonal_relevance', []),
                metadata={
                    'processor': 'JSONProcessor',
                    'json_structure': self._analyze_json_structure(json_data),
                    'total_keys': self._count_keys(json_data)
                }
            )
            
        except json.JSONDecodeError as e:
            return ProcessingResult(
                success=False,
                error=f"Invalid JSON format: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Error processing JSON document {document_id}: {e}")
            return ProcessingResult(
                success=False,
                error=f"JSON processing error: {str(e)}"
            )
    
    def _convert_json_to_text(self, json_data: Any, prefix: str = "") -> str:
        """Convert JSON data to readable text format."""
        text_parts = []
        
        if isinstance(json_data, dict):
            for key, value in json_data.items():
                if isinstance(value, (dict, list)):
                    text_parts.append(f"{prefix}{key}:")
                    text_parts.append(self._convert_json_to_text(value, prefix + "  "))
                else:
                    text_parts.append(f"{prefix}{key}: {value}")
        
        elif isinstance(json_data, list):
            for i, item in enumerate(json_data[:50]):  # Limit to first 50 items
                if isinstance(item, (dict, list)):
                    text_parts.append(f"{prefix}Item {i+1}:")
                    text_parts.append(self._convert_json_to_text(item, prefix + "  "))
                else:
                    text_parts.append(f"{prefix}Item {i+1}: {item}")
            
            if len(json_data) > 50:
                text_parts.append(f"{prefix}... and {len(json_data) - 50} more items")
        
        else:
            text_parts.append(f"{prefix}{json_data}")
        
        return '\n'.join(text_parts)
    
    def _extract_json_title(self, json_data: Any) -> Optional[str]:
        """Extract a meaningful title from JSON data."""
        # Look for common title fields
        title_fields = ['title', 'name', 'description', 'label', 'subject']
        
        if isinstance(json_data, dict):
            for field in title_fields:
                if field in json_data and isinstance(json_data[field], str):
                    title = json_data[field].strip()
                    if title and len(title) < 200:
                        return title
            
            # Try to create descriptive title from keys
            keys = list(json_data.keys())[:5]
            agricultural_keys = [k for k in keys if any(term in k.lower() for term in ['crop', 'farm', 'field', 'plant', 'soil'])]
            
            if agricultural_keys:
                return f"Agricultural Data: {', '.join(agricultural_keys)}"
            
            return f"JSON Data: {', '.join(keys)}"
        
        elif isinstance(json_data, list) and json_data:
            first_item = json_data[0]
            if isinstance(first_item, dict):
                return self._extract_json_title(first_item)
        
        return "JSON Document"
    
    def _analyze_json_structure(self, json_data: Any) -> Dict[str, Any]:
        """Analyze JSON structure for metadata."""
        if isinstance(json_data, dict):
            return {
                'type': 'object',
                'keys': list(json_data.keys())[:10],  # First 10 keys
                'total_keys': len(json_data)
            }
        elif isinstance(json_data, list):
            return {
                'type': 'array',
                'length': len(json_data),
                'item_types': list(set(type(item).__name__ for item in json_data[:10]))
            }
        else:
            return {
                'type': type(json_data).__name__
            }
    
    def _count_keys(self, json_data: Any) -> int:
        """Count total number of keys in nested JSON structure."""
        if isinstance(json_data, dict):
            return len(json_data) + sum(self._count_keys(v) for v in json_data.values())
        elif isinstance(json_data, list):
            return sum(self._count_keys(item) for item in json_data)
        else:
            return 0