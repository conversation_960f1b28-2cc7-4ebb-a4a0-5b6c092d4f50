"""
Type definitions for E-Phy agricultural tools.
"""
from enum import Enum
from typing import Literal

# Authorization status types based on actual data
AuthorizationStatus = Literal["authorized_only", "all", "withdrawn"]

# Function categories based on database analysis
FunctionCategory = Literal[
    "Herbicide", "Fongicide", "Insecticide", "Matière fertilisante",
    "Molluscicide", "Adjuvant", "Subst. Croiss.", "Régulateur de croissance",
    "Fongicide | Insecticide", "Herbicide | Fongicide", "Acaricide",
    "Bactericide", "Nematicide", "Viricide"
]

# Target types from database
TargetType = Literal[
    "Champignon", "Insecte", "Autre", "Acarien", "Bactérie",
    "Régulateur", "Mollusque", "Oiseau", "Nématode", "Rongeur", "Virus"
]

# Crop categories from database
CropCategory = Literal[
    "Fruits", "Céréales", "Légumes", "Ornementales", "Sylviculture",
    "Oléagineux", "Cultures industrielles", "Prairies et fourrages",
    "Cultures tropicales", "Plantes à fibre", "Plantes aromatiques", "Autres"
]

# Product types
ProductType = Literal["PPP", "all"]

# Urgency levels for crop protection
UrgencyLevel = Literal["immediate", "normal", "preventive"]

# Application stages
ApplicationStage = Literal["preventive", "curative", "systematic"]

# Safety levels
SafetyLevel = Literal["low", "moderate", "high", "very_high"]

# Data quality levels
DataQuality = Literal["excellent", "good", "fair", "poor", "unavailable"]

# Sort options
SortBy = Literal["relevance", "name", "authorization_date", "effectiveness", "safety"]

# Language preferences
Language = Literal["french", "english", "auto"]

# Dosage units commonly found in database
DoseUnit = Literal[
    "L/ha", "kg/ha", "L/hL", "kg/hL", "mL/m²", "L/q", "g/q",
    "mL/10 m²", "mL/L", "kg/q", "g/m²", "L/10 m²", "g/10 m²", "g/L", "L/t"
]

# Application conditions
WeatherCondition = Literal["sunny", "cloudy", "humid", "dry", "windy", "calm"]

# Equipment types
EquipmentType = Literal["sprayer", "drone", "manual", "granule_spreader", "irrigation"]

# Budget constraints
BudgetConstraint = Literal["strict", "moderate", "flexible"]

# Resistance risk levels
ResistanceRisk = Literal["low", "medium", "high"]

# Environmental impact levels
EnvironmentalImpact = Literal["low", "medium", "high"]

# User types for compliance
UserType = Literal["professional", "amateur", "certified"]

# Hazard importance levels
HazardImportance = Literal["low", "standard", "high", "critical"]

# Condition types
ConditionType = Literal["regulatory", "safety", "environmental", "application"]

# Data scope options for product authorization lookup
DataScope = Literal["minimal", "summary", "detailed"]

# Response format options
ResponseFormat = Literal["concise", "detailed", "structured"]


class SearchConfidence(Enum):
    """Search confidence levels for fuzzy matching."""
    EXACT = 1.0
    HIGH = 0.8
    MEDIUM = 0.6
    LOW = 0.4
    VERY_LOW = 0.2


class DataCompleteness(Enum):
    """Data completeness scoring."""
    COMPLETE = 1.0
    MOSTLY_COMPLETE = 0.8
    PARTIAL = 0.6
    LIMITED = 0.4
    MINIMAL = 0.2
    NONE = 0.0
