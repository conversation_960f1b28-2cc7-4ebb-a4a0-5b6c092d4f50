"""
MIME type utilities for file detection and mapping.
Provides functions to detect MIME types from filenames and map them to categories.
"""
import mimetypes
from pathlib import Path
from typing import Optional, Literal

from schemas.assistant import MIMEType


def detect_mime_type_from_filename(filename: str) -> MIMEType:
    """
    Detect MIME type from filename extension.
    
    Args:
        filename: Name of the file
        
    Returns:
        MIMEType enum value
    """
    # Get file extension
    file_extension = Path(filename).suffix.lower()
    
    # Extension to MIME type mapping
    extension_map = {
        # Images
        '.jpg': MIMEType.IMAGE_JPEG,
        '.jpeg': MIMEType.IMAGE_JPEG,
        '.png': MIMEType.IMAGE_PNG,
        '.gif': MIMEType.IMAGE_GIF,
        '.bmp': MIMEType.IMAGE_BMP,
        '.tiff': MIMEType.IMAGE_TIFF,
        '.tif': MIMEType.IMAGE_TIFF,
        '.webp': MIMEType.IMAGE_WEBP,
        '.svg': MIMEType.IMAGE_SVG,
        
        # Documents
        '.pdf': MIMEType.APPLICATION_PDF,
        '.doc': MIMEType.APPLICATION_MSWORD,
        '.docx': MIMEType.APPLICATION_DOCX,
        '.xls': MIMEType.APPLICATION_EXCEL,
        '.xlsx': MIMEType.APPLICATION_XLSX,
        '.ppt': MIMEType.APPLICATION_POWERPOINT,
        '.pptx': MIMEType.APPLICATION_PPTX,
        
        # Text
        '.txt': MIMEType.TEXT_PLAIN,
        '.csv': MIMEType.TEXT_CSV,
        '.html': MIMEType.TEXT_HTML,
        '.htm': MIMEType.TEXT_HTML,
        '.rtf': MIMEType.TEXT_RTF,
        
        # Data
        '.json': MIMEType.APPLICATION_JSON,
        '.xml': MIMEType.APPLICATION_XML,
        
        # Archives
        '.zip': MIMEType.APPLICATION_ZIP,
        '.rar': MIMEType.APPLICATION_RAR,
    }
    
    # Try extension mapping first
    if file_extension in extension_map:
        return extension_map[file_extension]
    
    # Fallback to Python's mimetypes module
    mime_type_str, _ = mimetypes.guess_type(filename)
    if mime_type_str:
        # Try to match with our enum values
        for mime_enum in MIMEType:
            if mime_enum.value == mime_type_str:
                return mime_enum
    
    # Default fallback
    return MIMEType.AUTO


def mime_to_category(mime_type: MIMEType) -> Literal["image", "document", "other"]:
    """
    Map MIME type to simple category.
    
    Args:
        mime_type: MIMEType enum value
        
    Returns:
        Category string: "image", "document", or "other"
    """
    if mime_type == MIMEType.AUTO:
        return "other"
    
    # Image types
    image_types = {
        MIMEType.IMAGE_JPEG, MIMEType.IMAGE_PNG, MIMEType.IMAGE_GIF,
        MIMEType.IMAGE_BMP, MIMEType.IMAGE_TIFF, MIMEType.IMAGE_WEBP,
        MIMEType.IMAGE_SVG
    }
    
    # Document types
    document_types = {
        MIMEType.APPLICATION_PDF, MIMEType.APPLICATION_MSWORD, MIMEType.APPLICATION_DOCX,
        MIMEType.APPLICATION_EXCEL, MIMEType.APPLICATION_XLSX,
        MIMEType.APPLICATION_POWERPOINT, MIMEType.APPLICATION_PPTX,
        MIMEType.TEXT_PLAIN, MIMEType.TEXT_CSV, MIMEType.TEXT_HTML,
        MIMEType.TEXT_RTF, MIMEType.APPLICATION_JSON, MIMEType.APPLICATION_XML
    }
    
    if mime_type in image_types:
        return "image"
    elif mime_type in document_types:
        return "document"
    else:
        return "other"


def is_image_mime_type(mime_type: MIMEType) -> bool:
    """Check if MIME type is an image."""
    return mime_to_category(mime_type) == "image"


def is_document_mime_type(mime_type: MIMEType) -> bool:
    """Check if MIME type is a document."""
    return mime_to_category(mime_type) == "document"


def get_content_type_string(mime_type: MIMEType) -> str:
    """
    Get the content-type string for HTTP headers.
    
    Args:
        mime_type: MIMEType enum value
        
    Returns:
        Content-type string
    """
    if mime_type == MIMEType.AUTO:
        return "application/octet-stream"
    
    return mime_type.value


def resolve_mime_type(mime_type_input: Optional[MIMEType], filename: str) -> MIMEType:
    """
    Resolve final MIME type based on input specification and filename.
    
    Args:
        mime_type_input: MIME type from file input (can be AUTO or None)
        filename: Actual filename
        
    Returns:
        Resolved MIMEType
    """
    if mime_type_input and mime_type_input != MIMEType.AUTO:
        return mime_type_input
    
    # Auto-detect from filename
    return detect_mime_type_from_filename(filename)


# Keep the old function name for backward compatibility
def resolve_file_type(file_input_type: Optional[MIMEType], filename: str) -> MIMEType:
    """
    Deprecated: Use resolve_mime_type instead.
    """
    return resolve_mime_type(file_input_type, filename)