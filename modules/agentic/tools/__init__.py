import functools
import inspect
from typing import Callable

from pydantic_ai import RunContext

from const import EConnectionState
from models.dbm import DatabaseManager
from modules.agentic.utils.response_processor import ResponseProcessor
from schemas.agent import SAgentDeps
from utils.logger import logger


def with_event(func, event_message=None):
    """
    A decorator that adds event processing capabilities to a function.
    # Get the original signature
    This decorator:
    1. Handles RunContext extraction and processing
    2. Adds connection state events with the provided message
    3. Supports both synchronous and asynchronous functions
    4. Preserves function signatures and annotations

    Usage examples:

    # Basic usage with a function
    @with_event("Processing weather data")
    async def get_weather(ctx, location):
        # Function implementation
        pass

    # Usage with Tool creation
    Tool(with_event(get_current_agricultural_weather, "Getting agricultural weather"), takes_ctx=True)

    Args:
        func: The function to decorate
        event_message: Optional message to log during execution

    Returns:
        A wrapped function that handles context and events
    """
    original_sig = inspect.signature(func)
    original_params = list(original_sig.parameters.values())

    # Check if first param is ctx or not
    has_ctx_param = bool(original_params) and original_params[0].name == 'ctx'

    # Define our wrapper function
    async def wrapper_func(*args, **kwargs):
        if event_message:
            print(f"[Event] {event_message}")

        # Extract ctx from args if present
        new_args = args
        if args and hasattr(args[0], '__class__') and args[0].__class__.__name__ == 'RunContext':
            deps: SAgentDeps = args[0].deps
            processor: ResponseProcessor = deps.meta
            logger.debug(f"Calling external: {event_message}")
            if processor:
                await processor.add_event(
                    processor.create_connection_event(EConnectionState.processing, event_message))

            # If the original function doesn't have ctx param, remove it from args
            if not has_ctx_param:
                new_args = args[1:]  # Skip the ctx parameter for the actual function call
        # Make function support *arg or **kwargs any format
        # function = cast(Callable[[Any], str], func)
        # Call the original function
        result = func(*new_args, **kwargs)
        if inspect.isawaitable(result):
            return await result
        return result

    # Copy all metadata from the original function to the wrapper
    functools.update_wrapper(wrapper_func, func)

    # Modify the wrapper function to handle ctx properly
    if not has_ctx_param:
        # Create a new signature that includes ctx
        ctx_param = inspect.Parameter('ctx', inspect.Parameter.POSITIONAL_OR_KEYWORD,
                                      annotation=RunContext)
        new_params = [ctx_param] + original_params
        new_sig = inspect.Signature(parameters=new_params,
                                    return_annotation=original_sig.return_annotation)

        # Apply the new signature to the wrapper
        wrapper_func.__signature__ = new_sig

        # Update annotations to include ctx
        wrapper_func.__annotations__ = {'ctx': RunContext}

        # Copy over the original function's annotations
        if hasattr(func, '__annotations__') and func.__annotations__:
            for name, annotation in func.__annotations__.items():
                wrapper_func.__annotations__[name] = annotation
    else:
        # Just preserve the original signature and annotations
        wrapper_func.__signature__ = original_sig
        wrapper_func.__annotations__ = func.__annotations__.copy()

    # Copy other metadata from the original function
    # functools.update_wrapper(wrapper_func, func)

    return wrapper_func


def with_db_session(func: Callable, event_message: str = None) -> Callable:
    """
    A decorator that adds database session management to async functions that require database access.
    This decorator:
    1. Handles async database session creation and cleanup
    2. Forwards function docstrings for LLM tool descriptions
    3. Properly manages parallel tool execution with independent sessions
    4. Integrates with the with_event decorator for consistent event handling
    
    Usage examples:
    
    # Basic usage with a database function
    @with_db_session("Searching agricultural products...")
    async def search_products(query: str, include_withdrawn: bool = False):
        # Function will receive session as first parameter automatically
        pass
    
    # Usage with Tool creation  
    Tool(with_db_session(EPhyTool.search_agricultural_products, "Searching agricultural products..."), takes_ctx=True)
    
    Args:
        func: The async function to decorate (should expect session as first parameter)
        event_message: Optional message to log during execution
        
    Returns:
        A wrapped function that handles database sessions and context
    """
    # Get the original function signature
    original_sig = inspect.signature(func)
    original_params = list(original_sig.parameters.values())

    # Check if the function expects a session parameter (for class methods, it's usually the second param after cls)
    has_session_param = (
            len(original_params) > 0 and
            (original_params[0].name in ['session', 'sess'] or
             (len(original_params) > 1 and original_params[1].name in ['session', 'sess']))
    )

    if not has_session_param:
        raise ValueError(f"Function {func.__name__} must have a 'session' parameter to use with_db_session decorator")

    # Create the wrapper function that manages database sessions
    async def db_wrapper_func(*args, **kwargs):
        db_manager = DatabaseManager()

        # Create async session for this tool execution
        async with db_manager.session_scope() as session:
            # For classmethod functions, we need to inject the session as the second parameter
            # The first parameter should be the class itself
            if len(original_params) > 0 and original_params[0].name == 'cls':
                # This is a classmethod - get the owning class
                if hasattr(func, '__self__') and func.__self__ is not None:
                    # Bound classmethod
                    owner_class = func.__self__
                else:
                    # Unbound classmethod - try to get the class from the qualname
                    module_name = getattr(func, '__module__', None)
                    qualname = getattr(func, '__qualname__', '')

                    if module_name and '.' in qualname:
                        class_name = qualname.split('.')[0]
                        try:
                            import importlib
                            module = importlib.import_module(module_name)
                            owner_class = getattr(module, class_name, None)
                        except:
                            owner_class = None
                    else:
                        owner_class = None

                if owner_class:
                    # Call with (cls, session, *original_args)
                    new_args = (owner_class, session) + args
                else:
                    # Fallback: call with (session, *original_args) 
                    new_args = (session,) + args

            elif hasattr(func, '__self__'):  # Instance method
                # For instance methods, inject session after self
                new_args = (args[0], session) + args[1:] if args else (session,)
            else:  # Regular function
                # For regular functions, session goes first
                new_args = (session,) + args

            # Call the original function with the session
            result = await func(*new_args, **kwargs)
            return result

    # Copy function metadata including docstring
    functools.update_wrapper(db_wrapper_func, func)

    # Create new signature without the session and cls parameters for the external interface
    if len(original_params) > 1 and original_params[0].name == 'cls' and original_params[1].name in ['session', 'sess']:
        # For class methods, remove both cls and session parameters (first two parameters)
        new_params = original_params[2:]
    elif len(original_params) > 1 and original_params[1].name in ['session', 'sess']:
        # For instance methods, remove the session parameter (second parameter)
        new_params = [original_params[0]] + original_params[2:]
    elif original_params[0].name in ['session', 'sess']:
        # For regular functions, remove the session parameter (first parameter) 
        new_params = original_params[1:]
    else:
        new_params = original_params

    new_sig = inspect.Signature(
        parameters=new_params,
        return_annotation=original_sig.return_annotation
    )
    db_wrapper_func.__signature__ = new_sig

    # Copy annotations, excluding session parameter
    new_annotations = {}
    if hasattr(func, '__annotations__') and func.__annotations__:
        for name, annotation in func.__annotations__.items():
            if name not in ['session', 'sess']:
                new_annotations[name] = annotation
    db_wrapper_func.__annotations__ = new_annotations

    # Apply the with_event decorator for consistent event handling
    if event_message:
        return with_event(db_wrapper_func, event_message)
    else:
        return with_event(db_wrapper_func, f"Executing {func.__name__}...")

# def with_event_tool(func, event_message=None):
#     from pydantic_ai.tools import Tool  # Import here to avoid circular imports
#
#     @functools.wraps(func)
#     async def wrapper(*args, **kwargs):
#         if event_message:
#             print(f"[Event] {event_message}")
#
#         # Extract ctx from args if present
#         if args and hasattr(args[0], '__class__') and args[0].__class__.__name__ == 'RunContext':
#             ctx = args[0]
#             print(f"[Context] {ctx}")
#
#         # Make function support *arg or **kwargs any format
#         function = cast(Callable[[Any], str], func)
#         # Handle both async and non-async functions
#         result = function(*args, **kwargs)
#         if inspect.isawaitable(result):
#             return await result
#         return result
#
#     # Check if the first parameter is annotated with RunContext
#     sig = inspect.signature(func)
#     params = list(sig.parameters.values())
#     takes_context = False
#
#     if params and 'RunContext' in str(params[0].annotation):
#         takes_context = True
#
#     # Return a Tool instance directly
#     return Tool(wrapper, takes_ctx=takes_context)


# Forward function metadata to wrapper
# def with_event_working(func, event_message=None):
#     @functools.wraps(func)  # This preserves the original function's metadata
#     async def wrapper(*args, **kwargs):
#         if event_message:
#             print(f"[Event] {event_message}")
#
#         # Extract ctx from args if present
#         if args and hasattr(args[0], '__class__') and args[0].__class__.__name__ == 'RunContext':
#             ctx = args[0]
#             print(f"[Context] {ctx}")
#
#         # Make function support *arg or **kwargs any format
#         function = cast(Callable[[Any], str], func)
#         # Handle both async and non-async functions
#         result = function(*args, **kwargs)
#         if inspect.isawaitable(result):
#             return await result
#         return result
#
#     return wrapper
