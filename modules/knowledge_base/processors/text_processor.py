"""
Text document processor for plain text, TXT files, and fallback processing.
Handles text encoding detection and basic text processing.
"""
import chardet
from typing import List, Dict, Any, Optional
from uuid import UUID

from modules.knowledge_base.processors.base_processor import BaseDocumentProcessor, ProcessingResult
from utils.logger import logger


class TextProcessor(BaseDocumentProcessor):
    """Processor for plain text documents."""
    
    def __init__(self):
        super().__init__()
        self.supported_extensions = {'.txt', '.text', '.log', '.md', '.rst'}
    
    async def process(self, file_content: bytes, document_id: UUID) -> ProcessingResult:
        """
        Process text file and extract content.
        
        Args:
            file_content: Text file content as bytes
            document_id: Document ID for reference
            
        Returns:
            ProcessingResult with extracted text and metadata
        """
        try:
            # Detect encoding
            encoding_result = chardet.detect(file_content)
            encoding = encoding_result.get('encoding', 'utf-8')
            confidence = encoding_result.get('confidence', 0.0)
            
            if not encoding or confidence < 0.5:
                # Fallback to common encodings
                for fallback_encoding in ['utf-8', 'utf-16', 'latin-1', 'cp1252']:
                    try:
                        text_content = file_content.decode(fallback_encoding)
                        encoding = fallback_encoding
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    return ProcessingResult(
                        success=False,
                        error="Unable to decode text file - unsupported encoding"
                    )
            else:
                try:
                    text_content = file_content.decode(encoding)
                except UnicodeDecodeError:
                    # Try with error handling
                    text_content = file_content.decode(encoding, errors='replace')
            
            # Clean and process text
            cleaned_text = self._clean_text(text_content)
            
            if not cleaned_text.strip():
                return ProcessingResult(
                    success=False,
                    error="File contains no readable text content"
                )
            
            # Extract metadata
            title = self._extract_title(cleaned_text)
            language = self._detect_language(cleaned_text)
            agricultural_metadata = await self._extract_agricultural_metadata(cleaned_text)
            
            # Estimate page count based on text length (rough estimate)
            estimated_pages = max(1, len(cleaned_text) // 2000)  # ~2000 chars per page
            
            return ProcessingResult(
                success=True,
                content=cleaned_text,
                title=title,
                language=language,
                total_pages=estimated_pages,
                crop_categories=agricultural_metadata.get('crop_categories', []),
                topics=agricultural_metadata.get('topics', []),
                geographic_scope=agricultural_metadata.get('geographic_scope', []),
                seasonal_relevance=agricultural_metadata.get('seasonal_relevance', []),
                metadata={
                    'processor': 'TextProcessor',
                    'encoding': encoding,
                    'encoding_confidence': confidence,
                    'original_size_bytes': len(file_content),
                    'processed_size_chars': len(cleaned_text)
                }
            )
            
        except Exception as e:
            logger.error(f"Error processing text document {document_id}: {e}")
            return ProcessingResult(
                success=False,
                error=f"Text processing error: {str(e)}"
            )
    
    def _clean_text(self, text: str) -> str:
        """Clean text content by normalizing whitespace and removing artifacts."""
        if not text:
            return ""
        
        # Normalize line endings
        text = text.replace('\r\n', '\n').replace('\r', '\n')
        
        # Remove excessive whitespace while preserving structure
        lines = text.split('\n')
        cleaned_lines = []
        
        consecutive_empty = 0
        for line in lines:
            line = line.rstrip()  # Remove trailing whitespace
            
            if not line:
                consecutive_empty += 1
                # Keep at most 2 consecutive empty lines
                if consecutive_empty <= 2:
                    cleaned_lines.append('')
            else:
                consecutive_empty = 0
                # Normalize internal whitespace
                line = ' '.join(line.split())
                cleaned_lines.append(line)
        
        # Join and remove leading/trailing whitespace
        cleaned_text = '\n'.join(cleaned_lines).strip()
        
        # Remove common text artifacts
        artifacts = [
            '\ufeff',  # BOM
            '\x00',    # Null bytes
        ]
        
        for artifact in artifacts:
            cleaned_text = cleaned_text.replace(artifact, '')
        
        return cleaned_text
    
    def _extract_title(self, content: str) -> Optional[str]:
        """
        Extract title from text content.
        Looks for title patterns in the first few lines.
        """
        lines = content.split('\n')
        
        # Look for potential titles in first 10 lines
        for i, line in enumerate(lines[:10]):
            line = line.strip()
            
            if not line:
                continue
            
            # Skip lines that look like metadata or headers
            if any(indicator in line.lower() for indicator in ['date:', 'author:', 'file:', 'version:']):
                continue
            
            # Check if line looks like a title
            if self._looks_like_title(line):
                return line
        
        return None
    
    def _looks_like_title(self, line: str) -> bool:
        """Check if a line looks like a title."""
        # Title characteristics
        if len(line) < 5 or len(line) > 150:
            return False
        
        # Likely title if:
        # - Starts with capital letter
        # - Contains title-like words
        # - Not too long
        # - Doesn't end with period (unlikely for titles)
        
        if not line[0].isupper():
            return False
        
        if line.endswith('.') and not any(word in line.lower() for word in ['dr.', 'mr.', 'mrs.', 'inc.', 'ltd.']):
            return False
        
        # Boost for title-like words
        title_words = ['guide', 'manual', 'report', 'study', 'analysis', 'overview', 'introduction']
        if any(word in line.lower() for word in title_words):
            return True
        
        # Check if it's a reasonable length for a title
        word_count = len(line.split())
        return 2 <= word_count <= 20