"""
Enhanced Alternative Products Finder Tool for E-Phy Agricultural Database.
AI Agent optimized tool for discovering substitute products with comprehensive
data quality indicators, fallback logic, and enhanced decision-making support.
"""
from dataclasses import dataclass
from typing import List, Optional, Dict, Any, Tuple

from sqlalchemy import select, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from models.e_phy import Product, ProductUse, ProductSubstance, ActiveSubstance, Crop, Target


@dataclass
class AlternativeProduct:
    """Enhanced alternative product information for AI agent decision-making."""
    product_id: int
    product_name: str
    registration_number: str
    active_substances: List[str]
    function_category: str
    similarity_score: float
    resistance_management_value: str
    relative_efficacy: str
    application_differences: List[str]
    availability_status: str
    user_restrictions: List[str]
    cost_implications: Optional[str]

    # Enhanced AI decision-making fields
    data_completeness_score: float
    confidence_level: str
    ai_recommendation_priority: str
    substance_mode_of_action: List[str]
    environmental_risk_level: str
    usage_complexity: str
    data_quality_indicators: Dict[str, str]
    fallback_reasoning: List[str]


@dataclass
class ResistanceStrategy:
    """Enhanced resistance management strategy for AI agent guidance."""
    strategy_type: str
    recommended_sequence: List[str]
    timing_suggestions: List[str]
    rotation_period: str
    effectiveness_rating: str
    implementation_difficulty: str

    # Enhanced AI guidance fields
    confidence_in_strategy: str
    data_limitations: List[str]
    risk_assessment: str
    alternative_strategies: List[str]
    success_probability: str


@dataclass
class AlternativeAnalysis:
    """Enhanced analysis of alternative options for AI agent understanding."""
    total_alternatives_found: int
    categories_covered: List[str]
    resistance_diversity_score: float
    coverage_gaps: List[str]
    seasonal_considerations: List[str]
    regional_availability: Dict[str, int]

    # Enhanced AI understanding fields
    data_reliability_score: float
    search_completeness: str
    critical_missing_data: List[str]
    ai_confidence_indicators: Dict[str, str]
    decision_complexity: str
    recommendation_certainty: str


@dataclass
class AlternativeProductsResponse:
    """Enhanced alternative products response optimized for AI agent decision-making."""
    reference_product: Dict[str, Any]
    alternatives: List[AlternativeProduct]
    resistance_strategy: ResistanceStrategy
    analysis: AlternativeAnalysis
    recommendations: List[str]
    limitations: List[str]
    search_metadata: Dict[str, Any]

    # Enhanced AI decision support fields
    ai_decision_support: Dict[str, Any]
    data_quality_summary: Dict[str, Any]
    fallback_explanations: List[str]
    confidence_assessment: Dict[str, str]
    next_steps_guidance: List[str]


async def find_alternative_products(
        session: AsyncSession,
        reference_product_id: int,
        crop_name: Optional[str] = None,
        target_pest: Optional[str] = None,
        resistance_management: bool = True,
        same_active_substance: bool = False,
        organic_alternatives: bool = False,
        availability_status: str = "available",
        limit: int = 15,
) -> AlternativeProductsResponse:
    """
    Alternative products finder with AI agent optimizations.
    
    Args:
        reference_product_id: ID of the reference product
        crop_name: Specific crop name (optional)
        target_pest: Specific target pest/disease (optional)
        resistance_management: Find different modes of action
        same_active_substance: Find products with same active ingredient
        organic_alternatives: Include biological options
        availability_status: Filter by availability
        limit: Maximum number of alternatives

    Returns:
        AlternativeProductsResponse with AI decision support
    """

    # Initialize tracking for data quality and AI decision support
    data_quality_tracker = DataQualityTracker()
    fallback_explanations = []

    # Get reference product with enhanced validation
    reference_product, ref_quality_issues = await _get_reference_product_enhanced(
        session, reference_product_id, data_quality_tracker
    )

    if not reference_product:
        return _create_enhanced_no_reference_response(reference_product_id, data_quality_tracker)

    # Enhanced crop and target finding with fallback logic
    crop, crop_fallback_used = await _find_crop_by_name_enhanced(session, crop_name)
    target, target_fallback_used = await _find_target_by_name_enhanced(session, target_pest)

    if crop_fallback_used:
        fallback_explanations.append(
            f"Used fuzzy matching for crop: '{crop_name}' → '{crop.crop_name if crop else 'not found'}'")
    if target_fallback_used:
        fallback_explanations.append(
            f"Used enhanced search for target: '{target_pest}' → '{target.target_name if target else 'not found'}'")

    # Get reference product's active substances with quality assessment
    reference_substances, substance_quality = await _get_product_substances_enhanced(
        session, reference_product_id, data_quality_tracker
    )

    # Enhanced search for alternatives with multiple strategies
    alternatives = await _find_alternatives_with_fallback_strategies(
        session, reference_product, reference_substances, crop, target,
        same_active_substance, resistance_management, organic_alternatives,
        availability_status, limit, data_quality_tracker, fallback_explanations
    )

    # Enhanced similarity and resistance calculations
    for alt_product in alternatives:
        await _enhance_alternative_with_ai_features(
            session, reference_product, alt_product, reference_substances, data_quality_tracker
        )

    # Sort with AI-optimized ranking
    alternatives = _sort_alternatives_for_ai_agent(alternatives, same_active_substance, resistance_management)

    # Generate enhanced resistance strategy
    resistance_strategy = await _generate_enhanced_resistance_strategy(
        session, reference_substances, alternatives, data_quality_tracker
    )

    # Comprehensive analysis with AI insights
    analysis = await _analyze_alternatives_for_ai_agent(
        session, reference_product, alternatives, data_quality_tracker
    )

    # AI-optimized recommendations
    recommendations = _generate_ai_optimized_recommendations(
        reference_product, alternatives, resistance_strategy, analysis, data_quality_tracker
    )

    # Enhanced limitations with actionable insights
    limitations = _identify_limitations_with_solutions(
        crop, target, alternatives, data_quality_tracker
    )

    # Create AI decision support summary
    ai_decision_support = _create_ai_decision_support(
        reference_product, alternatives, resistance_strategy, data_quality_tracker
    )

    # Data quality summary for AI understanding
    data_quality_summary = data_quality_tracker.get_summary()

    # Confidence assessment
    confidence_assessment = _assess_overall_confidence(
        alternatives, resistance_strategy, data_quality_tracker
    )

    # Next steps guidance for AI agent
    next_steps_guidance = _generate_next_steps_guidance(
        alternatives, resistance_strategy, data_quality_tracker
    )

    return AlternativeProductsResponse(
        reference_product={
            "product_id": reference_product.id,
            "name": reference_product.product_name,
            "registration_number": reference_product.registration_number,
            "active_substances": [sub.name for sub in reference_substances],
            "function_category": reference_product.function_category,
            "authorization_status": reference_product.authorization_status,
            "data_quality_score": data_quality_tracker.get_product_quality_score(reference_product.id)
        },
        alternatives=alternatives[:limit],
        resistance_strategy=resistance_strategy,
        analysis=analysis,
        recommendations=recommendations,
        limitations=limitations,
        search_metadata={
            "search_criteria": {
                "resistance_management": resistance_management,
                "same_active_substance": same_active_substance,
                "organic_alternatives": organic_alternatives,
                "crop_specified": crop_name is not None,
                "target_specified": target_pest is not None
            },
            "alternatives_found": len(alternatives),
            "search_confidence": analysis.resistance_diversity_score,
            "fallback_strategies_used": len(fallback_explanations),
            "data_completeness": data_quality_tracker.get_overall_completeness()
        },
        ai_decision_support=ai_decision_support,
        data_quality_summary=data_quality_summary,
        fallback_explanations=fallback_explanations,
        confidence_assessment=confidence_assessment,
        next_steps_guidance=next_steps_guidance
    )


class DataQualityTracker:
    """Tracks data quality issues and provides AI-friendly insights."""

    def __init__(self):
        self.missing_substance_count = 0
        self.total_products_checked = 0
        self.quality_issues = []
        self.product_quality_scores = {}
        self.fallback_strategies_used = []

    def track_missing_substances(self, product_id: int, product_name: str):
        self.missing_substance_count += 1
        self.quality_issues.append(f"Missing active substance data for {product_name}")
        self.product_quality_scores[product_id] = 0.3  # Low quality due to missing substances

    def track_product_checked(self, product_id: int, quality_score: float = 1.0):
        self.total_products_checked += 1
        self.product_quality_scores[product_id] = quality_score

    def add_quality_issue(self, issue: str):
        self.quality_issues.append(issue)

    def add_fallback_strategy(self, strategy: str):
        self.fallback_strategies_used.append(strategy)

    def get_product_quality_score(self, product_id: int) -> float:
        return self.product_quality_scores.get(product_id, 0.5)

    def get_overall_completeness(self) -> float:
        if self.total_products_checked == 0:
            return 0.0
        return 1.0 - (self.missing_substance_count / self.total_products_checked)

    def get_summary(self) -> Dict[str, Any]:
        return {
            "total_products_analyzed": self.total_products_checked,
            "missing_substance_rate": self.missing_substance_count / max(self.total_products_checked, 1),
            "critical_issues_count": len(self.quality_issues),
            "fallback_strategies_count": len(self.fallback_strategies_used),
            "overall_data_reliability": "high" if self.get_overall_completeness() > 0.8
            else "medium" if self.get_overall_completeness() > 0.5
            else "low"
        }


async def _get_reference_product_enhanced(
        session: AsyncSession,
        product_id: int,
        quality_tracker: DataQualityTracker
) -> Tuple[Optional[Product], List[str]]:
    """Enhanced reference product retrieval with quality assessment."""

    query = select(Product).options(
        selectinload(Product.substances).selectinload(ProductSubstance.substance)
    ).where(Product.id == product_id)

    result = await session.execute(query)
    product = result.scalar_one_or_none()

    quality_issues = []

    if product:
        # Assess product data quality
        quality_score = 1.0

        if not product.function_category:
            quality_issues.append("Missing function category")
            quality_score -= 0.2

        if not product.substances or len(product.substances) == 0:
            quality_issues.append("Missing active substance data")
            quality_tracker.track_missing_substances(product.id, product.product_name)
            quality_score -= 0.5
        else:
            quality_tracker.track_product_checked(product.id, quality_score)

        if not product.is_currently_authorized:
            quality_issues.append("Product not currently authorized")
            quality_score -= 0.3

        quality_tracker.track_product_checked(product.id, max(quality_score, 0.1))

    return product, quality_issues


async def _find_crop_by_name_enhanced(
        session: AsyncSession,
        crop_name: Optional[str]
) -> Tuple[Optional[Crop], bool]:
    """Enhanced crop finding with expanded synonym matching."""

    if not crop_name:
        return None, False

    # Extended crop translations for better AI agent understanding
    crop_translations = {
        # English to French
        "wheat": "blé", "corn": "maïs", "maize": "maïs", "potato": "pomme de terre",
        "rape": "colza", "rapeseed": "colza", "canola": "colza", "barley": "orge",
        "sunflower": "tournesol", "soybean": "soja", "sugar beet": "betterave sucrière",
        "tomato": "tomate", "cucumber": "concombre", "lettuce": "laitue",
        "apple": "pomme", "grape": "vigne", "vineyard": "vigne",

        # French alternatives
        "ble": "blé", "mais": "maïs", "pommes de terre": "pomme de terre",
        "oleagineux": "colza", "cereales": "blé"
    }

    search_terms = [crop_name.lower().strip()]

    # Add translation if available
    normalized_input = crop_name.lower().strip()
    if normalized_input in crop_translations:
        search_terms.append(crop_translations[normalized_input])

    # Try reverse translations
    for english, french in crop_translations.items():
        if normalized_input == french:
            search_terms.append(english)

    # Build comprehensive query
    query = select(Crop).where(
        or_(
            *[Crop.crop_name.ilike(f"%{term}%") for term in search_terms],
            *[Crop.normalized_name.ilike(f"%{term}%") for term in search_terms],
            *[Crop.common_synonyms.ilike(f"%{term}%") for term in search_terms]
        )
    )

    result = await session.execute(query)
    crop = result.scalar_one_or_none()

    # Determine if fallback was used
    fallback_used = crop is not None and not any(
        term in crop.crop_name.lower() for term in [crop_name.lower()]
    )

    return crop, fallback_used


async def _find_target_by_name_enhanced(
        session: AsyncSession,
        target_name: Optional[str]
) -> Tuple[Optional[Target], bool]:
    """Enhanced target finding with comprehensive translation support."""

    if not target_name:
        return None, False

    # Comprehensive target translations for agricultural AI
    target_translations = {
        # Disease translations
        "late blight": "mildiou", "downy mildew": "mildiou", "mildew": "mildiou",
        "powdery mildew": "oïdium", "oidium": "oïdium", "rust": "rouille",
        "black spot": "tache noire", "brown rot": "pourriture brune",
        "grey mold": "pourriture grise", "botrytis": "pourriture grise",
        "anthracnose": "anthracnose", "scab": "tavelure",
        "canker": "chancre", "blight": "brûlure",

        # Pest translations
        "aphids": "pucerons", "plant lice": "pucerons", "greenfly": "pucerons",
        "caterpillars": "chenilles", "larvae": "larves", "grubs": "vers blancs",
        "slugs": "limaces", "snails": "escargots", "wireworms": "taupins",
        "thrips": "thrips", "spider mites": "acariens", "mites": "acariens",
        "whitefly": "aleurodes", "scale insects": "cochenilles",
        "beetles": "coléoptères", "weevils": "charançons",

        # Weed translations
        "weeds": "adventices", "grass weeds": "graminées adventices",
        "broadleaf weeds": "dicotylédones adventices",
        "wild oats": "folles avoines", "blackgrass": "vulpin",

        # French alternatives
        "puceron": "pucerons", "chenille": "chenilles", "limace": "limaces",
        "acarien": "acariens", "adventice": "adventices"
    }

    search_terms = [target_name.lower().strip()]

    # Add translations
    normalized_input = target_name.lower().strip()
    if normalized_input in target_translations:
        search_terms.append(target_translations[normalized_input])

    # Try reverse translations
    for english, french in target_translations.items():
        if normalized_input == french:
            search_terms.append(english)

    # Enhanced query with broader matching
    query = select(Target).where(
        or_(
            *[Target.target_name.ilike(f"%{term}%") for term in search_terms],
            # Try partial matches for complex target names
            *[Target.target_name.ilike(f"%{term.split()[0]}%") for term in search_terms if ' ' in term]
        )
    )

    result = await session.execute(query)
    target = result.scalars().first()  # Get first match instead of requiring only one

    # Determine if enhanced search was used
    fallback_used = target is not None and not any(
        term in target.target_name.lower() for term in [target_name.lower()]
    )

    return target, fallback_used


async def _get_product_substances_enhanced(
        session: AsyncSession,
        product_id: int,
        quality_tracker: DataQualityTracker
) -> Tuple[List[ActiveSubstance], Dict[str, Any]]:
    """Enhanced active substance retrieval with quality assessment."""

    query = select(ActiveSubstance).join(ProductSubstance).where(
        ProductSubstance.product_id == product_id
    )
    result = await session.execute(query)
    substances = result.scalars().all()

    substance_quality = {
        "substances_found": len(substances),
        "has_complete_data": len(substances) > 0,
        "quality_issues": []
    }

    if len(substances) == 0:
        substance_quality["quality_issues"].append("No active substance data available")
        quality_tracker.add_quality_issue(f"Product {product_id} missing active substances")

    return substances, substance_quality


async def _find_alternatives_with_fallback_strategies(
        session: AsyncSession,
        reference_product: Product,
        reference_substances: List[ActiveSubstance],
        crop: Optional[Crop],
        target: Optional[Target],
        same_active_substance: bool,
        resistance_management: bool,
        organic_alternatives: bool,
        availability_status: str,
        limit: int,
        quality_tracker: DataQualityTracker,
        fallback_explanations: List[str]
) -> List[AlternativeProduct]:
    """Find alternatives using multiple fallback strategies for comprehensive coverage."""

    alternatives = []

    # Strategy 1: Exact crop-target matches (highest priority)
    if crop and target:
        exact_alternatives = await _find_crop_target_alternatives_enhanced(
            session, reference_product.id, crop.id, target.id,
            same_active_substance, resistance_management, organic_alternatives,
            availability_status, quality_tracker
        )
        alternatives.extend(exact_alternatives)
        if exact_alternatives:
            fallback_explanations.append(f"Found {len(exact_alternatives)} exact crop-target matches")

    # Strategy 2: Crop-only matches (medium priority)
    if crop and len(alternatives) < limit:
        crop_alternatives = await _find_crop_only_alternatives(
            session, reference_product.id, crop.id, same_active_substance,
            resistance_management, organic_alternatives, availability_status, quality_tracker
        )
        # Remove duplicates
        existing_ids = {alt.product_id for alt in alternatives}
        new_crop_alternatives = [alt for alt in crop_alternatives if alt.product_id not in existing_ids]
        alternatives.extend(new_crop_alternatives)
        if new_crop_alternatives:
            fallback_explanations.append(f"Added {len(new_crop_alternatives)} crop-specific alternatives")

    # Strategy 3: Function category matches (broader search)
    if len(alternatives) < limit:
        function_alternatives = await _find_function_category_alternatives_enhanced(
            session, reference_product, reference_substances, same_active_substance,
            organic_alternatives, availability_status, limit - len(alternatives), quality_tracker
        )
        # Remove duplicates
        existing_ids = {alt.product_id for alt in alternatives}
        new_function_alternatives = [alt for alt in function_alternatives if alt.product_id not in existing_ids]
        alternatives.extend(new_function_alternatives)
        if new_function_alternatives:
            fallback_explanations.append(f"Added {len(new_function_alternatives)} function category alternatives")

    # Strategy 4: Emergency fallback - similar products by name similarity
    if len(alternatives) < 3:  # If we have very few alternatives
        emergency_alternatives = await _find_emergency_fallback_alternatives(
            session, reference_product, availability_status, quality_tracker
        )
        existing_ids = {alt.product_id for alt in alternatives}
        emergency_new = [alt for alt in emergency_alternatives if alt.product_id not in existing_ids]
        alternatives.extend(emergency_new)
        if emergency_new:
            fallback_explanations.append(
                f"Used emergency fallback to find {len(emergency_new)} additional alternatives")

    return alternatives


async def _find_crop_target_alternatives_enhanced(
        session: AsyncSession,
        reference_product_id: int,
        crop_id: int,
        target_id: int,
        same_active_substance: bool,
        resistance_management: bool,
        organic_alternatives: bool,
        availability_status: str,
        quality_tracker: DataQualityTracker
) -> List[AlternativeProduct]:
    """Find alternatives for specific crop-target combination with enhanced data quality tracking."""

    query = select(Product).options(
        selectinload(Product.substances).selectinload(ProductSubstance.substance)
    ).join(ProductUse).where(
        and_(
            ProductUse.crop_id == crop_id,
            ProductUse.target_id == target_id,
            Product.id != reference_product_id,
            ProductUse.is_currently_authorized == True
        )
    )

    # Apply filters
    if availability_status == "available":
        query = query.where(Product.is_currently_authorized == True)

    if organic_alternatives:
        query = query.where(
            or_(
                Product.function_category.ilike("%biologique%"),
                Product.function_category.ilike("%bio%"),
                Product.authorized_mentions.ilike("%biologique%"),
                Product.authorized_mentions.ilike("%biocontrôle%")
            )
        )

    result = await session.execute(query.distinct())
    products = result.scalars().all()

    alternatives = []
    for product in products:
        # Enhanced quality assessment
        substances = [ps.substance.name for ps in product.substances if ps.substance]
        data_completeness = _calculate_data_completeness(product, substances)

        quality_tracker.track_product_checked(product.id, data_completeness)

        alternative = AlternativeProduct(
            product_id=product.id,
            product_name=product.product_name,
            registration_number=product.registration_number,
            active_substances=substances,
            function_category=product.function_category or "Unknown",
            similarity_score=0.0,  # Will be calculated later
            resistance_management_value="unknown",
            relative_efficacy="similar",  # Same crop-target suggests similar efficacy
            application_differences=[],
            availability_status="available" if product.is_currently_authorized else "limited",
            user_restrictions=_extract_user_restrictions(product),
            cost_implications=None,

            # Enhanced AI fields
            data_completeness_score=data_completeness,
            confidence_level=_determine_confidence_level(data_completeness, substances),
            ai_recommendation_priority="high",  # Exact matches get high priority
            substance_mode_of_action=_infer_mode_of_action(substances),
            environmental_risk_level=_assess_environmental_risk(product),
            usage_complexity=_assess_usage_complexity(product),
            data_quality_indicators=_get_data_quality_indicators(product, substances),
            fallback_reasoning=[]
        )

        alternatives.append(alternative)

    return alternatives


async def _find_crop_only_alternatives(
        session: AsyncSession,
        reference_product_id: int,
        crop_id: int,
        same_active_substance: bool,
        resistance_management: bool,
        organic_alternatives: bool,
        availability_status: str,
        quality_tracker: DataQualityTracker
) -> List[AlternativeProduct]:
    """Find alternatives for specific crop (broader target coverage)."""

    query = select(Product).options(
        selectinload(Product.substances).selectinload(ProductSubstance.substance)
    ).join(ProductUse).where(
        and_(
            ProductUse.crop_id == crop_id,
            Product.id != reference_product_id,
            ProductUse.is_currently_authorized == True
        )
    )

    # Apply filters
    if availability_status == "available":
        query = query.where(Product.is_currently_authorized == True)

    if organic_alternatives:
        query = query.where(
            or_(
                Product.function_category.ilike("%biologique%"),
                Product.authorized_mentions.ilike("%biologique%"),
                Product.authorized_mentions.ilike("%biocontrôle%")
            )
        )

    result = await session.execute(query.distinct().limit(10))
    products = result.scalars().all()

    alternatives = []
    for product in products:
        substances = [ps.substance.name for ps in product.substances if ps.substance]
        data_completeness = _calculate_data_completeness(product, substances)

        quality_tracker.track_product_checked(product.id, data_completeness)

        alternative = AlternativeProduct(
            product_id=product.id,
            product_name=product.product_name,
            registration_number=product.registration_number,
            active_substances=substances,
            function_category=product.function_category or "Unknown",
            similarity_score=0.0,
            resistance_management_value="unknown",
            relative_efficacy="potentially_similar",
            application_differences=[],
            availability_status="available" if product.is_currently_authorized else "limited",
            user_restrictions=_extract_user_restrictions(product),
            cost_implications=None,

            # Enhanced AI fields
            data_completeness_score=data_completeness,
            confidence_level=_determine_confidence_level(data_completeness, substances),
            ai_recommendation_priority="medium",  # Crop matches get medium priority
            substance_mode_of_action=_infer_mode_of_action(substances),
            environmental_risk_level=_assess_environmental_risk(product),
            usage_complexity=_assess_usage_complexity(product),
            data_quality_indicators=_get_data_quality_indicators(product, substances),
            fallback_reasoning=["Crop-specific match (broader target coverage)"]
        )

        alternatives.append(alternative)

    return alternatives


async def _find_function_category_alternatives_enhanced(
        session: AsyncSession,
        reference_product: Product,
        reference_substances: List[ActiveSubstance],
        same_active_substance: bool,
        organic_alternatives: bool,
        availability_status: str,
        limit: int,
        quality_tracker: DataQualityTracker
) -> List[AlternativeProduct]:
    """Enhanced function category search with better quality tracking."""

    if not reference_product.function_category:
        return []

    # Extract main functions (handle pipe-separated categories)
    main_functions = [func.strip() for func in reference_product.function_category.split("|")]

    alternatives = []

    for function in main_functions:
        query = select(Product).options(
            selectinload(Product.substances).selectinload(ProductSubstance.substance)
        ).where(
            and_(
                Product.function_category.ilike(f"%{function}%"),
                Product.id != reference_product.id
            )
        )

        # Apply filters
        if availability_status == "available":
            query = query.where(Product.is_currently_authorized == True)

        if organic_alternatives:
            query = query.where(
                or_(
                    Product.function_category.ilike("%biologique%"),
                    Product.authorized_mentions.ilike("%biologique%"),
                    Product.authorized_mentions.ilike("%biocontrôle%")
                )
            )

        result = await session.execute(query.limit(limit))
        products = result.scalars().all()

        for product in products:
            substances = [ps.substance.name for ps in product.substances if ps.substance]
            data_completeness = _calculate_data_completeness(product, substances)

            quality_tracker.track_product_checked(product.id, data_completeness)

            alternative = AlternativeProduct(
                product_id=product.id,
                product_name=product.product_name,
                registration_number=product.registration_number,
                active_substances=substances,
                function_category=product.function_category or "Unknown",
                similarity_score=0.0,
                resistance_management_value="unknown",
                relative_efficacy="potentially_similar",
                application_differences=[],
                availability_status="available" if product.is_currently_authorized else "limited",
                user_restrictions=_extract_user_restrictions(product),
                cost_implications=None,

                # Enhanced AI fields
                data_completeness_score=data_completeness,
                confidence_level=_determine_confidence_level(data_completeness, substances),
                ai_recommendation_priority="low",  # Function matches get lower priority
                substance_mode_of_action=_infer_mode_of_action(substances),
                environmental_risk_level=_assess_environmental_risk(product),
                usage_complexity=_assess_usage_complexity(product),
                data_quality_indicators=_get_data_quality_indicators(product, substances),
                fallback_reasoning=[f"Function category match: {function}"]
            )

            alternatives.append(alternative)

    return alternatives


async def _find_emergency_fallback_alternatives(
        session: AsyncSession,
        reference_product: Product,
        availability_status: str,
        quality_tracker: DataQualityTracker
) -> List[AlternativeProduct]:
    """Emergency fallback strategy when few alternatives are found."""

    # Try to find products with similar names (brand families)
    product_name_parts = reference_product.product_name.split()

    if not product_name_parts:
        return []

    # Search for products with similar name components
    first_part = product_name_parts[0]

    query = select(Product).options(
        selectinload(Product.substances).selectinload(ProductSubstance.substance)
    ).where(
        and_(
            Product.product_name.ilike(f"%{first_part}%"),
            Product.id != reference_product.id
        )
    )

    if availability_status == "available":
        query = query.where(Product.is_currently_authorized == True)

    result = await session.execute(query.limit(5))
    products = result.scalars().all()

    alternatives = []
    for product in products:
        substances = [ps.substance.name for ps in product.substances if ps.substance]
        data_completeness = _calculate_data_completeness(product, substances)

        quality_tracker.track_product_checked(product.id, data_completeness)

        alternative = AlternativeProduct(
            product_id=product.id,
            product_name=product.product_name,
            registration_number=product.registration_number,
            active_substances=substances,
            function_category=product.function_category or "Unknown",
            similarity_score=0.0,
            resistance_management_value="unknown",
            relative_efficacy="uncertain",
            application_differences=[],
            availability_status="available" if product.is_currently_authorized else "limited",
            user_restrictions=_extract_user_restrictions(product),
            cost_implications=None,

            # Enhanced AI fields
            data_completeness_score=data_completeness,
            confidence_level="low",  # Emergency fallback has low confidence
            ai_recommendation_priority="emergency",
            substance_mode_of_action=_infer_mode_of_action(substances),
            environmental_risk_level=_assess_environmental_risk(product),
            usage_complexity=_assess_usage_complexity(product),
            data_quality_indicators=_get_data_quality_indicators(product, substances),
            fallback_reasoning=["Emergency fallback: similar product name"]
        )

        alternatives.append(alternative)

    return alternatives


# Enhanced helper functions for AI decision support

def _calculate_data_completeness(product: Product, substances: List[str]) -> float:
    """Calculate data completeness score for AI understanding."""
    score = 0.0

    # Product name (required) - 20%
    if product.product_name:
        score += 0.2

    # Function category - 20%
    if product.function_category:
        score += 0.2

    # Active substances - 40%
    if substances:
        score += 0.4

    # Authorization status - 10%
    if product.authorization_status:
        score += 0.1

    # Registration number - 10%
    if product.registration_number:
        score += 0.1

    return min(score, 1.0)


def _determine_confidence_level(data_completeness: float, substances: List[str]) -> str:
    """Determine confidence level for AI decision making."""
    if data_completeness >= 0.9 and substances:
        return "high"
    elif data_completeness >= 0.7:
        return "medium"
    elif data_completeness >= 0.5:
        return "low"
    else:
        return "very_low"


def _infer_mode_of_action(substances: List[str]) -> List[str]:
    """Infer mode of action from substance names for resistance management."""
    modes = []

    for substance in substances:
        substance_lower = substance.lower()

        # Basic mode of action inference (simplified)
        if any(term in substance_lower for term in ["glyphosate", "glufosinate"]):
            modes.append("amino_acid_synthesis_inhibitor")
        elif any(term in substance_lower for term in ["2,4-d", "mcpa", "dicamba"]):
            modes.append("auxin_mimic")
        elif any(term in substance_lower for term in ["atrazine", "simazine"]):
            modes.append("photosystem_inhibitor")
        elif any(term in substance_lower for term in ["acetochlor", "metolachlor"]):
            modes.append("cell_division_inhibitor")
        elif any(term in substance_lower for term in ["copper", "cuivre"]):
            modes.append("multi_site_contact")
        elif any(term in substance_lower for term in ["mancozeb", "thiram"]):
            modes.append("multi_site_protectant")
        else:
            modes.append("unknown_mode")

    return modes if modes else ["unknown_mode"]


def _assess_environmental_risk(product: Product) -> str:
    """Assess environmental risk level from available data."""
    if product.authorized_mentions:
        mentions = product.authorized_mentions.lower()

        if "biologique" in mentions or "biocontrôle" in mentions:
            return "low"
        elif "danger" in mentions or "toxique" in mentions:
            return "high"
        else:
            return "medium"

    return "unknown"


def _assess_usage_complexity(product: Product) -> str:
    """Assess usage complexity for AI guidance."""
    complexity_indicators = 0

    if product.authorized_mentions:
        mentions = product.authorized_mentions.lower()

        if "professionnel" in mentions:
            complexity_indicators += 1
        if "équipement" in mentions:
            complexity_indicators += 1
        if "formation" in mentions:
            complexity_indicators += 1

    if complexity_indicators >= 2:
        return "high"
    elif complexity_indicators == 1:
        return "medium"
    else:
        return "low"


def _get_data_quality_indicators(product: Product, substances: List[str]) -> Dict[str, str]:
    """Get data quality indicators for AI understanding."""
    indicators = {}

    indicators["substance_data"] = "complete" if substances else "missing"
    indicators["function_category"] = "present" if product.function_category else "missing"
    indicators["authorization_status"] = "current" if product.is_currently_authorized else "withdrawn"
    indicators["regulatory_mentions"] = "present" if product.authorized_mentions else "missing"

    return indicators


async def _enhance_alternative_with_ai_features(
        session: AsyncSession,
        reference_product: Product,
        alternative: AlternativeProduct,
        reference_substances: List[ActiveSubstance],
        quality_tracker: DataQualityTracker
):
    """Enhance alternative with AI-specific features."""

    # Calculate enhanced similarity score
    alternative.similarity_score = await _calculate_enhanced_similarity_score(
        session, reference_product, alternative, reference_substances
    )

    # Assess resistance management value
    alternative.resistance_management_value = _assess_enhanced_resistance_management_value(
        reference_substances, alternative.active_substances
    )

    # Update AI recommendation priority based on data quality and relevance
    alternative.ai_recommendation_priority = _determine_ai_priority(
        alternative.data_completeness_score,
        alternative.confidence_level,
        alternative.resistance_management_value,
        alternative.similarity_score
    )


async def _calculate_enhanced_similarity_score(
        session: AsyncSession,
        reference_product: Product,
        alternative: AlternativeProduct,
        reference_substances: List[ActiveSubstance]
) -> float:
    """Enhanced similarity calculation with AI-friendly weighting."""

    score = 0.0

    # Function category similarity (30%)
    if reference_product.function_category and alternative.function_category:
        ref_functions = set(reference_product.function_category.lower().split("|"))
        alt_functions = set(alternative.function_category.lower().split("|"))

        common_functions = ref_functions.intersection(alt_functions)
        total_functions = ref_functions.union(alt_functions)

        if total_functions:
            score += 0.3 * (len(common_functions) / len(total_functions))

    # Active substance similarity (40%)
    ref_substance_names = {sub.name.lower() for sub in reference_substances}
    alt_substance_names = {name.lower() for name in alternative.active_substances}

    if ref_substance_names and alt_substance_names:
        common_substances = ref_substance_names.intersection(alt_substance_names)
        total_substances = ref_substance_names.union(alt_substance_names)

        substance_similarity = len(common_substances) / len(total_substances)
        score += 0.4 * substance_similarity

    # Authorization status bonus (10%)
    if alternative.availability_status == "available":
        score += 0.1

    # Data completeness bonus (20%)
    score += 0.2 * alternative.data_completeness_score

    return min(score, 1.0)


def _assess_enhanced_resistance_management_value(
        reference_substances: List[ActiveSubstance],
        alternative_substances: List[str]
) -> str:
    """Enhanced resistance management assessment."""

    if not reference_substances or not alternative_substances:
        return "uncertain_insufficient_data"

    ref_names = {sub.name.lower() for sub in reference_substances}
    alt_names = {name.lower() for name in alternative_substances}

    common_substances = ref_names.intersection(alt_names)

    if not common_substances:
        return "excellent"
    elif len(common_substances) < len(ref_names) / 2:
        return "good"
    elif len(common_substances) < len(ref_names):
        return "fair"
    else:
        return "poor"


def _determine_ai_priority(
        data_completeness: float,
        confidence_level: str,
        resistance_value: str,
        similarity_score: float
) -> str:
    """Determine AI recommendation priority based on multiple factors."""

    # Base priority on resistance management value
    if resistance_value == "excellent" and data_completeness > 0.7:
        return "high"
    elif resistance_value in ["good", "fair"] and confidence_level in ["high", "medium"]:
        return "medium"
    elif data_completeness < 0.5 or confidence_level == "very_low":
        return "low"
    else:
        return "medium"


def _sort_alternatives_for_ai_agent(
        alternatives: List[AlternativeProduct],
        same_active_substance: bool,
        resistance_management: bool
) -> List[AlternativeProduct]:
    """Sort alternatives optimized for AI agent decision making."""

    if same_active_substance:
        # For same substance search, prioritize similarity and data quality
        alternatives.sort(key=lambda x: (
            x.similarity_score,
            x.data_completeness_score,
            x.confidence_level == "high",
            x.availability_status == "available"
        ), reverse=True)
    else:
        # For resistance management, prioritize diversity and data quality
        alternatives.sort(key=lambda x: (
            x.resistance_management_value == "excellent",
            x.resistance_management_value == "good",
            x.data_completeness_score,
            x.similarity_score,
            x.confidence_level == "high"
        ), reverse=True)

    return alternatives


async def _generate_enhanced_resistance_strategy(
        session: AsyncSession,
        reference_substances: List[ActiveSubstance],
        alternatives: List[AlternativeProduct],
        quality_tracker: DataQualityTracker
) -> ResistanceStrategy:
    """Generate enhanced resistance strategy with AI guidance."""

    # Categorize alternatives by resistance value
    excellent_alternatives = [alt for alt in alternatives if alt.resistance_management_value == "excellent"]
    good_alternatives = [alt for alt in alternatives if alt.resistance_management_value == "good"]

    # Create rotation sequence with data quality awareness
    sequence = []
    if reference_substances:
        sequence.append(reference_substances[0].name)

    # Add diverse alternatives (prioritize high data quality)
    high_quality_excellent = [
        alt for alt in excellent_alternatives
        if alt.data_completeness_score > 0.7 and alt.active_substances
    ]

    for alt in high_quality_excellent[:2]:
        if alt.active_substances:
            sequence.append(alt.active_substances[0])

    # Determine strategy type with confidence assessment
    if len(high_quality_excellent) >= 2:
        strategy_type = "full_rotation"
        effectiveness = "high"
        difficulty = "moderate"
        confidence = "high"
        risk_assessment = "low"
    elif len(excellent_alternatives) + len(good_alternatives) >= 2:
        strategy_type = "partial_rotation"
        effectiveness = "moderate"
        difficulty = "low"
        confidence = "medium"
        risk_assessment = "medium"
    else:
        strategy_type = "limited_options"
        effectiveness = "low"
        difficulty = "high"
        confidence = "low"
        risk_assessment = "high"

    # Enhanced timing suggestions
    timing_suggestions = [
        "Rotate between different modes of action each growing season",
        "Monitor for early resistance development signs",
        "Avoid consecutive applications of same active substance family"
    ]

    if len(sequence) >= 3:
        timing_suggestions.append("Implement 3-way rotation for maximum resistance prevention")

    # Data limitations assessment
    data_limitations = []
    if quality_tracker.missing_substance_count > 0:
        data_limitations.append(f"{quality_tracker.missing_substance_count} products missing active substance data")

    if quality_tracker.get_overall_completeness() < 0.7:
        data_limitations.append("Limited data completeness may affect strategy accuracy")

    # Alternative strategies
    alternative_strategies = []
    if strategy_type == "limited_options":
        alternative_strategies.extend([
            "Consider tank mixing with compatible products",
            "Implement integrated pest management (IPM) approaches",
            "Explore biological control options",
            "Consult local agricultural advisor for additional options"
        ])

    # Success probability assessment
    if confidence == "high" and len(high_quality_excellent) >= 3:
        success_probability = "high"
    elif confidence == "medium" and len(alternatives) >= 2:
        success_probability = "medium"
    else:
        success_probability = "low"

    return ResistanceStrategy(
        strategy_type=strategy_type,
        recommended_sequence=sequence,
        timing_suggestions=timing_suggestions,
        rotation_period="seasonal" if len(sequence) >= 2 else "not_applicable",
        effectiveness_rating=effectiveness,
        implementation_difficulty=difficulty,

        # Enhanced AI guidance fields
        confidence_in_strategy=confidence,
        data_limitations=data_limitations,
        risk_assessment=risk_assessment,
        alternative_strategies=alternative_strategies,
        success_probability=success_probability
    )


async def _analyze_alternatives_for_ai_agent(
        session: AsyncSession,
        reference_product: Product,
        alternatives: List[AlternativeProduct],
        quality_tracker: DataQualityTracker
) -> AlternativeAnalysis:
    """Enhanced analysis for AI agent understanding."""

    # Count categories
    categories = set()
    for alt in alternatives:
        if alt.function_category:
            categories.update(alt.function_category.split("|"))

    # Calculate resistance diversity with quality weighting
    high_quality_alternatives = [alt for alt in alternatives if alt.data_completeness_score > 0.5]
    substance_diversity = len(set(
        substance for alt in high_quality_alternatives
        for substance in alt.active_substances
    ))
    max_possible_diversity = len(high_quality_alternatives) if high_quality_alternatives else 1
    diversity_score = min(substance_diversity / max_possible_diversity, 1.0)

    # Enhanced gap identification
    gaps = []
    if not alternatives:
        gaps.append("No alternatives found for specified criteria")
    elif len(alternatives) < 3:
        gaps.append("Limited alternative options available")

    if diversity_score < 0.5:
        gaps.append("Limited chemical diversity in alternatives")

    # Check for different efficacy levels
    excellent_alternatives = [alt for alt in alternatives if alt.resistance_management_value == "excellent"]
    if not excellent_alternatives:
        gaps.append("No alternatives with completely different modes of action found")

    # Critical missing data assessment
    critical_missing_data = []
    missing_substance_rate = quality_tracker.missing_substance_count / max(quality_tracker.total_products_checked, 1)
    if missing_substance_rate > 0.5:
        critical_missing_data.append("High rate of missing active substance data")

    if len([alt for alt in alternatives if alt.confidence_level == "very_low"]) > len(alternatives) * 0.3:
        critical_missing_data.append("Many alternatives have very low data confidence")

    # AI confidence indicators
    ai_confidence_indicators = {
        "data_reliability": "high" if quality_tracker.get_overall_completeness() > 0.8 else
        "medium" if quality_tracker.get_overall_completeness() > 0.5 else "low",
        "search_coverage": "comprehensive" if len(alternatives) >= 10 else
        "adequate" if len(alternatives) >= 5 else "limited",
        "substance_diversity": "high" if diversity_score > 0.7 else
        "medium" if diversity_score > 0.4 else "low"
    }

    # Decision complexity assessment
    if len(alternatives) > 10 and len(excellent_alternatives) > 5:
        decision_complexity = "low"  # Many good options
    elif len(alternatives) > 5 and len(excellent_alternatives) > 2:
        decision_complexity = "medium"
    else:
        decision_complexity = "high"  # Few options, difficult choice

    # Recommendation certainty
    if (quality_tracker.get_overall_completeness() > 0.8 and
            len(excellent_alternatives) > 3 and
            diversity_score > 0.6):
        recommendation_certainty = "high"
    elif quality_tracker.get_overall_completeness() > 0.6 and len(alternatives) > 3:
        recommendation_certainty = "medium"
    else:
        recommendation_certainty = "low"

    return AlternativeAnalysis(
        total_alternatives_found=len(alternatives),
        categories_covered=list(categories),
        resistance_diversity_score=diversity_score,
        coverage_gaps=gaps,
        seasonal_considerations=[
            "Consider weather conditions for application timing",
            "Rotate products between growing seasons",
            "Plan applications around crop growth stages",
            "Account for pre-harvest intervals in rotation planning"
        ],
        regional_availability={"france": len(alternatives)},

        # Enhanced AI understanding fields
        data_reliability_score=quality_tracker.get_overall_completeness(),
        search_completeness="comprehensive" if len(alternatives) >= 10 else
        "adequate" if len(alternatives) >= 5 else "limited",
        critical_missing_data=critical_missing_data,
        ai_confidence_indicators=ai_confidence_indicators,
        decision_complexity=decision_complexity,
        recommendation_certainty=recommendation_certainty
    )


def _generate_ai_optimized_recommendations(
        reference_product: Product,
        alternatives: List[AlternativeProduct],
        strategy: ResistanceStrategy,
        analysis: AlternativeAnalysis,
        quality_tracker: DataQualityTracker
) -> List[str]:
    """Generate AI-optimized recommendations with actionable insights."""

    recommendations = []

    # Primary recommendations based on data quality and alternatives
    if not alternatives:
        recommendations.extend([
            "🔍 IMMEDIATE ACTION: Broaden search criteria (consider different crops, targets, or application methods)",
            "📞 CONSULT EXPERT: Contact local agricultural advisor for specialized alternatives",
            "🌐 CHECK UPDATES: Verify newly authorized products in regulatory database",
            "🛡️ PREVENTIVE FOCUS: Emphasize preventive measures and integrated pest management"
        ])
    elif analysis.recommendation_certainty == "high":
        recommendations.extend([
            "✅ EXCELLENT OPTIONS: Multiple high-quality alternatives available",
            "🎯 IMPLEMENT ROTATION: Strong resistance management strategy recommended",
            f"🌟 TOP PRIORITY: Focus on {len([alt for alt in alternatives if alt.ai_recommendation_priority == 'high'])} highest-priority alternatives"
        ])
    elif analysis.recommendation_certainty == "medium":
        recommendations.extend([
            "⚠️ MODERATE OPTIONS: Limited but viable alternatives found",
            "🔄 CAREFUL PLANNING: Plan resistance management carefully with available options",
            "📊 VERIFY DATA: Double-check product authorizations before use"
        ])
    else:
        recommendations.extend([
            "⚠️ LIMITED OPTIONS: Few alternatives found - use caution",
            "🛡️ ENHANCE IPM: Strengthen integrated pest management practices",
            "📞 SEEK GUIDANCE: Consult agricultural advisor for additional strategies"
        ])

    # Strategy-specific recommendations with confidence indicators
    if strategy.confidence_in_strategy == "high":
        if strategy.strategy_type == "full_rotation":
            recommendations.append("🎯 OPTIMAL STRATEGY: Full rotation plan with high success probability")
        elif strategy.strategy_type == "partial_rotation":
            recommendations.append("🔄 GOOD STRATEGY: Partial rotation feasible with available options")
    else:
        recommendations.append("⚠️ STRATEGY LIMITATION: Limited confidence in resistance management strategy")

    # Data quality-based recommendations
    if quality_tracker.missing_substance_count > 0:
        recommendations.append(
            f"📊 DATA ALERT: {quality_tracker.missing_substance_count} products missing active substance data - verify before use")

    if quality_tracker.get_overall_completeness() < 0.6:
        recommendations.append("🔍 DATA VERIFICATION: Low data completeness - independently verify product information")

    # Quality-based product recommendations
    high_confidence_alternatives = [alt for alt in alternatives if alt.confidence_level == "high"]
    if high_confidence_alternatives:
        recommendations.append(
            f"🌟 HIGH CONFIDENCE: {len(high_confidence_alternatives)} alternatives have high data confidence")

    # Emergency vs optimal recommendations
    emergency_alternatives = [alt for alt in alternatives if alt.ai_recommendation_priority == "emergency"]
    if emergency_alternatives:
        recommendations.append(
            f"🚨 EMERGENCY OPTIONS: {len(emergency_alternatives)} emergency alternatives (use as last resort)")

    return recommendations


def _identify_limitations_with_solutions(
        crop: Optional[Crop],
        target: Optional[Target],
        alternatives: List[AlternativeProduct],
        quality_tracker: DataQualityTracker
) -> List[str]:
    """Enhanced limitations identification with actionable solutions."""

    limitations = []

    # Search scope limitations with solutions
    if not crop:
        limitations.append(
            "🌾 SCOPE LIMITATION: No specific crop provided → SOLUTION: Specify crop for targeted recommendations")

    if not target:
        limitations.append(
            "🎯 SCOPE LIMITATION: No specific target provided → SOLUTION: Identify pest/disease for precise alternatives")

    # Data quality limitations with impacts
    data_quality_issues = []
    if quality_tracker.missing_substance_count > 0:
        data_quality_issues.append(
            f"💊 SUBSTANCE DATA: {quality_tracker.missing_substance_count} products missing active substance information")

    if quality_tracker.get_overall_completeness() < 0.7:
        data_quality_issues.append("📊 COMPLETENESS: Overall data completeness below optimal threshold")

    if data_quality_issues:
        limitations.extend(data_quality_issues)
        limitations.append("🔧 SOLUTION: Independently verify product labels and regulatory status")

    # System limitations with workarounds
    limitations.extend([
        "💰 COST DATA: Economic comparison not available → SOLUTION: Consult local suppliers for pricing",
        "🧪 MODE OF ACTION: Inferred from substance names → SOLUTION: Verify with product technical data sheets",
        "📍 REGIONAL AVAILABILITY: May vary by location → SOLUTION: Check with local distributors",
        "⏰ SEASONAL TIMING: Specific timing not analyzed → SOLUTION: Consult product labels for application windows"
    ])

    # Alternative-specific limitations
    if alternatives:
        low_confidence_count = len([alt for alt in alternatives if alt.confidence_level in ["low", "very_low"]])
        if low_confidence_count > 0:
            limitations.append(
                f"📊 CONFIDENCE: {low_confidence_count} alternatives have low data confidence → SOLUTION: Prioritize high-confidence options")

        emergency_count = len([alt for alt in alternatives if alt.ai_recommendation_priority == "emergency"])
        if emergency_count > 0:
            limitations.append(
                f"🚨 EMERGENCY OPTIONS: {emergency_count} emergency fallback alternatives → SOLUTION: Use only if no better options available")

    # Regulatory limitations with compliance guidance
    limitations.extend([
        "⚖️ REGULATORY: Authorization status may change → SOLUTION: Verify current status before purchase",
        "🏛️ LOCAL RULES: Additional local restrictions may apply → SOLUTION: Check regional agricultural regulations",
        "📋 LABEL COMPLIANCE: Always verify authorized uses → SOLUTION: Read complete product labels before application"
    ])

    return limitations


def _create_ai_decision_support(
        reference_product: Product,
        alternatives: List[AlternativeProduct],
        resistance_strategy: ResistanceStrategy,
        quality_tracker: DataQualityTracker
) -> Dict[str, Any]:
    """Create comprehensive AI decision support summary."""

    # Categorize alternatives by AI recommendation priority
    priority_breakdown = {
        "high": len([alt for alt in alternatives if alt.ai_recommendation_priority == "high"]),
        "medium": len([alt for alt in alternatives if alt.ai_recommendation_priority == "medium"]),
        "low": len([alt for alt in alternatives if alt.ai_recommendation_priority == "low"]),
        "emergency": len([alt for alt in alternatives if alt.ai_recommendation_priority == "emergency"])
    }

    # Confidence level distribution
    confidence_breakdown = {
        "high": len([alt for alt in alternatives if alt.confidence_level == "high"]),
        "medium": len([alt for alt in alternatives if alt.confidence_level == "medium"]),
        "low": len([alt for alt in alternatives if alt.confidence_level == "low"]),
        "very_low": len([alt for alt in alternatives if alt.confidence_level == "very_low"])
    }

    # Resistance management effectiveness
    resistance_breakdown = {
        "excellent": len([alt for alt in alternatives if alt.resistance_management_value == "excellent"]),
        "good": len([alt for alt in alternatives if alt.resistance_management_value == "good"]),
        "fair": len([alt for alt in alternatives if alt.resistance_management_value == "fair"]),
        "poor": len([alt for alt in alternatives if alt.resistance_management_value == "poor"])
    }

    # Decision complexity assessment
    decision_factors = {
        "total_options": len(alternatives),
        "high_quality_options": len([alt for alt in alternatives if alt.data_completeness_score > 0.7]),
        "resistance_diverse_options": resistance_breakdown["excellent"] + resistance_breakdown["good"],
        "data_reliability": quality_tracker.get_overall_completeness()
    }

    # AI recommendation summary
    if priority_breakdown["high"] >= 3 and confidence_breakdown["high"] >= 3:
        ai_recommendation = "Multiple excellent alternatives available - straightforward decision"
    elif priority_breakdown["high"] + priority_breakdown["medium"] >= 5:
        ai_recommendation = "Several good alternatives available - moderate complexity decision"
    elif priority_breakdown["emergency"] > 0:
        ai_recommendation = "Limited options with emergency alternatives - complex decision requiring expert consultation"
    else:
        ai_recommendation = "Few alternatives available - challenging decision requiring careful evaluation"

    return {
        "ai_recommendation_summary": ai_recommendation,
        "priority_breakdown": priority_breakdown,
        "confidence_breakdown": confidence_breakdown,
        "resistance_effectiveness": resistance_breakdown,
        "decision_complexity_factors": decision_factors,
        "strategy_confidence": resistance_strategy.confidence_in_strategy,
        "recommended_approach": _determine_recommended_approach(priority_breakdown, confidence_breakdown,
                                                                resistance_strategy)
    }


def _determine_recommended_approach(
        priority_breakdown: Dict[str, int],
        confidence_breakdown: Dict[str, int],
        resistance_strategy: ResistanceStrategy
) -> str:
    """Determine the recommended approach for the AI agent."""

    if priority_breakdown["high"] >= 3 and confidence_breakdown["high"] >= 2:
        return "proceed_with_top_alternatives"
    elif priority_breakdown["high"] + priority_breakdown["medium"] >= 3:
        return "evaluate_medium_priority_options"
    elif priority_breakdown["emergency"] > 0:
        return "seek_expert_consultation"
    else:
        return "expand_search_criteria"


def _assess_overall_confidence(
        alternatives: List[AlternativeProduct],
        resistance_strategy: ResistanceStrategy,
        quality_tracker: DataQualityTracker
) -> Dict[str, str]:
    """Assess overall confidence in recommendations."""

    # Alternative quality confidence
    high_quality_count = len([alt for alt in alternatives if alt.confidence_level == "high"])
    if high_quality_count >= 3:
        alternative_confidence = "high"
    elif high_quality_count >= 1:
        alternative_confidence = "medium"
    else:
        alternative_confidence = "low"

    # Data completeness confidence
    if quality_tracker.get_overall_completeness() > 0.8:
        data_confidence = "high"
    elif quality_tracker.get_overall_completeness() > 0.6:
        data_confidence = "medium"
    else:
        data_confidence = "low"

    # Overall assessment
    if (alternative_confidence == "high" and
            data_confidence == "high" and
            resistance_strategy.confidence_in_strategy == "high"):
        overall_confidence = "high"
    elif (alternative_confidence in ["medium", "high"] and
          data_confidence in ["medium", "high"]):
        overall_confidence = "medium"
    else:
        overall_confidence = "low"

    return {
        "alternative_quality": alternative_confidence,
        "data_completeness": data_confidence,
        "resistance_strategy": resistance_strategy.confidence_in_strategy,
        "overall_assessment": overall_confidence
    }


def _generate_next_steps_guidance(
        alternatives: List[AlternativeProduct],
        resistance_strategy: ResistanceStrategy,
        quality_tracker: DataQualityTracker
) -> List[str]:
    """Generate next steps guidance for AI agent."""

    guidance = []

    # Immediate next steps based on alternatives found
    if not alternatives:
        guidance.extend([
            "1. EXPAND SEARCH: Try broader search criteria (different crops/targets)",
            "2. CONSULT EXPERT: Contact agricultural advisor for specialized recommendations",
            "3. REVIEW IPM: Focus on integrated pest management strategies"
        ])
    else:
        high_priority = [alt for alt in alternatives if alt.ai_recommendation_priority == "high"]

        if high_priority:
            guidance.extend([
                f"1. EVALUATE TOP {len(high_priority)}: Focus on high-priority alternatives first",
                "2. VERIFY AUTHORIZATION: Confirm current regulatory status",
                "3. CHECK AVAILABILITY: Contact suppliers for product availability"
            ])
        else:
            guidance.extend([
                "1. CAREFUL EVALUATION: Review medium/low priority alternatives thoroughly",
                "2. SEEK VALIDATION: Get expert opinion on selected alternatives",
                "3. CONSIDER COMBINATIONS: Explore tank mixing or rotation strategies"
            ])

    # Strategy implementation guidance
    if resistance_strategy.confidence_in_strategy == "high":
        guidance.append("4. IMPLEMENT ROTATION: Execute resistance management strategy as recommended")
    else:
        guidance.append("4. ADAPT STRATEGY: Modify resistance management approach based on local conditions")

    # Data quality follow-up
    if quality_tracker.missing_substance_count > 0:
        guidance.append("5. DATA VERIFICATION: Independently verify missing active substance information")

    # Final validation step
    guidance.append("6. EXPERT REVIEW: Have local agricultural expert review final selection")

    return guidance


def _extract_user_restrictions(product: Product) -> List[str]:
    """Extract user type restrictions from product data."""
    restrictions = []

    if product.authorized_mentions:
        mentions = product.authorized_mentions.lower()

        if "amateur" in mentions or "jardin" in mentions:
            restrictions.append("Amateur use authorized")
        elif "professionnel" in mentions:
            restrictions.append("Professional use only")
        else:
            restrictions.append("Check user authorization requirements")

        if "biologique" in mentions:
            restrictions.append("Organic farming approved")

        if "biocontrôle" in mentions:
            restrictions.append("Biocontrol product")

    return restrictions


def _create_enhanced_no_reference_response(
        product_id: int,
        quality_tracker: DataQualityTracker
) -> AlternativeProductsResponse:
    """Create enhanced response when reference product not found."""

    return AlternativeProductsResponse(
        reference_product={
            "product_id": product_id,
            "error": "Reference product not found",
            "suggested_actions": [
                "Verify product ID accuracy",
                "Search by product name instead",
                "Check if product has been withdrawn"
            ]
        },
        alternatives=[],
        resistance_strategy=ResistanceStrategy(
            strategy_type="none",
            recommended_sequence=[],
            timing_suggestions=[],
            rotation_period="not_applicable",
            effectiveness_rating="none",
            implementation_difficulty="none",
            confidence_in_strategy="none",
            data_limitations=["Reference product not found"],
            risk_assessment="cannot_assess",
            alternative_strategies=["Find valid reference product first"],
            success_probability="none"
        ),
        analysis=AlternativeAnalysis(
            total_alternatives_found=0,
            categories_covered=[],
            resistance_diversity_score=0.0,
            coverage_gaps=["Reference product not found in database"],
            seasonal_considerations=[],
            regional_availability={},
            data_reliability_score=0.0,
            search_completeness="failed",
            critical_missing_data=["Reference product not available"],
            ai_confidence_indicators={"error": "reference_not_found"},
            decision_complexity="cannot_assess",
            recommendation_certainty="none"
        ),
        recommendations=[
            "🔍 VERIFY ID: Double-check the reference product ID",
            "📞 CONSULT ADVISOR: Contact agricultural advisor for product identification",
            "🌐 SEARCH BY NAME: Try searching by product name or registration number",
            "📋 CHECK STATUS: Verify if product is still authorized"
        ],
        limitations=[
            "❌ CANNOT PROCEED: Invalid reference product prevents analysis",
            "🔍 PRODUCT IDENTIFICATION: May be withdrawn, renamed, or have different ID",
            "📊 NO DATA AVAILABLE: Cannot assess alternatives without valid reference"
        ],
        search_metadata={
            "search_criteria": {"error": "invalid_reference_product"},
            "alternatives_found": 0,
            "search_confidence": 0.0,
            "fallback_strategies_used": 0,
            "data_completeness": 0.0
        },
        ai_decision_support={
            "ai_recommendation_summary": "Cannot provide recommendations without valid reference product",
            "priority_breakdown": {"error": 1},
            "confidence_breakdown": {"none": 1},
            "resistance_effectiveness": {"cannot_assess": 1},
            "decision_complexity_factors": {"error": "no_reference_product"},
            "strategy_confidence": "none",
            "recommended_approach": "find_valid_reference_product"
        },
        data_quality_summary=quality_tracker.get_summary(),
        fallback_explanations=["Reference product lookup failed"],
        confidence_assessment={
            "alternative_quality": "none",
            "data_completeness": "none",
            "resistance_strategy": "none",
            "overall_assessment": "cannot_assess"
        },
        next_steps_guidance=[
            "1. VERIFY PRODUCT: Confirm correct product ID or name",
            "2. CHECK DATABASE: Ensure product exists in current database",
            "3. CONTACT SUPPORT: Get assistance with product identification",
            "4. ALTERNATIVE SEARCH: Try searching for similar products by name"
        ]
    )
