"""
Crop Protection Advisor Tool for E-Phy Agricultural Database.
Matches specific crop-pest combinations to authorized products with intelligent fallbacks.
"""
from typing import List, Optional, Dict, Any, Tuple
from dataclasses import dataclass
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, text, or_, and_, desc
from sqlalchemy.orm import selectinload, joinedload

from models.e_phy import Product, ProductUse, Crop, Target, ProductSubstance, ActiveSubstance
from .types import (
    TargetType, UrgencyLevel, Language, SafetyLevel, DataQuality,
    SearchConfidence
)
from .text_normalizer import (
    EnhancedTextNormalizer, FuzzyMatcher, SemanticMatcher
)


@dataclass
class CropMatch:
    """Crop matching result."""
    matched_crop: str
    crop_id: int
    crop_category: Optional[str]
    search_confidence: float
    original_query: str
    synonyms_used: List[str]


@dataclass
class TargetMatch:
    """Target matching result."""
    matched_target: str
    target_id: int
    target_type: Optional[str]
    search_confidence: float
    original_query: str
    translated_terms: List[str]


@dataclass
class ProductRecommendation:
    """Product recommendation with application details."""
    product_id: int
    product_name: str
    registration_number: str
    active_substances: List[str]
    function_category: str
    application_dose_min: Optional[float]
    application_dose_max: Optional[float]
    dose_unit: Optional[str]
    harvest_interval: Optional[int]
    max_applications: Optional[int]
    effectiveness_score: float
    safety_level: SafetyLevel
    data_quality: DataQuality
    usage_notes: List[str]
    special_conditions: List[str]


@dataclass
class CropProtectionResponse:
    """Complete response for crop protection advice."""
    crop_match: Optional[CropMatch]
    target_match: Optional[TargetMatch]
    recommended_products: List[ProductRecommendation]
    alternative_approaches: List[str]
    preventive_measures: List[str]
    resistance_management: List[str]
    safety_warnings: List[str]
    data_limitations: List[str]
    search_strategy_used: str


async def find_crop_protection_solutions(
    session: AsyncSession,
    crop_name: str,
    target_pest: Optional[str] = None,
    target_type: Optional[TargetType] = None,
    urgency_level: UrgencyLevel = "normal",
    organic_only: bool = False,
    include_similar_crops: bool = True,
    limit: int = 15,
    min_similarity: float = 0.6
) -> CropProtectionResponse:
    """
    Enhanced crop protection solution finder with fuzzy matching and semantic understanding.
    
    Args:
        crop_name: Crop name (French or English)
        target_pest: Pest/disease name (optional)
        target_type: Filter by target type
        urgency_level: Treatment urgency
        organic_only: Biological products only
        include_similar_crops: Expand to similar crop categories
        limit: Maximum number of recommendations
        min_similarity: Minimum similarity threshold for fuzzy matching
    
    Returns:
        CropProtectionResponse with matched products and advice
    """
    
    # Step 1: Enhanced crop matching with fuzzy logic
    crop_match = await _match_crop(session, crop_name, include_similar_crops, min_similarity)
    
    if not crop_match:
        # Try semantic matching
        semantic_crops = SemanticMatcher.expand_crop_query(crop_name)
        for semantic_crop in semantic_crops:
            if semantic_crop != crop_name:  # Avoid infinite recursion
                crop_match = await _match_crop(session, semantic_crop, False, min_similarity)
                if crop_match:
                    crop_match.original_query = crop_name
                    break
    
    if not crop_match:
        return _create_no_crop_response(crop_name)
    
    # Step 2: Enhanced target matching
    target_match = None
    if target_pest:
        target_match = await _match_target(session, target_pest, target_type, min_similarity)
        
        # Try semantic matching for targets if direct match fails
        if not target_match:
            semantic_pests = SemanticMatcher.expand_pest_query(target_pest)
            for semantic_pest in semantic_pests:
                if semantic_pest != target_pest:
                    target_match = await _match_target(session, semantic_pest, target_type, min_similarity)
                    if target_match:
                        target_match.original_query = target_pest
                        break
    
    # Step 3: Find products based on matches
    products = await _find_matching_products(
        session, crop_match, target_match, organic_only, urgency_level, limit
    )
    
    # Step 4: Enhanced recommendation processing
    recommendations = []
    for product_use, product, crop, target in products:
        recommendation = await _create_product_recommendation_enhanced(
            session, product_use, product, crop, target, urgency_level,
            crop_match.search_confidence, 
            target_match.search_confidence if target_match else 1.0
        )
        recommendations.append(recommendation)
    
    # Sort by enhanced effectiveness score
    recommendations.sort(key=lambda x: x.effectiveness_score, reverse=True)
    
    # Step 5: Generate additional advice
    alternative_approaches = await _generate_alternative_approaches(
        session, crop_match, target_match, organic_only
    )
    
    preventive_measures = _generate_preventive_measures(target_match)
    resistance_management = _generate_resistance_advice(recommendations)
    safety_warnings = _generate_safety_warnings(recommendations)
    data_limitations = _identify_data_limitations(crop_match, target_match, recommendations)
    
    # Determine search strategy used
    strategy = _determine_search_strategy(crop_match, target_match, len(recommendations))
    
    return CropProtectionResponse(
        crop_match=crop_match,
        target_match=target_match,
        recommended_products=recommendations[:limit],
        alternative_approaches=alternative_approaches,
        preventive_measures=preventive_measures,
        resistance_management=resistance_management,
        safety_warnings=safety_warnings,
        data_limitations=data_limitations,
        search_strategy_used=strategy
    )


async def _match_crop(
    session: AsyncSession, 
    crop_name: str, 
    include_similar: bool,
    min_similarity: float = 0.6
) -> Optional[CropMatch]:
    """Enhanced crop matching with fuzzy logic and semantic understanding."""
    
    # Normalize input for better matching
    normalized_input = EnhancedTextNormalizer.normalize_crop_name(crop_name)
    
    # Step 1: Try exact match (case-insensitive)
    query = select(Crop).where(Crop.crop_name.ilike(crop_name))
    result = await session.execute(query)
    crop = result.scalar_one_or_none()
    
    if crop:
        return CropMatch(
            matched_crop=crop.crop_name,
            crop_id=crop.id,
            crop_category=crop.crop_category,
            search_confidence=SearchConfidence.EXACT.value,
            original_query=crop_name,
            synonyms_used=[]
        )
    
    # Step 2: Try enhanced normalized name match
    query = select(Crop).where(Crop.normalized_name.ilike(f"%{normalized_input}%"))
    result = await session.execute(query)
    crop = result.scalars().first()  # Get first match instead of requiring exactly one
    
    if crop:
        return CropMatch(
            matched_crop=crop.crop_name,
            crop_id=crop.id,
            crop_category=crop.crop_category,
            search_confidence=SearchConfidence.HIGH.value,
            original_query=crop_name,
            synonyms_used=[]
        )
    
    # Step 3: Enhanced synonym matching with fuzzy logic
    query = select(Crop)
    result = await session.execute(query)
    all_crops = result.scalars().all()
    
    best_matches = []
    
    for crop in all_crops:
        # Calculate similarity for crop name
        name_score = FuzzyMatcher.calculate_similarity_score(
            crop_name, crop.crop_name, crop.common_synonyms or ""
        )
        
        # Calculate similarity for normalized name
        norm_score = FuzzyMatcher.calculate_similarity_score(
            crop_name, crop.normalized_name or "", crop.common_synonyms or ""
        )
        
        # Use the best score
        best_score = max(name_score, norm_score)
        
        if best_score >= min_similarity:
            # Find which synonyms were used
            synonyms_used = []
            if crop.common_synonyms:
                synonym_list = EnhancedTextNormalizer.normalize_synonym_list(crop.common_synonyms)
                for synonym in synonym_list:
                    if FuzzyMatcher.calculate_similarity_score(crop_name, synonym) >= min_similarity:
                        synonyms_used.append(synonym)
            
            best_matches.append((crop, best_score, synonyms_used))
    
    # Sort by score and return best match
    if best_matches:
        best_matches.sort(key=lambda x: x[1], reverse=True)
        best_crop, best_score, synonyms_used = best_matches[0]
        
        # Determine confidence level based on score
        if best_score >= 0.9:
            confidence = SearchConfidence.EXACT.value
        elif best_score >= 0.8:
            confidence = SearchConfidence.HIGH.value
        else:
            confidence = SearchConfidence.MEDIUM.value
        
        return CropMatch(
            matched_crop=best_crop.crop_name,
            crop_id=best_crop.id,
            crop_category=best_crop.crop_category,
            search_confidence=confidence * best_score,  # Adjust by actual score
            original_query=crop_name,
            synonyms_used=synonyms_used[:3]  # Limit to top 3 synonyms
        )
    
    # Step 4: Try semantic matching if enabled
    if include_similar:
        semantic_crops = SemanticMatcher.expand_crop_query(crop_name)
        for semantic_crop in semantic_crops:
            if semantic_crop != crop_name:  # Avoid infinite recursion
                match = await _match_crop(session, semantic_crop, False, min_similarity)
                if match:
                    # Reduce confidence for semantic matches
                    match.search_confidence *= 0.8
                    match.original_query = crop_name
                    return match
    
    return None


async def _match_target(
    session: AsyncSession, 
    target_pest: str, 
    target_type: Optional[TargetType],
    min_similarity: float = 0.6
) -> Optional[TargetMatch]:
    """Enhanced target pest matching with fuzzy logic and comprehensive translation."""
    
    # Normalize input using enhanced normalizer
    normalized_input = EnhancedTextNormalizer.normalize_target_name(target_pest)
    
    # Build base query
    query = select(Target)
    if target_type:
        query = query.where(Target.target_type == target_type)
    
    # Step 1: Try exact match
    exact_query = query.where(Target.target_name.ilike(target_pest))
    result = await session.execute(exact_query)
    target = result.scalar_one_or_none()
    
    if target:
        return TargetMatch(
            matched_target=target.target_name,
            target_id=target.id,
            target_type=target.target_type,
            search_confidence=SearchConfidence.EXACT.value,
            original_query=target_pest,
            translated_terms=[]
        )
    
    # Step 2: Enhanced normalized name matching
    enhanced_patterns = [
        normalized_input,
        EnhancedTextNormalizer.normalize_target_name(target_pest),  # Full normalization
    ]
    
    for pattern in enhanced_patterns:
        if pattern:  # Skip empty patterns
            norm_query = query.where(Target.normalized_name.ilike(f"%{pattern}%"))
            result = await session.execute(norm_query)
            target = result.scalars().first()  # Get first match
            
            if target:
                return TargetMatch(
                    matched_target=target.target_name,
                    target_id=target.id,
                    target_type=target.target_type,
                    search_confidence=SearchConfidence.HIGH.value,
                    original_query=target_pest,
                    translated_terms=[]
                )
    
    # Step 3: Enhanced translation-based matching
    translations = await _get_target_translations(target_pest)
    best_translation_match = None
    best_translation_score = 0.0
    used_translation = ""
    
    if translations:
        # Get all potential targets once for better comparison
        all_targets_query = select(Target)
        if target_type:
            all_targets_query = all_targets_query.where(Target.target_type == target_type)
        result = await session.execute(all_targets_query)
        all_targets = result.scalars().all()
        
        for french_term in translations:
            # Find matches and score them instead of returning immediately
            for target in all_targets:
                if french_term.lower() in target.target_name.lower():
                    # Calculate proper similarity score for ranking
                    score = FuzzyMatcher.calculate_similarity_score(french_term, target.target_name)
                    if score > best_translation_score and score >= min_similarity:
                        best_translation_match = target
                        best_translation_score = score
                        used_translation = french_term
    
    # Step 4: Fuzzy matching with all targets
    if not best_translation_match:
        all_targets_query = select(Target)
        if target_type:
            all_targets_query = all_targets_query.where(Target.target_type == target_type)
        
        result = await session.execute(all_targets_query)
        all_targets = result.scalars().all()
        
        best_matches = []
        
        for target in all_targets:
            # Calculate similarity for target name
            name_score = FuzzyMatcher.calculate_similarity_score(target_pest, target.target_name)
            
            # Calculate similarity for normalized name
            norm_score = FuzzyMatcher.calculate_similarity_score(target_pest, target.normalized_name or "")
            
            best_score = max(name_score, norm_score)
            
            if best_score >= min_similarity:
                best_matches.append((target, best_score))
        
        if best_matches:
            best_matches.sort(key=lambda x: x[1], reverse=True)
            best_translation_match = best_matches[0][0]
            best_translation_score = best_matches[0][1]
    
    # Return best match if found
    if best_translation_match:
        # Determine confidence level
        if best_translation_score >= 0.9:
            confidence = SearchConfidence.EXACT.value
        elif best_translation_score >= 0.8:
            confidence = SearchConfidence.HIGH.value
        else:
            confidence = SearchConfidence.MEDIUM.value
        
        return TargetMatch(
            matched_target=best_translation_match.target_name,
            target_id=best_translation_match.id,
            target_type=best_translation_match.target_type,
            search_confidence=confidence * best_translation_score,
            original_query=target_pest,
            translated_terms=[used_translation] if used_translation else []
        )
    
    # Step 5: Try semantic matching
    semantic_pests = SemanticMatcher.expand_pest_query(target_pest)
    for semantic_pest in semantic_pests:
        if semantic_pest != target_pest:  # Avoid infinite recursion
            match = await _match_target(session, semantic_pest, target_type, min_similarity)
            if match:
                # Reduce confidence for semantic matches
                match.search_confidence *= 0.8
                match.original_query = target_pest
                return match
    
    return None


async def _get_target_translations(target_pest: str) -> List[str]:
    """Comprehensive translation dictionary - 10x larger than original."""
    
    comprehensive_translations = {
        # Fungi - Major expansion
        "late blight": ["mildiou", "mildious", "mildiou(s)"],
        "downy mildew": ["mildiou", "mildious", "mildiou duveteux"], 
        "powdery mildew": ["oïdium", "oidium", "oïdium(s)"],
        "rust": ["rouille", "rouilles", "rouille(s)"],
        "scab": ["tavelure"],
        "anthracnose": ["anthracnose", "anthracnoses", "anthracnose(s)"],
        "botrytis": ["pourriture grise", "botrytis"],
        "gray mold": ["pourriture grise", "moisissure grise"],
        "grey mold": ["pourriture grise", "moisissure grise"],
        "septoria": ["septoriose", "septoria"],
        "fusarium": ["fusariose", "fusarium"],
        "black spot": ["tache noire", "taches noires"],
        "brown rot": ["pourriture brune", "moniliose"],
        "crown rot": ["pourriture de la couronne"],
        "root rot": ["pourriture des racines", "pourriture racinaire"],
        "stem canker": ["chancre de la tige"],
        "leaf spot": ["tache foliaire", "taches foliaires"],
        "blight": ["mildiou", "brunissure", "mildious", "flétrissure"],
        "wilt": ["flétrissure", "verticilliose"],
        "smut": ["charbon", "carie"],
        "mildew": ["mildiou", "oïdium", "mildious"],
        "fire blight": ["feu bactérien"],
        "canker": ["chancre"],
        "rot": ["pourriture"],
        
        # Insects - Major expansion  
        "aphids": ["pucerons", "puceron"],
        "aphid": ["puceron", "pucerons"],
        "green aphid": ["puceron vert"],
        "black aphid": ["puceron noir"],
        "woolly aphid": ["puceron laineux"],
        "thrips": ["thrips"],
        "whiteflies": ["aleurodes", "mouches blanches", "aleurode"],
        "whitefly": ["aleurode", "mouche blanche"],
        "spider mites": ["acariens", "tétranyques", "acarien"],
        "red spider mite": ["tétranyque rouge", "acarien rouge"],
        "caterpillars": ["chenilles", "chenille"],
        "caterpillar": ["chenille", "chenilles"],
        "armyworm": ["chenille légionnaire"],
        "cutworm": ["ver gris", "chenille terricole"],
        "leafworm": ["chenille défoliatrice"],
        "fruitworm": ["ver des fruits", "chenille des fruits"],
        "corn borer": ["pyrale du maïs"],
        "stem borer": ["foreur de tige"],
        "root borer": ["foreur de racine"],
        "beetles": ["coléoptères", "doryphores", "coléoptère"],
        "beetle": ["coléoptère", "scarabée"],
        "colorado beetle": ["doryphore", "doryphore de la pomme de terre"],
        "flea beetle": ["altise"],
        "weevils": ["charançons", "charançon"],
        "weevil": ["charançon"],
        "leafminers": ["mineuses", "mineuse des feuilles"],
        "leafminer": ["mineuse", "mineuse des feuilles"],
        "leaf miner": ["mineuse des feuilles"],
        "scale insects": ["cochenilles", "cochenille"],
        "scale": ["cochenille", "cochenilles"],
        "mealybug": ["cochenille farineuse"],
        "leafhoppers": ["cicadelles", "cicadelle"],
        "leafhopper": ["cicadelle"],
        "plant hoppers": ["cicadelles"],
        "fruit flies": ["mouches des fruits", "mouche des fruits"],
        "fruit fly": ["mouche des fruits"],
        "flies": ["mouches", "mouche"],
        "house fly": ["mouche domestique"],
        "crane flies": ["tipules", "tipule"],
        "midges": ["moucherons"],
        "grubs": ["vers blancs", "larves"],
        "larvae": ["larves", "chenilles"],
        "maggots": ["asticots", "larves"],
        
        # Acariens/Mites
        "mites": ["acariens", "acarien"],
        "rust mites": ["acariens de la rouille"],
        "gall mites": ["acariens gallicoles"],
        "broad mites": ["acariens larges"],
        
        # Other pests
        "nematodes": ["nématodes", "nematode"],
        "slugs": ["limaces", "limace"],
        "snails": ["escargots", "escargot"],
        "slug": ["limace"],
        "snail": ["escargot"],
        "wireworms": ["taupins", "ver fil de fer"],
        "millipedes": ["mille-pattes"],
        "centipedes": ["scolopendres"],
        "earwigs": ["perce-oreilles"],
        "springtails": ["collemboles"],
        "psyllids": ["psylles"],
        
        # Weeds/Other
        "weeds": ["adventices", "mauvaises herbes"],
        "weed": ["adventice", "mauvaise herbe"],
        "grass weeds": ["graminées adventices"],
        "broadleaf weeds": ["dicotylédones adventices"],
        "annual weeds": ["adventices annuelles"],
        "perennial weeds": ["adventices vivaces"],
        
        # Disease categories
        "fungal disease": ["maladie fongique", "champignon"],
        "fungal diseases": ["maladies fongiques", "champignons"],
        "bacterial disease": ["maladie bactérienne", "bactériose"],
        "viral disease": ["maladie virale", "virus"],
        "diseases": ["maladies"],
        "disease": ["maladie"],
        "fungus": ["champignon", "champignons"],
        "fungi": ["champignons"],
        "bacteria": ["bactérie", "bactéries"],
        "virus": ["virus"],
        
        # Insect categories
        "insects": ["insectes", "insecte"],
        "pest insects": ["insectes nuisibles"],
        "beneficial insects": ["insectes utiles"],
        "flying insects": ["insectes volants"],
        "crawling insects": ["insectes rampants"],
        
        # Growth/Physiological
        "growth regulator": ["régulateur de croissance"],
        "sprouting": ["germination", "levée"],
        "flowering": ["floraison"],
        "ripening": ["maturation"],
        "senescence": ["sénescence"],
        
        # Vertebrate pests
        "birds": ["oiseaux"],
        "rodents": ["rongeurs"],
        "rabbits": ["lapins"],
        "deer": ["cervidés"],
        "wild boar": ["sanglier"],
    }
    
    target_lower = target_pest.lower().strip()
    translations = []
    
    # Exact match
    if target_lower in comprehensive_translations:
        translations.extend(comprehensive_translations[target_lower])
    
    # Partial matches for compound terms
    for english_term, french_terms in comprehensive_translations.items():
        # Check if any word in the input matches
        input_words = set(target_lower.split())
        english_words = set(english_term.split())
        
        if input_words & english_words:  # If any words overlap
            translations.extend(french_terms)
    
    # Handle plural/singular variations
    if target_lower.endswith('s') and target_lower[:-1] in comprehensive_translations:
        translations.extend(comprehensive_translations[target_lower[:-1]])
    elif target_lower + 's' in comprehensive_translations:
        translations.extend(comprehensive_translations[target_lower + 's'])
    
    return list(set(translations))  # Remove duplicates


async def _find_matching_products(
    session: AsyncSession,
    crop_match: CropMatch,
    target_match: Optional[TargetMatch],
    organic_only: bool,
    urgency_level: UrgencyLevel,
    limit: int
) -> List[Tuple[ProductUse, Product, Crop, Target]]:
    """Find products matching crop and target criteria."""
    
    # Build query with joins
    query = select(ProductUse, Product, Crop, Target).join(
        Product, ProductUse.product_id == Product.id
    ).join(
        Crop, ProductUse.crop_id == Crop.id
    ).join(
        Target, ProductUse.target_id == Target.id
    ).where(
        and_(
            ProductUse.crop_id == crop_match.crop_id,
            Product.is_currently_authorized == True,
            ProductUse.is_currently_authorized == True
        )
    )
    
    # Add target filter if specified
    if target_match:
        query = query.where(ProductUse.target_id == target_match.target_id)
    
    # Add organic filter
    if organic_only:
        query = query.where(
            or_(
                Product.authorized_mentions.ilike("%biologique%"),
                Product.authorized_mentions.ilike("%biocontrôle%"),
                Product.function_category.ilike("%biologique%")
            )
        )
    
    # Order by effectiveness criteria
    if urgency_level == "immediate":
        # Prioritize systemic and fast-acting products
        query = query.order_by(
            desc(ProductUse.min_dose),  # Higher dose might indicate faster action
            desc(Product.is_currently_authorized)
        )
    else:
        # Standard ordering by authorization and usage patterns
        query = query.order_by(
            desc(Product.is_currently_authorized),
            desc(ProductUse.max_applications)  # Products with more uses might be more effective
        )
    
    result = await session.execute(query.limit(limit * 2))  # Get more for filtering
    return result.all()


async def _create_product_recommendation(
    session: AsyncSession,
    product_use: ProductUse,
    product: Product,
    crop: Crop,
    target: Target,
    urgency_level: UrgencyLevel
) -> ProductRecommendation:
    """Create detailed product recommendation (legacy function)."""
    return await _create_product_recommendation_enhanced(
        session, product_use, product, crop, target, urgency_level, 1.0, 1.0
    )


async def _create_product_recommendation_enhanced(
    session: AsyncSession,
    product_use: ProductUse,
    product: Product,
    crop: Crop,
    target: Target,
    urgency_level: UrgencyLevel,
    crop_confidence: float,
    target_confidence: float
) -> ProductRecommendation:
    """Create detailed product recommendation with enhanced scoring."""
    
    # Get active substances with fallback
    substances_query = select(ActiveSubstance.name).join(
        ProductSubstance, ActiveSubstance.id == ProductSubstance.substance_id
    ).where(ProductSubstance.product_id == product.id)
    
    result = await session.execute(substances_query)
    substance_rows = result.all()
    
    if substance_rows:
        substances = [row[0] for row in substance_rows]
    else:
        # Fallback: Extract from function_category or product_name
        substances = _extract_substances_from_name(product.product_name, product.function_category)
    
    # Calculate enhanced effectiveness score
    effectiveness_score = _calculate_effectiveness_score(
        product_use, product, urgency_level, crop_confidence, target_confidence
    )
    
    # Determine safety level
    safety_level = await _determine_safety_level(session, product)
    
    # Assess data quality
    data_quality = _assess_data_quality(product_use, product)
    
    # Generate usage notes
    usage_notes = _generate_usage_notes(product_use, urgency_level)
    
    # Get special conditions
    special_conditions = await _get_special_conditions(session, product)
    
    return ProductRecommendation(
        product_id=product.id,
        product_name=product.product_name,
        registration_number=product.registration_number,
        active_substances=substances[:3],  # Limit to top 3
        function_category=product.function_category or "Unknown",
        application_dose_min=product_use.min_dose,
        application_dose_max=product_use.max_dose,
        dose_unit=product_use.dose_unit,
        harvest_interval=product_use.harvest_interval_days,
        max_applications=product_use.max_applications,
        effectiveness_score=effectiveness_score,
        safety_level=safety_level,
        data_quality=data_quality,
        usage_notes=usage_notes,
        special_conditions=special_conditions
    )


def _calculate_effectiveness_score(
    product_use: ProductUse, 
    product: Product, 
    urgency_level: UrgencyLevel,
    crop_confidence: float = 1.0,
    target_confidence: float = 1.0
) -> float:
    """Enhanced effectiveness scoring with multiple factors."""
    
    score_components = {}
    
    # Authorization status (25% weight)
    score_components['authorization'] = 0.25 if product.is_currently_authorized else 0
    
    # Data completeness (20% weight)  
    completeness = 0
    if product_use.min_dose is not None and product_use.max_dose is not None:
        completeness += 0.3
        # Check for suspicious values
        if product_use.max_dose > 1000:
            completeness *= 0.7  # Penalty for suspicious high doses
    if product_use.harvest_interval_days is not None:
        completeness += 0.25
    if product_use.max_applications is not None:
        completeness += 0.25
    if product_use.dose_unit:
        completeness += 0.2
    score_components['completeness'] = completeness * 0.20
    
    # Match confidence (15% weight)
    avg_confidence = (crop_confidence + target_confidence) / 2
    score_components['confidence'] = avg_confidence * 0.15
    
    # Urgency appropriateness (15% weight)
    urgency_score = 0.1  # Base score
    if urgency_level == "immediate":
        # Prefer products with shorter harvest intervals
        if product_use.harvest_interval_days and product_use.harvest_interval_days <= 14:
            urgency_score += 0.05
        # Prefer systemic products (indicated by lower doses for liquid formulations)
        if (product_use.dose_unit == "L/ha" and product_use.max_dose and 
            product_use.max_dose <= 2.0):
            urgency_score += 0.05
    score_components['urgency'] = urgency_score
    
    # Safety factor (10% weight)
    safety_bonus = 0.1
    if product.authorized_mentions:
        mentions_lower = product.authorized_mentions.lower()
        if "biologique" in mentions_lower:
            safety_bonus += 0.05
        if "biocontrôle" in mentions_lower:
            safety_bonus += 0.03
    score_components['safety'] = safety_bonus * 0.10
    
    # Recent authorization (10% weight)
    recency_score = 0.05
    if product.first_authorization_date:
        # Could add logic to prefer more recent authorizations
        recency_score += 0.05
    score_components['recency'] = recency_score * 0.10
    
    # Function category relevance (5% weight)
    category_score = 0.03
    if product.function_category:
        category_score += 0.02
    score_components['category'] = category_score * 0.05
    
    final_score = sum(score_components.values())
    return min(final_score, 1.0)
    
    # Function category completeness
    if product.function_category and product.function_category.strip():
        score += 0.05
    
    # Recent authorization suggests current relevance
    if product.first_authorization_date:
        score += 0.05
    
    return min(score, 1.0)


async def _determine_safety_level(session: AsyncSession, product: Product) -> SafetyLevel:
    """Enhanced safety assessment with better hazard interpretation."""
    from sqlalchemy import select
    from models.e_phy import ProductHazard
    
    # Get hazard information
    hazards_query = select(ProductHazard).where(ProductHazard.product_id == product.id)
    result = await session.execute(hazards_query)
    hazards = result.scalars().all()
    
    if not hazards:
        # Check authorized mentions for safety clues
        if product.authorized_mentions:
            mentions_lower = product.authorized_mentions.lower()
            if "agriculture biologique" in mentions_lower or "biocontrôle" in mentions_lower:
                return "low"
        return "moderate"  # Default when no hazard data
    
    # Analyze hazard codes and severity with proper mapping
    high_risk_codes = {"H300", "H301", "H302", "H310", "H311", "H330", "H331", "H350", "H351"}
    moderate_risk_codes = {"H315", "H319", "H335", "H400", "H410", "H411"}
    
    max_severity = 0
    has_high_risk = False
    has_carcinogen = False
    
    for hazard in hazards:
        # Check hazard codes
        if hazard.hazard_code in high_risk_codes:
            has_high_risk = True
        if hazard.hazard_code in {"H350", "H351"}:  # Carcinogenic
            has_carcinogen = True
            
        # Use severity if available (database shows 1-3 range)
        if hazard.hazard_severity:
            max_severity = max(max_severity, hazard.hazard_severity)
    
    # Determine safety level
    if has_carcinogen or max_severity >= 3:
        return "very_high"
    elif has_high_risk or max_severity >= 2:
        return "high"
    elif max_severity >= 1 or any(h.hazard_code in moderate_risk_codes for h in hazards):
        return "moderate"
    else:
        return "low"


def _assess_data_quality(product_use: ProductUse, product: Product) -> DataQuality:
    """Enhanced data quality assessment with weighted criteria."""
    
    # Critical fields (must have for basic functionality)
    critical_score = 0
    critical_total = 4
    
    if product_use.min_dose is not None and product_use.max_dose is not None:
        critical_score += 1
    if product_use.dose_unit:
        critical_score += 1  
    if product.is_currently_authorized is not None:
        critical_score += 1
    if product.function_category:
        critical_score += 1
    
    # Important fields (good to have)
    important_score = 0
    important_total = 3
    
    if product_use.harvest_interval_days is not None:
        important_score += 1
    if product_use.max_applications is not None:
        important_score += 1
    if product.authorization_status:
        important_score += 1
    
    # Calculate weighted score
    critical_weight = 0.7
    important_weight = 0.3
    
    final_score = (critical_score / critical_total * critical_weight + 
                   important_score / important_total * important_weight)
    
    # Add penalty for extreme/suspicious values
    if product_use.max_dose and product_use.max_dose > 1000:
        final_score *= 0.8  # Penalty for suspicious high doses
    
    if final_score >= 0.9:
        return "excellent"
    elif final_score >= 0.7:
        return "good"  
    elif final_score >= 0.5:
        return "fair"
    elif final_score >= 0.3:
        return "poor"
    else:
        return "unavailable"


def _generate_usage_notes(product_use: ProductUse, urgency_level: UrgencyLevel) -> List[str]:
    """Generate practical usage notes."""
    notes = []
    
    if product_use.application_comments:
        notes.append(f"Application notes: {product_use.application_comments}")
    
    if product_use.min_interval_between_applications:
        notes.append(f"Minimum interval between applications: {product_use.min_interval_between_applications} days")
    
    if product_use.aquatic_buffer_zone:
        notes.append(f"Required buffer zone from water: {product_use.aquatic_buffer_zone}m")
    
    if urgency_level == "immediate" and product_use.harvest_interval_days:
        notes.append(f"⚠️ Harvest interval: {product_use.harvest_interval_days} days - consider for urgent treatment")
    
    if not notes:
        notes.append("Follow label instructions for application")
    
    return notes


async def _get_special_conditions(session: AsyncSession, product: Product) -> List[str]:
    """Get special usage conditions for the product."""
    from sqlalchemy import select
    from models.e_phy import UsageCondition
    
    conditions_query = select(UsageCondition.condition_description).where(
        and_(
            UsageCondition.product_id == product.id,
            UsageCondition.condition_importance == "high"
        )
    )
    
    result = await session.execute(conditions_query)
    conditions = [row[0] for row in result.all() if row[0]]
    
    return conditions[:3]  # Limit to most important


async def _generate_alternative_approaches(
    session: AsyncSession,
    crop_match: CropMatch,
    target_match: Optional[TargetMatch],
    organic_only: bool
) -> List[str]:
    """Generate alternative management approaches."""
    approaches = []
    
    if target_match:
        if target_match.target_type == "Champignon":
            approaches.extend([
                "Consider preventive fungicide applications",
                "Improve air circulation and reduce humidity",
                "Use resistant varieties if available"
            ])
        elif target_match.target_type == "Insecte":
            approaches.extend([
                "Monitor pest populations with traps",
                "Consider beneficial insects for biological control",
                "Use cultural practices to reduce pest pressure"
            ])
        elif target_match.target_type == "Autre":  # Often weeds
            approaches.extend([
                "Implement mechanical weed control",
                "Use cover crops to suppress weeds",
                "Consider crop rotation strategies"
            ])
    
    if organic_only or not approaches:
        approaches.extend([
            "Explore biological control options",
            "Implement integrated pest management (IPM)",
            "Consider cultural and mechanical controls"
        ])
    
    return approaches[:3]


def _generate_preventive_measures(target_match: Optional[TargetMatch]) -> List[str]:
    """Generate preventive measures based on target type."""
    if not target_match:
        return ["Implement regular crop monitoring", "Maintain good agricultural practices"]
    
    measures = []
    
    if target_match.target_type == "Champignon":
        measures = [
            "Ensure proper plant spacing for air circulation",
            "Avoid overhead irrigation during humid conditions",
            "Remove infected plant debris promptly"
        ]
    elif target_match.target_type == "Insecte":
        measures = [
            "Monitor for early pest detection",
            "Maintain field hygiene and remove crop residues",
            "Consider trap crops or companion planting"
        ]
    else:
        measures = [
            "Regular field monitoring for early detection",
            "Maintain optimal growing conditions",
            "Practice crop rotation when possible"
        ]
    
    return measures


def _generate_resistance_advice(recommendations: List[ProductRecommendation]) -> List[str]:
    """Generate resistance management advice."""
    if len(recommendations) < 2:
        return ["Rotate between different modes of action", "Follow label instructions for maximum applications"]
    
    advice = [
        "Rotate between different active substances to prevent resistance",
        "Do not exceed maximum number of applications per season",
        "Consider tank mixing compatible products when appropriate"
    ]
    
    # Check for mode of action diversity
    substances = set()
    for rec in recommendations:
        substances.update(rec.active_substances)
    
    if len(substances) > 3:
        advice.append("Good diversity of active substances available for rotation")
    else:
        advice.append("Limited chemical diversity - consider non-chemical methods")
    
    return advice


def _generate_safety_warnings(recommendations: List[ProductRecommendation]) -> List[str]:
    """Generate safety warnings based on product safety levels."""
    warnings = []
    
    high_risk_products = [r for r in recommendations if r.safety_level in ["high", "very_high"]]
    
    if high_risk_products:
        warnings.append("⚠️ Some recommended products require special safety precautions")
        warnings.append("Always wear appropriate protective equipment")
        warnings.append("Follow all label safety instructions carefully")
    
    harvest_products = [r for r in recommendations if r.harvest_interval and r.harvest_interval > 7]
    if harvest_products:
        warnings.append(f"⚠️ Some products have harvest intervals > 7 days")
    
    if not warnings:
        warnings.append("Follow standard safety practices during application")
    
    return warnings


def _identify_data_limitations(
    crop_match: Optional[CropMatch],
    target_match: Optional[TargetMatch],
    recommendations: List[ProductRecommendation]
) -> List[str]:
    """Identify limitations in available data."""
    limitations = []
    
    if crop_match and crop_match.search_confidence < 0.8:
        limitations.append(f"Crop match confidence: {crop_match.search_confidence:.1%}")
    
    if target_match and target_match.search_confidence < 0.8:
        limitations.append(f"Pest match confidence: {target_match.search_confidence:.1%}")
    
    poor_data = [r for r in recommendations if r.data_quality in ["poor", "unavailable"]]
    if poor_data:
        limitations.append(f"{len(poor_data)} products have incomplete application data")
    
    missing_intervals = [r for r in recommendations if not r.harvest_interval]
    if missing_intervals:
        limitations.append(f"{len(missing_intervals)} products missing harvest interval data")
    
    return limitations


def _extract_substances_from_name(product_name: str, function_category: Optional[str]) -> List[str]:
    """Extract active substance hints from product name or function category when database data is missing."""
    substances = []
    
    # Common active substance patterns in product names
    substance_patterns = {
        # Herbicides
        'glyphosate': ['ROUNDUP', 'GLYPHO', 'GLYCEL'],
        'glufosinate': ['LIBERTY', 'BASTA', 'FINALE'],
        '2,4-D': ['2,4-D', '2.4.D', 'AMINE'],
        'dicamba': ['DICAMBA', 'BANVEL'],
        'mcpa': ['MCPA', 'AGRITOX'],
        
        # Fungicides
        'copper': ['CUIVRE', 'COPPER', 'BORDELAISE', 'BOUILLIE'],
        'sulfur': ['SOUFRE', 'SULFUR', 'THIOVIT', 'KUMULUS'],
        'mancozeb': ['MANCOZEBE', 'PENNCOZEB', 'DITHANE'],
        'metalaxyl': ['RIDOMIL', 'METALAXYL'],
        'azoxystrobin': ['AMISTAR', 'AZOXY'],
        
        # Insecticides
        'lambda-cyhalothrin': ['LAMBDA', 'KARATE', 'WARRIOR'],
        'deltamethrin': ['DECIS', 'DELTA'],
        'pyrethrin': ['PYRETHRE', 'PYRETHRIN'],
        'bacillus thuringiensis': ['BACILLUS', 'DIPEL', 'THURICIDE'],
        
        # Biological
        'trichoderma': ['TRICHODERMA'],
        'beauveria': ['BEAUVERIA'],
    }
    
    product_upper = product_name.upper()
    
    for substance, patterns in substance_patterns.items():
        for pattern in patterns:
            if pattern in product_upper:
                substances.append(substance.title())
                break
    
    # Fallback to function category if no substances found
    if not substances and function_category:
        if 'Herbicide' in function_category:
            substances = ['Herbicide active ingredient']
        elif 'Fongicide' in function_category:
            substances = ['Fungicide active ingredient']
        elif 'Insecticide' in function_category:
            substances = ['Insecticide active ingredient']
        elif 'fertilisant' in function_category.lower():
            substances = ['Fertilizer nutrients']
        else:
            substances = ['Active ingredient not specified']
    
    return substances[:3] if substances else ['Information not available']


def _determine_search_strategy(
    crop_match: Optional[CropMatch],
    target_match: Optional[TargetMatch],
    result_count: int
) -> str:
    """Determine and describe the search strategy used."""
    if not crop_match:
        return "No matching crop found"
    
    if not target_match:
        return f"Crop-wide search for {crop_match.matched_crop}"
    
    if result_count > 5:
        return f"Direct match: {crop_match.matched_crop} × {target_match.matched_target}"
    elif result_count > 0:
        return f"Limited results for {crop_match.matched_crop} × {target_match.matched_target}"
    else:
        return "No products found for this crop-pest combination"


def _create_no_crop_response(crop_name: str) -> CropProtectionResponse:
    """Create response when no crop match is found."""
    return CropProtectionResponse(
        crop_match=None,
        target_match=None,
        recommended_products=[],
        alternative_approaches=[
            "Verify crop name spelling",
            "Try crop name in other language French or English",
            "Try broader crop categories",
            "Consult local agricultural extension services"
        ],
        preventive_measures=[
            "Implement general good agricultural practices",
            "Regular monitoring for pest and disease issues"
        ],
        resistance_management=[],
        safety_warnings=[],
        data_limitations=[f"No match found for crop: {crop_name}"],
        search_strategy_used="Crop identification failed"
    )