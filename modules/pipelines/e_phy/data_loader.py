import asyncio
import csv
import os
import re
from datetime import datetime
from typing import Dict, List, Any, Optional

import pandas as pd
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession

from models.e_phy import (
    ActiveSubstance, Product, ProductSubstance, ParallelTradePermit,
    ProductComposition, Crop, Target, ProductUse, ProductHazard,
    UsageCondition
)
from utils.logger import logger
from utils.timer import Timer

# Constants for mapping
DATE_FORMAT = "%d/%m/%Y"
STATUS_MAPPING = {
    "AUTORISE": True,
    "RETIRE": False,
    "NON_INSCRITE": False,
    "Autorisé": True,
    "Retrait": False
}


class DataLoader:
    """
    Main class for loading E-Phy data from CSV files into the database.
    Handles extraction, transformation, validation, and loading of agricultural product data.
    """

    def __init__(self, session: AsyncSession, data_dir: str):
        """
        Initialize the data loader
        
        Args:
            session: SQLAlchemy async session object
            data_dir: Directory containing the CSV files to process
        """
        self.session = session
        self.data_dir = data_dir

        # Cache for object lookups to reduce database queries
        self.product_cache = {}
        self.substance_cache = {}
        self.substance_name_cache = {}  # New cache to optimize substance lookups
        self.normalized_substance_cache = {}  # New cache for normalized names
        self.crop_cache = {}
        self.target_cache = {}

        # Dataframes to store processed data
        self.dfs = {}

        # Statistics for data quality reporting
        self.stats = {
            'loaded_files': 0,
            'products': 0,
            'substances': 0,
            'crops': 0,
            'targets': 0,
            'uses': 0,
            'errors': []
        }
        self.t = Timer("E - PHY Data Loader")
        self.t.start()

    @staticmethod
    def normalize_column_name(col_name: str) -> str:
        """
        Normalize column names to handle character encoding issues
        Converts curved apostrophes to straight apostrophes and handles other special chars
        """
        if not isinstance(col_name, str):
            return col_name

        # Replace curly quotes with straight quotes using translate
        replacement_map = {
            ord('\u2018'): "'",
            ord('\u2019'): "'",
            ord('\u201C'): '"',
            ord('\u201D'): '"'
        }
        normalized = col_name.translate(replacement_map)
        # Handle other problematic characters

        normalized = normalized.strip()

        return normalized

    async def load_all_data(self):
        """Main method to load all data from CSV files"""
        logger.info("Starting E-Phy data loading process")

        try:
            self.t.checkpoint("Init")
            # Load and validate all CSV files first
            await self._load_csv_data()
            self.t.checkpoint("Load CSV")
            # Perform initial data cleaning and preprocessing
            self._preprocess_data()
            self.t.checkpoint("Preprocess")
            # Load data in proper order to maintain referential integrity
            await self.load_active_substances()
            self.t.checkpoint("Load Substances")
            await self.load_products()
            self.t.checkpoint("Load Products")
            await self.load_product_substances()
            self.t.checkpoint("Load Product Substances")
            await self.load_parallel_trade_permits()
            self.t.checkpoint("Load Parallel Trade Permits")
            await self.load_product_compositions()
            self.t.checkpoint("Load Product Compositions")
            await self.load_targets_and_crops()
            self.t.checkpoint("Load Targets and Crops")
            await self.load_product_uses()
            self.t.checkpoint("Load Product Uses")
            await self.load_product_hazards()
            self.t.checkpoint("Load Product Hazards")
            await self.load_usage_conditions()
            self.t.checkpoint("Load Usage Conditions")
            # Log successful completion
            logger.info("Data loading completed successfully")
            self._log_statistics()
            self.t.stop()

        except Exception as e:
            logger.error(f"Error in load_all_data: {e}")
            self.stats['errors'].append(f"Global error: {str(e)}")
            raise

    async def _load_csv_data(self):
        """Load all CSV files into pandas DataFrames for preprocessing"""
        logger.info("Loading CSV files into DataFrames")

        # Map of filename to internal DataFrame name
        file_mapping = {
            'substance_active_utf8.csv': 'substances',
            'produits_utf8.csv': 'products',
            'produits_usages_utf8.csv': 'product_uses',
            'usages_des_produits_autorises_utf8.csv': 'authorized_uses',
            'permis_de_commerce_parallele_utf8.csv': 'parallel_permits',
            'mfsc_et_mixte_composition_utf8.csv': 'mfsc_compositions',
            'mfsc_et_mixte_usage_utf8.csv': 'mfsc_uses',
            'produits_phrases_de_risque_utf8.csv': 'hazards',
            'produits_classe_et_mention_danger_utf8.csv': 'hazard_classes',
            'produits_condition_emploi_utf8.csv': 'usage_conditions'
        }

        async def load_single_file(filename: str, df_name: str):
            filepath = os.path.join(self.data_dir, filename)
            if not os.path.exists(filepath):
                logger.warning(f"File not found: {filepath}")
                self.stats['errors'].append(f"Missing file: {filename}")
                return

            try:
                df = await asyncio.to_thread(
                    pd.read_csv,
                    filepath,
                    delimiter=';',
                    encoding='utf-8',
                    low_memory=False
                )

                if df.empty:
                    logger.warning(f"Empty file: {filename}")
                    self.stats['errors'].append(f"Empty file: {filename}")
                    return

                # Normalize column names to handle character encoding issues
                df.columns = [self.normalize_column_name(col) for col in df.columns]

                self.dfs[df_name] = df
                logger.info(f"Loaded {filename} with {len(df)} rows")
                self.stats['loaded_files'] += 1

            except Exception as e:
                logger.error(f"Error loading {filename}: {e}")
                self.stats['errors'].append(f"Error loading {filename}: {str(e)}")

        tasks = [
            load_single_file(filename, df_name)
            for filename, df_name in file_mapping.items()
        ]
        await asyncio.gather(*tasks)

        logger.info(f"Loaded {self.stats['loaded_files']} CSV files")

    def _preprocess_data(self):
        """Perform initial cleaning and preprocessing of loaded data"""
        logger.info("Preprocessing data")

        # Process each DataFrame based on type
        for df_name, df in self.dfs.items():
            # Fill NaN values with empty strings for string columns
            for col in df.select_dtypes(include=['object']).columns:
                df[col] = df[col].fillna('')

            # Convert float columns that should be strings to avoid type errors
            # This handles cases where numeric values like permit numbers are stored as floats
            numeric_str_columns = ['numero AMM', 'N° Permis', 'N° AMM de référence français',
                                   'N° AMM du produit importé', 'Numéro AMM du produit de référence']

            for col in numeric_str_columns:
                if col in df.columns and df[col].dtype in ['float64', 'int64']:
                    # Convert numeric to string while handling NaN values
                    df[col] = df[col].apply(lambda x: '' if pd.isna(x) else str(x).rstrip('.0'))

            # Normalize column names by trimming whitespace
            df.columns = [col.strip() for col in df.columns]

            # Perform specific preprocessing based on DataFrame type
            if df_name == 'substances':
                # Normalize substance names
                if 'Nom substance active' in df.columns:
                    df['normalized_name'] = df['Nom substance active'].apply(self.normalize_name)

            elif df_name == 'products':
                # Clean and standardize product data
                if 'nom produit' in df.columns:
                    df['normalized_product_name'] = df['nom produit'].apply(self.normalize_name)

                # Pre-process Substances actives column for faster processing later
                if 'Substances actives' in df.columns:
                    df['substances_list'] = df['Substances actives'].apply(self._extract_substances)

            # Merge product uses from multiple sources if available
            if df_name == 'product_uses' and 'authorized_uses' in self.dfs:
                logger.info("Merging product uses data from multiple sources")
                # Ensure consistent column names for merging
                common_cols = set(df.columns).intersection(set(self.dfs['authorized_uses'].columns))
                if common_cols:
                    self.dfs[df_name] = pd.concat([df, self.dfs['authorized_uses']], ignore_index=True)
                    logger.info(f"Merged uses data, now has {len(self.dfs[df_name])} rows")

        logger.info("Data preprocessing completed")

    def _extract_substances(self, substances_text: str) -> List[Dict[str, Any]]:
        """Extract substance names, concentrations, and units from raw text"""
        if not substances_text or not isinstance(substances_text, str):
            return []

        result = []
        substance_entries = substances_text.split('|')

        for i, entry in enumerate(substance_entries):
            entry = entry.strip()
            if not entry:
                continue

            # Same regex patterns as before, but extracting directly to a structured format
            substance_name = None
            concentration = None
            unit = None

            # Pattern 1: Standard format with value and unit - "substance (Common) 10.0 g/L"
            match = re.match(r'(.*?)(?:\(([^)]+)\))?\s+(\d+(?:[.,]\d+)?)\s+(\w+(?:/\w+)?|\%)', entry)
            if match:
                substance_name = match.group(1).strip().lower()
                concentration = self.parse_float(match.group(3))
                unit = match.group(4)
            else:
                # Pattern 2: Format without parentheses - "substance 10.0 g/L"
                match = re.match(r'(.*?)\s+(\d+(?:[.,]\d+)?)\s+(\w+(?:/\w+)?|\%)', entry)
                if match:
                    substance_name = match.group(1).strip().lower()
                    concentration = self.parse_float(match.group(2))
                    unit = match.group(3)
                else:
                    # Pattern 3: Just percentage format - "substance (Common) 10.0%"
                    match = re.match(r'(.*?)(?:\(([^)]+)\))?\s+(\d+(?:[.,]\d+)?)\s*\%', entry)
                    if match:
                        substance_name = match.group(1).strip().lower()
                        concentration = self.parse_float(match.group(3))
                        unit = "%"
                    else:
                        # Pattern 4: Value followed by dots - "substance (Common) 80.0 .."
                        match = re.match(r'(.*?)(?:\(([^)]+)\))?\s+(\d+(?:[.,]\d+)?)\s*\.\.', entry)
                        if match:
                            substance_name = match.group(1).strip().lower()
                            concentration = self.parse_float(match.group(3))
                            unit = "%"  # Default to percentage as most likely
                        else:
                            # Pattern 5: Just name and common name - no concentration
                            match = re.match(r'(.*?)\s+\(([^)]+)\)', entry)
                            if match:
                                substance_name = match.group(1).strip().lower()
                                concentration = None
                                unit = None
                            else:
                                # Pattern 6: Just the substance name
                                substance_name = entry.lower()
                                concentration = None
                                unit = None

            if substance_name:
                result.append({
                    'name': substance_name,
                    'normalized_name': self.normalize_name(substance_name),
                    'concentration': concentration,
                    'unit': unit,
                    'primary': (i == 0)  # First substance is primary
                })

        return result

    async def read_csv(self, filename: str) -> List[Dict[str, Any]]:
        """
        Read a CSV file with semicolon delimiter and return list of dictionaries
        
        Args:
            filename: CSV filename to read
            
        Returns:
            List of dictionaries representing each row
        """
        filepath = os.path.join(self.data_dir, filename)

        try:
            # Use asyncio.to_thread to make file reading non-blocking
            return await asyncio.to_thread(self._read_csv_sync, filepath)
        except Exception as e:
            logger.error(f"Error reading {filename}: {e}")
            self.stats['errors'].append(f"Error reading {filename}: {str(e)}")
            return []

    def _read_csv_sync(self, filepath: str) -> List[Dict[str, Any]]:
        """Synchronous implementation of CSV reading for use with asyncio.to_thread"""
        data = []
        with open(filepath, 'r', encoding='utf-8') as f:
            reader = csv.reader(f, delimiter=';')
            headers = next(reader)

            # Clean headers
            headers = [h.strip() for h in headers]

            for row in reader:
                # Handle rows with fewer columns than headers
                if len(row) < len(headers):
                    row.extend([''] * (len(headers) - len(row)))

                # Create dictionary with cleaned values
                row_dict = {}
                for i, value in enumerate(row):
                    if i < len(headers):
                        row_dict[headers[i]] = value.strip() if isinstance(value, str) else value

                data.append(row_dict)

        return data

    def parse_date(self, date_str) -> Optional[datetime.date]:
        """
        Parse date from string in DD/MM/YYYY format
        
        Args:
            date_str: Date string to parse (can be string, float, or None)
            
        Returns:
            Date object or None if parsing fails
        """
        # Handle None or empty string
        if date_str is None:
            return None

        # Convert to string if needed
        if not isinstance(date_str, str):
            try:
                # Try to handle numeric timestamps or other non-string formats
                if isinstance(date_str, float) and pd.isna(date_str):
                    return None

                # Convert to string for processing
                date_str = str(date_str)
            except:
                return None

        # Handle empty strings
        if date_str.strip() == '':
            return None

        try:
            return datetime.strptime(date_str, DATE_FORMAT).date()
        except ValueError:
            # Try other common formats
            for fmt in ["%Y-%m-%d", "%d-%m-%Y", "%Y/%m/%d"]:
                try:
                    return datetime.strptime(date_str, fmt).date()
                except ValueError:
                    continue
            return None

    def safe_str(self, value, default='') -> str:
        """
        Safely convert any value to a string, handling None and NaN values
        
        Args:
            value: Value to convert to string
            default: Default value to return if value is None or NaN
            
        Returns:
            String representation of value or default
        """
        # Handle None
        if value is None:
            return default

        # Handle NaN values from pandas
        if isinstance(value, float) and pd.isna(value):
            return default

        # Convert to string
        try:
            # For floats that represent integers, strip trailing '.0'
            if isinstance(value, float) and value.is_integer():
                return str(int(value))
            # Handle floats with decimal part
            elif isinstance(value, float):
                # Convert to string - keeping the decimal part
                return str(value)
            return str(value)
        except:
            return default

    def normalize_name(self, name: str) -> str:
        """
        Create a normalized version of a name for better searching
        
        Args:
            name: String to normalize
            
        Returns:
            Normalized string
        """
        # Safely convert to string first
        name_str = self.safe_str(name)
        if not name_str:
            return ''

        # Convert to lowercase
        normalized = name_str.lower()

        # Remove accents (for French text)
        normalized = self._remove_accents(normalized)

        # Remove special characters
        normalized = re.sub(r'[^\w\s]', '', normalized)

        # Normalize whitespace
        normalized = re.sub(r'\s+', ' ', normalized).strip()

        return normalized

    def _remove_accents(self, text: str) -> str:
        """Remove accents from text"""
        accent_map = {
            'é': 'e', 'è': 'e', 'ê': 'e', 'ë': 'e',
            'à': 'a', 'â': 'a', 'ä': 'a',
            'î': 'i', 'ï': 'i',
            'ô': 'o', 'ö': 'o',
            'ù': 'u', 'û': 'u', 'ü': 'u',
            'ÿ': 'y', 'ç': 'c'
        }

        for accent, replace in accent_map.items():
            text = text.replace(accent, replace)

        return text

    def parse_float(self, value: str) -> Optional[float]:
        """
        Parse float from string, returning None if invalid
        
        Args:
            value: String to parse
            
        Returns:
            Float value or None
        """
        if value is None:
            return None

        # Handle NaN values
        if isinstance(value, float) and pd.isna(value):
            return None

        # Handle empty strings
        if isinstance(value, str) and value.strip() == '':
            return None

        try:
            # If it's already a float, return it directly
            if isinstance(value, float):
                return value

            # If it's a different numeric type, convert directly
            if isinstance(value, (int, complex)):
                return float(value)

            # For strings, replace comma with period for French number format
            if isinstance(value, str):
                return float(value.replace(',', '.'))

            # For any other type, attempt conversion
            return float(value)
        except (ValueError, TypeError):
            return None

    def parse_int(self, value: str) -> Optional[int]:
        """
        Parse integer from string, returning None if invalid
        
        Args:
            value: String to parse
            
        Returns:
            Integer value or None
        """
        if value is None:
            return None

        # Handle NaN values
        if isinstance(value, float) and pd.isna(value):
            return None

        # Handle empty strings
        if isinstance(value, str) and value.strip() == '':
            return None

        try:
            # If it's already an int, return it directly
            if isinstance(value, int):
                return value

            # If it's a float, convert to int
            if isinstance(value, float):
                # Don't convert NaN to int
                if pd.isna(value):
                    return None
                return int(value)

            # For strings, first convert to float in case it's a decimal string
            if isinstance(value, str):
                float_val = float(value.replace(',', '.'))
                return int(float_val)

            # For any other type, attempt conversion
            return int(value)
        except (ValueError, TypeError):
            return None

    async def load_active_substances(self):
        """Load active substances from CSV"""
        logger.info("Loading active substances")

        if 'substances' not in self.dfs:
            logger.warning("No substances data available")
            return

        df = self.dfs['substances']
        session = self.session
        substances_created = 0

        try:
            # Process substances in batches for better performance
            batch_size = 100
            total_rows = len(df)

            for start_idx in range(0, total_rows, batch_size):
                end_idx = min(start_idx + batch_size, total_rows)
                batch_df = df.iloc[start_idx:end_idx]

                for _, row in batch_df.iterrows():
                    name = row.get('Nom substance active', '')
                    if not name:
                        continue

                    # Check if substance already exists
                    existing = await session.execute(
                        select(ActiveSubstance).filter_by(name=name)
                    )
                    existing = existing.scalar_one_or_none()

                    if existing:
                        self.substance_cache[name.lower()] = existing.id
                        continue

                    # Create new substance
                    substance = ActiveSubstance(
                        name=name,
                        cas_number=row.get('Numero CAS', ''),
                        authorization_status=row.get("Etat d'autorisation", ''),
                        variants=row.get('Variant', ''),
                        normalized_name=self.normalize_name(name),
                        is_currently_authorized=STATUS_MAPPING.get(row.get("Etat d'autorisation", ''), False)
                    )
                    session.add(substance)
                    substances_created += 1

                # Commit the batch
                await session.commit()

                # Log progress for large datasets
                if substances_created % 500 == 0:
                    logger.info(f"Processed {substances_created} substances so far")

            # Cache substances for later use
            result = await session.execute(select(ActiveSubstance))
            for substance in result.scalars().all():
                # Cache by both regular and normalized name for faster lookups
                name_lower = substance.name.lower()
                self.substance_cache[name_lower] = substance.id
                self.substance_name_cache[name_lower] = substance.id

                # Cache normalized name for fuzzy matching
                if substance.normalized_name:
                    self.normalized_substance_cache[substance.normalized_name] = substance.id

                    # Also cache individual words for partial matching
                    for word in substance.normalized_name.split():
                        if len(word) > 3:  # Only index significant words
                            if word not in self.normalized_substance_cache:
                                self.normalized_substance_cache[word] = []
                            if isinstance(self.normalized_substance_cache[word], list):
                                self.normalized_substance_cache[word].append(substance.id)
                            else:
                                # If somehow it's not a list, convert to list with both values
                                old_value = self.normalized_substance_cache[word]
                                self.normalized_substance_cache[word] = [old_value, substance.id]

            self.stats['substances'] = substances_created
            logger.info(f"Loaded {substances_created} active substances")

        except Exception as e:
            await session.rollback()
            logger.error(f"Error loading active substances: {e}")
            self.stats['errors'].append(f"Error loading substances: {str(e)}")

    async def load_products(self):
        """Load products from CSV"""
        logger.info("Loading products")

        if 'products' not in self.dfs:
            logger.warning("No products data available")
            return

        df = self.dfs['products']
        session = self.session
        products_created = 0

        try:
            # Process products in batches for better performance
            batch_size = 100
            total_rows = len(df)

            for start_idx in range(0, total_rows, batch_size):
                end_idx = min(start_idx + batch_size, total_rows)
                batch_df = df.iloc[start_idx:end_idx]

                for _, row in batch_df.iterrows():
                    reg_number = row.get('numero AMM', '')
                    if not reg_number:
                        continue

                    # Check if product already exists
                    existing = await session.execute(
                        select(Product).filter_by(registration_number=reg_number)
                    )
                    existing = existing.scalar_one_or_none()

                    if existing:
                        self.product_cache[reg_number] = existing.id
                        continue

                    # Parse dates
                    withdrawal_date = self.parse_date(row.get('Date de retrait du produit', ''))
                    auth_date = self.parse_date(row.get('Date de première autorisation', ''))

                    # Create new product
                    product = Product(
                        product_type=row.get('type produit', ''),
                        registration_number=reg_number,
                        product_name=row.get('nom produit', ''),
                        alternative_names=row.get('seconds noms commerciaux', ''),
                        holder=row.get('titulaire', ''),
                        commercial_type=row.get('type commercial', ''),
                        usage_range=row.get('gamme usage', ''),
                        authorized_mentions=row.get('mentions autorisees', ''),
                        authorization_status=row.get("Etat d'autorisation", ''),
                        withdrawal_date=withdrawal_date,
                        first_authorization_date=auth_date,
                        reference_product_number=row.get('Numéro AMM du produit de référence', ''),
                        reference_product_name=row.get('Nom du produit de référence', ''),
                        formulation_type=row.get('formulations', ''),
                        function_category=row.get('fonctions', ''),
                        is_currently_authorized=STATUS_MAPPING.get(row.get("Etat d'autorisation", ''), False)
                    )

                    # Generate basic product summary
                    product_summary = f"{product.product_name} ({product.registration_number})"
                    if product.holder:
                        product_summary += f" - {product.holder}"
                    if product.function_category:
                        product_summary += f" - {product.function_category}"
                    product.product_summary = product_summary

                    session.add(product)
                    products_created += 1

                # Commit the batch
                await session.commit()

                # Log progress for large datasets
                if products_created % 500 == 0:
                    logger.info(f"Processed {products_created} products so far")

            # Cache products for later use
            result = await session.execute(select(Product))
            for product in result.scalars().all():
                self.product_cache[product.registration_number] = product.id

            self.stats['products'] = products_created
            logger.info(f"Loaded {products_created} products")

        except Exception as e:
            await session.rollback()
            logger.error(f"Error loading products: {e}")
            self.stats['errors'].append(f"Error loading products: {str(e)}")

    async def load_product_substances(self):
        """Extract product substances relationships from products.csv"""
        logger.info("Loading product-substance relationships")

        if 'products' not in self.dfs:
            logger.warning("No products data available for substance relationships")
            return

        df = self.dfs['products']
        session = self.session
        relationships_created = 0

        # Ensure we only have single IDs (not lists) in the substance cache
        # This prevents the unhashable type list error
        for key, value in list(self.normalized_substance_cache.items()):
            if isinstance(value, list) and len(value) > 0:
                # Keep the most frequent ID if possible
                id_counter = {}
                for sid in value:
                    if isinstance(sid, (int, str)):
                        id_counter[sid] = id_counter.get(sid, 0) + 1

                if id_counter:
                    # Find the most common ID
                    most_common_id = max(id_counter.items(), key=lambda x: x[1])[0]
                    self.normalized_substance_cache[key] = most_common_id
                else:
                    # If no valid IDs, remove the entry to avoid errors
                    self.normalized_substance_cache.pop(key)

        try:
            # Process relationships in batches
            batch_size = 100
            total_rows = len(df)

            for start_idx in range(0, total_rows, batch_size):
                end_idx = min(start_idx + batch_size, total_rows)
                batch_df = df.iloc[start_idx:end_idx]

                # Create a batch of objects to add to the session
                product_substance_batch = []

                for _, row in batch_df.iterrows():
                    product_id = self.product_cache.get(row.get('numero AMM', ''))
                    if not product_id:
                        continue

                    # Get the pre-processed substances list
                    substances_list = row.get('substances_list', [])
                    if not substances_list:
                        continue

                    # Track substance IDs already processed for this product to avoid duplicates
                    processed_substance_ids = set()

                    # Process each substance using the pre-processed data
                    for substance_data in substances_list:
                        substance_name = substance_data['name']
                        normalized_name = substance_data['normalized_name']
                        concentration = substance_data['concentration']
                        unit = substance_data['unit']
                        is_primary = substance_data['primary']

                        # Efficient substance lookup strategy:
                        # 1. Try exact match first (fastest)
                        substance_id = self.substance_name_cache.get(substance_name)

                        # 2. If not found, try normalized name
                        if not substance_id:
                            substance_id = self.normalized_substance_cache.get(normalized_name)

                        # 3. If still not found, try word-by-word matching
                        if not substance_id:
                            # Find potential matches based on individual words
                            matching_ids = set()
                            match_scores = {}

                            for word in normalized_name.split():
                                if len(word) > 3 and word in self.normalized_substance_cache:
                                    word_matches = self.normalized_substance_cache[word]
                                    # Handle both single values and lists
                                    if isinstance(word_matches, list):
                                        for sid in word_matches:
                                            if sid is not None:  # Ensure the ID is not None
                                                matching_ids.add(sid)
                                                match_scores[sid] = match_scores.get(sid, 0) + 1
                                    elif word_matches is not None:  # Handle direct values (not a list)
                                        matching_ids.add(word_matches)
                                        match_scores[word_matches] = match_scores.get(word_matches, 0) + 1

                            # Find the best match if any
                            if matching_ids:
                                best_id = None
                                best_score = 0
                                for sid, score in match_scores.items():
                                    if score > best_score:
                                        best_score = score
                                        best_id = sid

                                if best_score >= 2:  # Require at least two matching words
                                    substance_id = best_id

                        if substance_id:
                            # Skip if we've already processed this substance ID for this product
                            if substance_id in processed_substance_ids:
                                continue

                            # Add to processed set (only if hashable)
                            if not isinstance(substance_id, list):
                                processed_substance_ids.add(substance_id)

                            # Check if this relationship already exists in the database
                            existing = await session.execute(
                                select(ProductSubstance).filter_by(
                                    product_id=product_id,
                                    substance_id=substance_id
                                )
                            )
                            # Use first() instead of scalar_one_or_none() to handle potential duplicates
                            existing = existing.first()

                            if not existing:
                                product_substance = ProductSubstance(
                                    product_id=product_id,
                                    substance_id=substance_id,
                                    concentration=concentration,
                                    concentration_unit=unit,
                                    primary_substance=is_primary
                                )
                                product_substance_batch.append(product_substance)
                                relationships_created += 1

                # Add all objects to the session at once
                if product_substance_batch:
                    session.add_all(product_substance_batch)
                    await session.commit()

                    # Log progress for large datasets
                    logger.info(
                        f"Created {relationships_created} product-substance relationships for batch {start_idx}-{end_idx}")

            logger.info(f"Created {relationships_created} product-substance relationships total")

        except Exception as e:
            await session.rollback()
            logger.error(f"Error loading product substances: {e}")
            logger.error(f"Exception detail: {str(e)}")
            self.stats['errors'].append(f"Error loading product substances: {str(e)}")

    async def load_parallel_trade_permits(self):
        """Load parallel trade permits from CSV"""
        logger.info("Loading parallel trade permits")

        if 'parallel_permits' not in self.dfs:
            logger.warning("No parallel trade permits data available")
            return

        df = self.dfs['parallel_permits']
        session = self.session
        permits_created = 0

        # Create a set to track already processed permit_number + origin_state combinations
        # This helps avoid duplicates since some permit numbers appear multiple times
        # with different origin_states
        processed_combinations = set()

        try:
            # Process permits in batches
            batch_size = 100
            total_rows = len(df)

            for start_idx in range(0, total_rows, batch_size):
                end_idx = min(start_idx + batch_size, total_rows)
                batch_df = df.iloc[start_idx:end_idx]

                # Create a batch of permits to add at once
                permit_batch = []

                for _, row in batch_df.iterrows():
                    permit_number = row.get('N° Permis', '')
                    if not permit_number:
                        continue

                    # Create a unique composite key using permit number and origin state
                    # Ensure permit_number is a string to avoid type errors
                    permit_number_str = str(permit_number) if permit_number is not None else ''
                    origin_state = row.get('Etat Membre d\'origine', '')
                    # Convert origin_state to string to handle any non-string types
                    origin_state = str(origin_state) if origin_state is not None else ''
                    combo_key = f"{permit_number_str}|{origin_state}"

                    # Skip if we've already processed this combination
                    if combo_key in processed_combinations:
                        continue

                    # Check if permit already exists in database
                    existing = await session.execute(
                        select(ParallelTradePermit).filter_by(
                            permit_number=permit_number_str,
                            origin_member_state=origin_state
                        )
                    )
                    existing = existing.scalar_one_or_none()

                    if existing:
                        # Mark as processed and continue
                        processed_combinations.add(combo_key)
                        continue

                    # Find reference product ID
                    reference_reg = row.get('N° AMM de référence français', '')

                    # Define a local safe_get_str function
                    def safe_get_str(row, key, default=''):
                        value = row.get(key, default)
                        # Handle NaN values
                        if isinstance(value, float) and pd.isna(value):
                            return default
                        # Convert to string, handle None
                        return str(value) if value is not None else default

                    # Ensure reference_reg is a string
                    reference_reg = safe_get_str(row, 'N° AMM de référence français')
                    reference_product_id = self.product_cache.get(reference_reg)

                    # Generate a unique permit number that combines the original permit number and origin state
                    # This ensures uniqueness while preserving the original data
                    unique_permit_id = f"{permit_number_str}-{origin_state}" if origin_state else permit_number_str

                    # Helper function to safely get string values from the row
                    def safe_get_str(row, key, default=''):
                        value = row.get(key, default)
                        # Handle NaN values
                        if isinstance(value, float) and pd.isna(value):
                            return ''
                        # Convert to string, handle None
                        return str(value) if value is not None else ''

                    permit = ParallelTradePermit(
                        permit_number=unique_permit_id,  # Use composite ID to ensure uniqueness
                        product_name=safe_get_str(row, 'Nom du produit'),
                        authorization_status=safe_get_str(row, "Etat d'autorisation"),
                        permit_holder=safe_get_str(row, 'Détenteur PCP'),
                        french_reference_product=safe_get_str(row, 'Produit de référence français'),
                        french_reference_registration=reference_reg,
                        imported_product_name=safe_get_str(row, 'Nom du produit importé'),
                        imported_product_registration=safe_get_str(row, 'N° AMM du produit importé'),
                        origin_member_state=origin_state,
                        labeling_mentions=safe_get_str(row, 'Mentions d\'étiquetage'),
                        french_reference_product_id=reference_product_id,
                        is_currently_authorized=STATUS_MAPPING.get(safe_get_str(row, "Etat d'autorisation"), False)
                    )
                    permit_batch.append(permit)
                    permits_created += 1
                    processed_combinations.add(combo_key)

                # Add batch to session and commit
                if permit_batch:
                    session.add_all(permit_batch)
                    await session.commit()

                    # Log progress for large datasets
                    if permits_created % 500 == 0:
                        logger.info(f"Loaded {permits_created} parallel trade permits so far")

            logger.info(f"Loaded {permits_created} parallel trade permits")

        except Exception as e:
            await session.rollback()
            logger.error(f"Error loading parallel trade permits: {e}")
            self.stats['errors'].append(f"Error loading parallel trade permits: {str(e)}")

    async def load_product_compositions(self):
        """Load product compositions from CSV"""
        logger.info("Loading product compositions")

        if 'mfsc_compositions' not in self.dfs:
            logger.warning("No MFSC composition data available")
            return

        df = self.dfs['mfsc_compositions']
        session = self.session
        compositions_created = 0

        try:
            # Process compositions in batches
            batch_size = 100
            total_rows = len(df)

            for start_idx in range(0, total_rows, batch_size):
                end_idx = min(start_idx + batch_size, total_rows)
                batch_df = df.iloc[start_idx:end_idx]

                # Create a batch of compositions to add at once
                composition_batch = []

                for _, row in batch_df.iterrows():
                    product_id = self.product_cache.get(row.get('numero AMM', ''))
                    if not product_id:
                        continue

                    compositions_text = row.get('Composition', '')
                    if not compositions_text:
                        continue

                    # Parse composition entries like "Matière sèche (Min: 11.0 %, Max: 11.0 %)"
                    compositions = compositions_text.split('|')
                    for comp in compositions:
                        comp = comp.strip()
                        if not comp:
                            continue

                        # Try different regex patterns for robustness
                        match = re.match(r'(.*?)\s*\((Min:\s*([^%]+)[^,]*,\s*Max:\s*([^%]+)[^)]*)\)', comp)
                        if not match:
                            # Try alternative pattern
                            match = re.match(r'(.*?)\s*\((Min[^:]*:\s*([^,]+)[^,]*,\s*Max[^:]*:\s*([^)]+)[^)]*)\)',
                                             comp)
                            if not match:
                                logger.warning(f"Could not parse composition: {comp}")
                                continue

                        component_name = match.group(1).strip()
                        min_value = self.parse_float(match.group(3).strip()) if match.group(3).strip() else None
                        max_value = self.parse_float(match.group(4).strip()) if match.group(4).strip() else None

                        # Extract unit
                        unit_match = re.search(r'%|g/L|L/ha|kg/ha|mL/L', comp)
                        unit = unit_match.group(0) if unit_match else ''

                        # Assign component category for better categorization
                        component_category = self._categorize_component(component_name)

                        composition = ProductComposition(
                            product_id=product_id,
                            component_name=component_name,
                            min_value=min_value,
                            max_value=max_value,
                            unit=unit,
                            class_denomination=row.get('Dénomination de classe', ''),
                            component_category=component_category
                        )
                        composition_batch.append(composition)
                        compositions_created += 1

                # Add batch to session and commit
                if composition_batch:
                    session.add_all(composition_batch)
                    await session.commit()

                    # Log progress for large datasets
                    if compositions_created % 500 == 0:
                        logger.info(f"Loaded {compositions_created} product compositions so far")

            logger.info(f"Loaded {compositions_created} product compositions")

        except Exception as e:
            await session.rollback()
            logger.error(f"Error loading product compositions: {e}")
            self.stats['errors'].append(f"Error loading product compositions: {str(e)}")

    def _categorize_component(self, component_name: str) -> str:
        """
        Categorize a component based on its name

        Args:
            component_name: Name of the component

        Returns:
            Component category
        """
        name_lower = component_name.lower()

        # TODO extend the dict
        categories = {
            'Matière active': ['substance', 'active', 'actif', 'matière active', 'ingrédient actif'],
            'Nutriment': ['azote', 'phosphore', 'potassium', 'N', 'P', 'K', 'oligo-élément', 'nutriment',
                          'magnésium', 'calcium', 'soufre', 'fer', 'manganèse', 'zinc', 'cuivre', 'bore',
                          'molybdène', 'chlore', 'cobalt', 'silicium', 'engrais', 'nutriant', 'fertilisant'],
            'Matière organique': ['matière organique', 'composé organique', 'humus', 'compost', 'fumier',
                                  'tourbe', 'lignine', 'cellulose', 'acide humique', 'acide fulvique'],
            'Additif': ['adjuvant', 'additif', 'agent', 'stabilisant', 'conservateur', 'émulsifiant',
                        'mouillant', 'dispersant', 'adhésif', 'synergiste', 'tensioactif', 'anti-mousse',
                        'agent de compatibilité', 'phytoprotecteur'],
            'Solvant': ['solvant', 'eau', 'huile', 'alcool', 'acétone', 'glycol', 'pétrole', 'xylène',
                        'cyclohexanone', 'n-méthyl-2-pyrrolidone', 'diméthylsulfoxyde'],
            'Minéral': ['minéral', 'argile', 'silice', 'calcaire', 'magnésium', 'calcium', 'bentonite',
                        'kaolin', 'talc', 'dolomie', 'zéolite', 'perlite', 'vermiculite', 'sable'],
            'Biostimulant': ['biostimulant', 'acide aminé', 'algue', 'extrait', 'hormone végétale',
                             'bactérie bénéfique', 'mycorhize', 'auxine', 'cytokinine', 'gibbérelline',
                             'brassinostéroïde', 'trichoderma', 'bacillus'],
        }

        for category, keywords in categories.items():
            if any(keyword in name_lower for keyword in keywords):
                return category

        return 'Autre'  # Default category

    async def load_targets_and_crops(self):
        """Extract and create targets and crops from usage data"""
        logger.info("Loading targets and crops")

        all_crops = set()
        all_targets = set()

        # Combine usage data from both files
        if 'product_uses' in self.dfs:
            df_uses = self.dfs['product_uses']

            # Extract crops and targets from usage identifiers
            for _, row in df_uses.iterrows():
                usage_id = row.get('identifiant usage', '')
                if usage_id:
                    parts = usage_id.split('*')
                    if len(parts) >= 3:
                        crop_name = parts[0].strip()
                        target_name = parts[2].strip()

                        if crop_name:
                            all_crops.add(crop_name)
                        if target_name:
                            all_targets.add(target_name)

        # Extract from MFSC usage
        if 'mfsc_uses' in self.dfs:
            df_mfsc = self.dfs['mfsc_uses']

            for _, row in df_mfsc.iterrows():
                crop_name = row.get('type culture libelle', '')
                if crop_name:
                    all_crops.add(crop_name)

        session = self.session

        try:
            # Create Crop records in batches
            crops_created = 0
            crop_batch = []

            for crop_name in all_crops:
                if not crop_name:
                    continue

                # Check if crop already exists
                existing = await session.execute(
                    select(Crop).filter_by(crop_name=crop_name)
                )
                existing = existing.scalar_one_or_none()

                if existing:
                    self.crop_cache[crop_name.lower()] = existing.id
                    continue

                crop = Crop(
                    crop_name=crop_name,
                    normalized_name=self.normalize_name(crop_name),
                    crop_category=self._categorize_crop(crop_name),
                    common_synonyms='',  # Will be populated by data enhancer
                    usage_count=0  # Will be updated later
                )
                crop_batch.append(crop)

                # Add crops in batches for better performance
                if len(crop_batch) >= 100:
                    session.add_all(crop_batch)
                    await session.commit()
                    crops_created += len(crop_batch)
                    crop_batch = []

            # Add any remaining crops
            if crop_batch:
                session.add_all(crop_batch)
                await session.commit()
                crops_created += len(crop_batch)

            # Create Target records in batches
            targets_created = 0
            target_batch = []

            for target_name in all_targets:
                if not target_name:
                    continue

                # Check if target already exists
                existing = await session.execute(
                    select(Target).filter_by(target_name=target_name)
                )
                existing = existing.scalar_one_or_none()
                if existing:
                    self.target_cache[target_name.lower()] = existing.id
                    continue

                target = Target(
                    target_name=target_name,
                    target_type=self._categorize_target(target_name),
                    normalized_name=self.normalize_name(target_name),
                    common_synonyms=''  # Will be populated by data enhancer
                )
                target_batch.append(target)

                # Add targets in batches for better performance
                if len(target_batch) >= 100:
                    session.add_all(target_batch)
                    await session.commit()
                    targets_created += len(target_batch)
                    target_batch = []

            # Add any remaining targets
            if target_batch:
                session.add_all(target_batch)
                await session.commit()
                targets_created += len(target_batch)

            # Cache crops and targets for later use
            crop_result = await session.execute(select(Crop))
            for crop in crop_result.scalars().all():
                self.crop_cache[crop.crop_name.lower()] = crop.id

            target_result = await session.execute(select(Target))
            for target in target_result.scalars().all():
                self.target_cache[target.target_name.lower()] = target.id

            self.stats['crops'] = crops_created
            self.stats['targets'] = targets_created
            logger.info(f"Loaded {crops_created} crops and {targets_created} targets")

        except Exception as e:
            await session.rollback()
            logger.error(f"Error loading targets and crops: {e}")
            self.stats['errors'].append(f"Error loading targets and crops: {str(e)}")

    def _categorize_crop(self, crop_name: str) -> str:
        """
        Categorize a crop based on its name using a comprehensive list of categories and keywords.

        Args:
            crop_name: Name of the crop

        Returns:
            Crop category
        """
        name_lower = crop_name.lower()

        # TODO extend the dict
        categories = {
            'Céréales': ['blé', 'orge', 'avoine', 'seigle', 'céréale', 'maïs', 'riz', 'triticale', 'épeautre',
                         'sarrasin'],
            'Légumes': ['légume', 'carotte', 'tomate', 'poivron', 'aubergine', 'salade', 'épinard', 'chou',
                        'pomme de terre', 'pois', 'haricot', 'lentille', 'oignon', 'ail', 'échalote', 'asperge',
                        'betterave', 'navet', 'radis', 'celeri', 'fenouil'],
            'Fruits': ['fruit', 'pomme', 'poire', 'abricot', 'pêche', 'fraise', 'framboise', 'vigne', 'raisin',
                       'agrume', 'citron', 'orange', 'pamplemousse', 'mandarine', 'kiwi', 'banane', 'ananas',
                       'mangue', 'cerise', 'prune', 'figue', 'datte', 'myrtille', 'cassis', 'groseille'],
            'Oléagineux': ['colza', 'tournesol', 'soja', 'lin', 'arachide', 'olive', 'sésame', 'carthame'],
            'Plantes à fibre': ['coton', 'lin textile', 'chanvre', 'jute', 'sisal'],
            'Cultures industrielles': ['betterave sucrière', 'canne à sucre', 'houblon', 'tabac',
                                       'chicorée industrielle'],
            'Plantes aromatiques': ['aromatique', 'menthe', 'thym', 'basilic', 'persil', 'ciboulette', 'romarin',
                                    'sauge', 'estragon', 'coriandre', 'aneth', 'lavande'],
            'Plantes médicinales': ['médicinale', 'camomille', 'valériane', 'millepertuis', 'guimauve'],
            'Ornementales': ['fleur', 'ornementale', 'rosier', 'tulipe', 'chrysanthème', 'gazon', 'pelouse',
                             'géranium', 'marguerite', 'glaïeul', 'dahlia', 'orchidée'],
            'Cultures tropicales': ['banane', 'ananas', 'mangue', 'canne à sucre', 'café', 'cacao', 'noix de coco',
                                    'palmier', 'papaye', 'goyave', 'litchi'],
            'Sylviculture': ['arbre', 'forêt', 'pin', 'chêne', 'sapin', 'peuplier', 'bouleau', 'eucalyptus', 'hêtre'],
            'Prairies et fourrages': ['prairie', 'fourrage', 'luzerne', 'trèfle', 'fétuque', 'dactyle', 'graminée'],
            'Autres': []  # Default category
        }

        for category, keywords in categories.items():
            if any(keyword in name_lower for keyword in keywords):
                return category

        return 'Autres'  # Default category

    def _categorize_target(self, target_name: str) -> str:
        """
        Categorize a target based on its name

        Args:
            target_name: Name of the target

        Returns:
            Target type
        """
        name_lower = target_name.lower()

        # TODO extend the dict
        target_types = {
            'Insecte': ['puceron', 'chenille', 'mouche', 'cochenille', 'aleurode', 'thrips', 'coléoptère', 'insecte',
                        'punaise', 'cicadelle', 'psylle', 'teigne', 'doryphore', 'pyrale', 'charançon', 'scolyte',
                        'hanneton', 'cécidomyie', 'tipule', 'forficule', 'méligèthe', 'altise', 'bruche'],
            'Champignon': ['mildiou', 'oïdium', 'pourriture', 'rouille', 'septoriose', 'tavelure', 'fusariose',
                           'champignon', 'botrytis', 'phytophthora', 'alternaria', 'anthracnose', 'verticilliose',
                           'rhizoctone', 'sclérotinia', 'rhynchosporiose', 'helminthosporiose', 'cladosporiose',
                           'cercosporiose', 'moniliose', 'stemphyliose', 'phoma', 'pythium'],
            'Bactérie': ['bactérie', 'bactériose', 'feu bactérien', 'pseudomonas', 'xanthomonas', 'erwinia',
                         'clavibacter', 'agrobacterium', 'candidatus'],
            'Virus': ['virus', 'virose', 'mosaïque', 'jaunisse', 'enroulement', 'nanisme', 'marbrure'],
            'Acarien': ['acarien', 'araignée', 'tétranyque', 'phytopte', 'ériophyide', 'tarsonème'],
            'Nématode': ['nématode', 'anguillule', 'meloidogyne', 'heterodera', 'globodera', 'ditylenchus'],
            'Adventice': ['adventice', 'graminée', 'dicotylédone', 'mauvaise herbe', 'liseron', 'chiendent',
                          'rumex', 'chardon', 'ambroisie', 'folle avoine', 'morelle', 'séneçon', 'véronique',
                          'gaillet', 'renouée', 'mercuriale', 'pâturin', 'vulpin', 'brome', 'ray-grass', 'digitaire'],
            'Mollusque': ['escargot', 'limace', 'mollusque', 'gastéropode'],
            'Régulateur': ['tallage', 'verse', 'croissance', 'débourrement', 'floraison', 'germination',
                           'régulateur', 'nanifiant', 'élongation', 'maturité', 'chute', 'nouaison'],
            'Rongeur': ['rongeur', 'rat', 'souris', 'campagnol', 'taupe', 'mulot'],
            'Oiseau': ['oiseau', 'corbeau', 'étourneau', 'pigeon', 'moineau']
        }

        for target_type, keywords in target_types.items():
            if any(keyword in name_lower for keyword in keywords):
                return target_type

        return 'Autre'  # Default type

    async def load_product_uses(self):
        """Load product uses from CSV files"""
        logger.info("Loading product uses")

        uses_created = 0
        session = self.session

        try:
            # Load PPP uses in batches
            if 'product_uses' in self.dfs:
                df_uses = self.dfs['product_uses']

                # Process in smaller batches for better performance and more checkpoints
                batch_size = 50
                total_rows = len(df_uses)

                for start_idx in range(0, total_rows, batch_size):
                    end_idx = min(start_idx + batch_size, total_rows)
                    batch_df = df_uses.iloc[start_idx:end_idx]
                    use_batch = []
                    logger.info(f"Processing batch {end_idx}/{total_rows}")

                    for _, row in batch_df.iterrows():
                        product_id = self.product_cache.get(row.get('numero AMM', ''))
                        if not product_id:
                            continue

                        usage_id = row.get('identifiant usage', '')
                        if not usage_id:
                            continue

                        # Check if use already exists
                        existing = await session.execute(
                            select(ProductUse).filter_by(
                                product_id=product_id,
                                usage_id=usage_id
                            )
                        )
                        # Use first() instead of scalar_one_or_none() to handle potential duplicates
                        existing = existing.first()

                        if existing:
                            continue

                        # Extract crop and target from usage ID
                        parts = usage_id.split('*')
                        if len(parts) >= 3:
                            # Ensure crop_name and target_name are strings before calling strip()
                            crop_name = str(parts[0]).strip().lower()
                            target_name = str(parts[2]).strip().lower()

                            crop_id = self.crop_cache.get(crop_name)
                            target_id = self.target_cache.get(target_name)

                            if crop_id:
                                # Parse numeric fields - handling NaN values and type conversion
                                # Extract value, converting NaN to None and ensuring string conversion for valid values
                                def safe_get(row, key, default=''):
                                    value = row.get(key, default)
                                    # Handle NaN values from pandas
                                    if isinstance(value, float) and pd.isna(value):
                                        return ''
                                    # Handle other non-None values by converting to string
                                    return str(value) if value is not None else ''

                                dose_str = safe_get(row, 'dose retenue')
                                min_dose = self.parse_float(dose_str)

                                max_app_str = safe_get(row, 'nombre max d\'application')
                                max_applications = self.parse_int(max_app_str)

                                harvest_str = safe_get(row, 'delai avant recolte jour')
                                harvest_interval = self.parse_int(harvest_str)

                                min_stage_str = safe_get(row, 'stade cultural min (BBCH)')
                                min_growth_stage = self.parse_int(min_stage_str)

                                max_stage_str = safe_get(row, 'stade cultural max (BBCH)')
                                max_growth_stage = self.parse_int(max_stage_str)

                                aquatic_str = safe_get(row, 'ZNT aquatique (en m)')
                                aquatic_zone = self.parse_int(aquatic_str)

                                arthropod_str = safe_get(row, 'ZNT arthropodes non cibles (en m)')
                                arthropod_zone = self.parse_int(arthropod_str)

                                plant_str = safe_get(row, 'ZNT plantes non cibles (en m)')
                                plant_zone = self.parse_int(plant_str)

                                interval_str = safe_get(row, 'intervalle minimum entre applications (jour)')
                                min_interval = self.parse_int(interval_str)

                                # Calculate max buffer zone
                                buffer_zones = [z for z in [aquatic_zone, arthropod_zone, plant_zone] if z is not None]
                                max_buffer = max(buffer_zones) if buffer_zones else None

                                # Create usage record
                                usage = ProductUse(
                                    product_id=product_id,
                                    crop_id=crop_id,
                                    target_id=target_id,
                                    usage_id=usage_id,
                                    usage_description=safe_get(row, 'identifiant usage lib court'),
                                    application_part=self._derive_application_part(usage_id,
                                                                                   safe_get(row,
                                                                                            'identifiant usage lib court')),
                                    decision_date=self.parse_date(row.get('date decision', '')),
                                    min_growth_stage=min_growth_stage,
                                    max_growth_stage=max_growth_stage,
                                    usage_status=row.get('etat usage', ''),
                                    min_dose=min_dose,
                                    max_dose=min_dose,  # Same as min dose in this data
                                    dose_unit=safe_get(row, 'dose retenue unite'),
                                    harvest_interval_days=harvest_interval,
                                    max_applications=max_applications,
                                    distribution_end_date=self.parse_date(safe_get(row, 'date fin distribution')),
                                    usage_end_date=self.parse_date(safe_get(row, 'date fin utilisation')),
                                    application_conditions=safe_get(row, 'condition emploi'),
                                    aquatic_buffer_zone=aquatic_zone,
                                    arthropod_buffer_zone=arthropod_zone,
                                    plant_buffer_zone=plant_zone,
                                    min_interval_between_applications=min_interval,
                                    application_season_min='',  # Not directly available
                                    application_season_max='',  # Not directly available
                                    application_comments='',  # Not directly available
                                    is_currently_authorized=STATUS_MAPPING.get(safe_get(row, 'etat usage'), False),
                                    max_buffer_zone=max_buffer,
                                    has_special_conditions=bool(safe_get(row, 'condition emploi')),
                                    usage_summary=f"{str(parts[0])} - {str(parts[2])}"
                                )
                                use_batch.append(usage)
                                uses_created += 1

                    # Add batch to session and commit
                    if use_batch:
                        session.add_all(use_batch)
                        await session.commit()

                        # Log progress for large datasets
                        if uses_created % 500 == 0:
                            logger.info(f"Created {uses_created} product uses so far")

            # Load MFSC uses in batches
            if 'mfsc_uses' in self.dfs:
                df_mfsc = self.dfs['mfsc_uses']

                # Process in smaller batches for better performance and more checkpoints
                batch_size = 50
                total_rows = len(df_mfsc)

                for start_idx in range(0, total_rows, batch_size):
                    end_idx = min(start_idx + batch_size, total_rows)
                    batch_df = df_mfsc.iloc[start_idx:end_idx]
                    use_batch = []
                    logger.info(f"Processing batch mfsc_uses {end_idx}/{total_rows}")

                    for _, row in batch_df.iterrows():
                        product_id = self.product_cache.get(row.get('numero AMM', ''))
                        if not product_id:
                            continue

                        # Ensure crop_name is a string
                        crop_name_raw = row.get('type culture libelle', '')
                        crop_name = str(crop_name_raw).lower() if crop_name_raw is not None else ''
                        if not crop_name:
                            continue

                        crop_id = self.crop_cache.get(crop_name)
                        if crop_id:
                            # Generate a unique usage ID for MFSC
                            # Ensure both parts are strings
                            amm_value = row.get('numero AMM', '')
                            amm_str = str(amm_value) if amm_value is not None else ''

                            usage_id = f"{crop_name}*MFSC*{amm_str}"

                            # Check if use already exists
                            existing = await session.execute(
                                select(ProductUse).filter_by(
                                    product_id=product_id,
                                    usage_id=usage_id
                                )
                            )
                            # Use first() instead of scalar_one_or_none() to handle potential duplicates
                            existing = existing.first()

                            if existing:
                                continue

                            # Parse numeric fields - handling NaN values and type conversion
                            # Reuse the safe_get function to handle NaN values
                            def safe_get(row, key, default=''):
                                value = row.get(key, default)
                                # Handle NaN values from pandas
                                if isinstance(value, float) and pd.isna(value):
                                    return ''
                                # Handle other non-None values by converting to string
                                return str(value) if value is not None else ''

                            min_dose_str = safe_get(row, 'dose min par apport')
                            min_dose = self.parse_float(min_dose_str)

                            max_dose_str = safe_get(row, 'dose max par apport')
                            max_dose = self.parse_float(max_dose_str)

                            min_stage_str = safe_get(row, 'stade cultural min (BBCH)')
                            min_growth_stage = self.parse_int(min_stage_str)

                            max_stage_str = safe_get(row, 'stade cultural max (BBCH)')
                            max_growth_stage = self.parse_int(max_stage_str)

                            usage = ProductUse(
                                product_id=product_id,
                                crop_id=crop_id,
                                target_id=None,  # No target for MFSC
                                usage_id=usage_id,
                                usage_description=safe_get(row, 'culture commentaire'),
                                application_part='Totalité',  # Default for MFSC
                                decision_date=self.parse_date(safe_get(row, 'date decision')),
                                min_growth_stage=min_growth_stage,
                                max_growth_stage=max_growth_stage,
                                usage_status=safe_get(row, 'etat usage'),
                                min_dose=min_dose,
                                max_dose=max_dose,
                                dose_unit=safe_get(row, 'dose min par apport unite'),
                                harvest_interval_days=None,  # Not available
                                max_applications=None,  # Not available
                                application_season_min=safe_get(row, 'saison application min'),
                                application_season_max=safe_get(row, 'saison application max'),
                                application_comments=f"{safe_get(row, 'saison application min commentaire')} {safe_get(row, 'saison application max commentaire')}".strip(),
                                is_currently_authorized=STATUS_MAPPING.get(safe_get(row, 'etat usage'), False),
                                usage_summary=f"{str(crop_name)} - MFSC"
                            )
                            use_batch.append(usage)
                            uses_created += 1

                    # Add batch to session and commit
                    if use_batch:
                        session.add_all(use_batch)
                        await session.commit()

                        # Log progress for large datasets
                        if uses_created % 500 == 0:
                            logger.info(f"Created {uses_created} product uses so far")

            # Update usage count for crops
            await self._update_crop_usage_counts()

            self.stats['uses'] = uses_created
            logger.info(f"Loaded {uses_created} product uses")

        except Exception as e:
            import traceback
            await session.rollback()
            logger.error(f"Error loading product uses: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            self.stats['errors'].append(f"Error loading product uses: {str(e)}")

    async def _update_crop_usage_counts(self):
        """Update usage count for crops"""
        logger.info("Updating crop usage counts")
        session = self.session

        try:
            # Use SQL for efficiency
            result = await session.execute(
                select(
                    ProductUse.crop_id,
                    func.count(ProductUse.id).label('count')
                ).group_by(ProductUse.crop_id)
            )
            crop_counts = result.all()

            for crop_id, count in crop_counts:
                crop_result = await session.execute(select(Crop).filter(Crop.id == crop_id))
                crop = crop_result.scalar_one_or_none()
                if crop:
                    crop.usage_count = count

            await session.commit()
            logger.info(f"Updated usage counts for {len(crop_counts)} crops")

        except Exception as e:
            await session.rollback()
            logger.error(f"Error updating crop usage counts: {e}")

    def _derive_application_part(self, usage_id: str, description: str, comments: str = '') -> str:
        """
        Derive application part from usage ID, description, and comments

        Args:
            usage_id: Usage identifier
            description: Usage description
            comments: Application comments

        Returns:
            Application part
        """
        # TODO extend the dict
        application_parts = {
            'Partie aérienne': ['part.aer', 'feuillage', 'traitement aérien', 'pulvérisation', 'foliaire',
                                'parties aériennes', 'feuilles', 'tiges', 'rameaux', 'végétation', 'canopée'],
            'Sol': ['sol', 'terre', 'traitement du sol', 'incorporation', 'substrat', 'terreau',
                    'plancher', 'surface', 'prélevée', 'préplantation', 'présemis'],
            'Semence': ['semence', 'graine', 'semis', 'traitement de semence', 'enrobage', 'pelliculage',
                        'prégermination', 'pré-semis'],
            'Fruit': ['fruit', 'récolte', 'baies', 'gousses', 'cosses', 'siliques', 'drupes', 'pommes', 'poires'],
            'Racine': ['racine', 'racinaire', 'rhizome', 'bulbe', 'tubercule', 'radiculaire', 'collet'],
            'Tronc/Tige': ['tronc', 'tige', 'écorce', 'bois', 'cambium', 'stipe', 'branche', 'rameau', 'sarment'],
            'Post-récolte': ['post-récolte', 'stockage', 'conservation', 'entreposage', 'après récolte',
                             'silo', 'chambre froide', 'entrepôt'],
            'Irrigation': ['irrigation', 'fertirrigation', 'goutte à goutte', 'aspersion', "eau d'arrosage"],
            'Totalité': ['totalité', 'plante entière', 'plant', 'ensemble', 'culture', 'global']
        }

        fields_to_check = [usage_id or '', description or '', comments or '']
        for field in fields_to_check:
            field_lower = field.lower()
            for part, keywords in application_parts.items():
                if any(keyword in field_lower for keyword in keywords):
                    return part

        if usage_id and 'Trt Part.Aer.' in usage_id:
            return 'Partie aérienne'

        return 'Totalité'  # Default

    async def load_product_hazards(self):
        """Load product hazards from CSV"""
        logger.info("Loading product hazards")

        if 'hazards' not in self.dfs or 'hazard_classes' not in self.dfs:
            logger.warning("No hazard data available")
            return

        df_hazards = self.dfs['hazards']
        df_classes = self.dfs['hazard_classes']

        # Create mapping of hazard codes to categories and descriptions
        hazard_info = {}
        for _, row in df_classes.iterrows():
            code = row.get('Libellé court', '')
            description = row.get('Libellé long', '')
            if code:
                hazard_info[code] = description

        session = self.session
        hazards_created = 0

        try:
            # Process hazards in batches
            batch_size = 100
            total_rows = len(df_hazards)

            for start_idx in range(0, total_rows, batch_size):
                end_idx = min(start_idx + batch_size, total_rows)
                batch_df = df_hazards.iloc[start_idx:end_idx]
                hazard_batch = []

                for _, row in batch_df.iterrows():
                    product_id = self.product_cache.get(row.get('numero AMM', ''))
                    if not product_id:
                        continue

                    hazard_code = row.get('Libellé court phrase de risque', '')
                    if not hazard_code:
                        continue

                    # Check if hazard already exists for this product
                    existing = await session.execute(
                        select(ProductHazard).filter_by(
                            product_id=product_id,
                            hazard_code=hazard_code
                        )
                    )
                    # Use first() instead of scalar_one_or_none() to handle potential duplicates
                    existing = existing.first()

                    if existing:
                        continue

                    # Determine severity based on code (comprehensive approach)
                    severity = None
                    if hazard_code.startswith(('H3', 'R3')):
                        severity = 3  # High severity
                    elif hazard_code.startswith(('H2', 'R2')):
                        severity = 2  # Medium severity
                    elif hazard_code.startswith(('H4', 'R4')):
                        severity = 1  # Low severity
                    else:
                        # For other codes, try to determine based on numeric part
                        match = re.search(r'H(\d+)', hazard_code)
                        if match:
                            num = int(match.group(1))
                            if num >= 300 and num < 400:
                                severity = 3  # Toxic, flammable
                            elif num >= 200 and num < 300:
                                severity = 2  # Irritant, harmful
                            elif num >= 400:
                                severity = 1  # Environmental

                    # Determine category from hazard information
                    category = 'Autre'
                    if hazard_code in hazard_info:
                        description = hazard_info[hazard_code]
                        description_parts = description.split('-')
                        if len(description_parts) > 1:
                            category = description_parts[0].strip()

                    # Create hazard record
                    hazard = ProductHazard(
                        product_id=product_id,
                        hazard_code=hazard_code,
                        hazard_description=row.get('Libellé long phrase de risque', ''),
                        hazard_category=category,
                        hazard_severity=severity,
                        requires_special_equipment=severity >= 2 if severity else False
                    )
                    hazard_batch.append(hazard)
                    hazards_created += 1

                # Add batch to session and commit
                if hazard_batch:
                    session.add_all(hazard_batch)
                    await session.commit()

                    # Log progress for large datasets
                    if hazards_created % 1000 == 0:
                        logger.info(f"Created {hazards_created} product hazards so far")

            logger.info(f"Loaded {hazards_created} product hazards")

        except Exception as e:
            await session.rollback()
            logger.error(f"Error loading product hazards: {e}")
            self.stats['errors'].append(f"Error loading product hazards: {str(e)}")

    async def load_usage_conditions(self):
        """Load usage conditions from CSV"""
        logger.info("Loading usage conditions")

        if 'usage_conditions' not in self.dfs:
            logger.warning("No usage conditions data available")
            return

        df = self.dfs['usage_conditions']
        session = self.session
        conditions_created = 0

        try:
            # Process conditions in batches
            batch_size = 100
            total_rows = len(df)

            for start_idx in range(0, total_rows, batch_size):
                end_idx = min(start_idx + batch_size, total_rows)
                batch_df = df.iloc[start_idx:end_idx]
                condition_batch = []
                logger.debug(f"Processing batch {end_idx}/{total_rows}")
                for _, row in batch_df.iterrows():
                    product_id = self.product_cache.get(row.get('numero AMM', ''))
                    if not product_id:
                        continue

                    category = row.get('catégorie de condition d\'emploi', '')
                    description = row.get('condition d\'emploi libelle', '')

                    if not category or not description:
                        continue

                    # Check if condition already exists
                    existing = await session.execute(
                        select(UsageCondition).filter_by(
                            product_id=product_id,
                            condition_description=description
                        )
                    )
                    # Use first() instead of scalar_one_or_none() to handle potential duplicates
                    existing = existing.first()

                    if existing:
                        continue

                    # Determine condition type and importance
                    condition_type = self._determine_condition_type(category, description)
                    importance = self._determine_condition_importance(category, description)

                    condition = UsageCondition(
                        product_id=product_id,
                        condition_category=category,
                        condition_description=description,
                        condition_type=condition_type,
                        condition_importance=importance
                    )
                    condition_batch.append(condition)
                    conditions_created += 1

                # Add batch to session and commit
                if condition_batch:
                    session.add_all(condition_batch)
                    await session.commit()

                    # Log progress for large datasets
                    if conditions_created % 1000 == 0:
                        logger.info(f"Created {conditions_created} usage conditions so far")

            logger.info(f"Loaded {conditions_created} usage conditions")

        except Exception as e:
            await session.rollback()
            logger.error(f"Error loading usage conditions: {e}")
            self.stats['errors'].append(f"Error loading usage conditions: {str(e)}")

    def _determine_condition_type(self, category: str, description: str) -> str:
        """
        Determine the type of condition
        
        Args:
            category: Condition category
            description: Condition description
            
        Returns:
            Condition type
        """
        text = (category + ' ' + description).lower()

        if any(keyword in text for keyword in ['réglementaire', 'obligatoire', 'arrêté']):
            return 'regulatory'
        elif any(keyword in text for keyword in ['recommandation', 'conseil', 'recommandé']):
            return 'advisory'
        elif any(keyword in text for keyword in ['restriction', 'limité', 'interdit']):
            return 'restriction'

        return 'regulatory'  # Default

    def _determine_condition_importance(self, category: str, description: str) -> str:
        """
        Determine the importance of condition
        
        Args:
            category: Condition category
            description: Condition description
            
        Returns:
            Condition importance
        """
        text = (category + ' ' + description).lower()

        if any(keyword in text for keyword in ['danger', 'risque', 'protection', 'sécurité', 'toxique']):
            return 'high'
        elif any(keyword in text for keyword in ['important', 'essentiel', 'nécessaire']):
            return 'medium'

        return 'standard'  # Default

    def _log_statistics(self):
        """Log statistics about the loaded data"""
        logger.info("=== E-Phy Data Loading Statistics ===")
        logger.info(f"Files processed: {self.stats['loaded_files']}")
        logger.info(f"Products loaded: {self.stats['products']}")
        logger.info(f"Active substances loaded: {self.stats['substances']}")
        logger.info(f"Crops loaded: {self.stats['crops']}")
        logger.info(f"Targets loaded: {self.stats['targets']}")
        logger.info(f"Uses loaded: {self.stats['uses']}")

        if self.stats['errors']:
            logger.warning(f"Errors encountered: {len(self.stats['errors'])}")
            for i, error in enumerate(self.stats['errors'][:5], 1):
                logger.warning(f"Error {i}: {error}")

            if len(self.stats['errors']) > 5:
                logger.warning(f"... and {len(self.stats['errors']) - 5} more errors")

        logger.info("Data loading completed, but enhancement was skipped")
