# Embedding adapter system - with lazy loading
from .types import Embedding<PERSON><PERSON>ult, EmbeddingConfig
from .abc_adapter import Embedding<PERSON><PERSON>pter
from .cache import Embedding<PERSON><PERSON>, InMemoryEmbeddingCache, CacheManager
from .batch_processor import BatchProcessor, EmbeddingBatchProcessor
from .factory import (
    EmbeddingAdapterFactory,
    get_model_name,
    POPULAR_MODELS
)

# Note: Specific adapters are NOT imported here to avoid loading dependencies
# Use EmbeddingAdapterFactory.create_adapter() to get instances

__all__ = [
    # Core types
    "EmbeddingResult",
    "EmbeddingConfig",
    
    # Base adapter (for type hints)
    "EmbeddingAdapter",
    
    # Caching
    "EmbeddingCache",
    "InMemoryEmbeddingCache",
    "CacheManager",
    
    # Batch processing
    "BatchProcessor",
    "EmbeddingBatchProcessor",
    
    # Factory (main entry point)
    "EmbeddingAdapterFactory",
    "get_model_name",
    "POPULAR_MODELS"
]