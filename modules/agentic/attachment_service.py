"""
File input service for processing file inputs from users and preparing them for AI agents.
Handles MinIO file retrieval and conversion to Pydantic-AI compatible formats.
"""
from typing import List, Optional, Union
from pydantic_ai import BinaryContent

from modules.external.minio_adapter import MinIOAdapter
from schemas.assistant import SFileInput, MIMEType
from utils.logger import logger
from utils.mime_utils import resolve_mime_type, mime_to_category, get_content_type_string


class AttachmentService:
    """Service for processing file inputs and preparing them for AI agent consumption."""
    
    def __init__(self, default_bucket: str = None):
        """
        Initialize AttachmentService.
        
        Args:
            default_bucket: Default MinIO bucket for file operations
        """
        self.minio_adapter = MinIOAdapter(default_bucket)
    
    async def process_file_inputs(self, file_inputs: List[SFileInput]) -> List[Union[BinaryContent, str]]:
        """
        Process a list of file inputs and convert them to formats suitable for AI processing.
        
        Args:
            file_inputs: List of file input specifications
            
        Returns:
            List of BinaryContent objects and context strings ready for AI consumption
        """
        if not file_inputs:
            return []
        
        processed_items = []
        
        for file_input in file_inputs:
            try:
                # Process each file input
                result = await self._process_single_file(file_input)
                if result:
                    processed_items.extend(result)
                    
            except Exception as e:
                logger.error(f"Error processing file {file_input.object_name}: {e}")
                # Add error context instead of failing completely
                error_msg = f"⚠️ Failed to load file '{file_input.object_name}': {str(e)}"
                processed_items.append(error_msg)
        
        logger.info(f"Processed {len(processed_items)} file input items from {len(file_inputs)} file specifications")
        return processed_items
    
    async def _process_single_file(self, file_input: SFileInput) -> Optional[List[Union[BinaryContent, str]]]:
        """
        Process a single file input.
        
        Args:
            file_input: File input specification
            
        Returns:
            List containing BinaryContent and optional context string, or None if failed
        """
        try:
            # Get file info first
            file_info = await self.minio_adapter.get_file_info(
                file_input.object_name, 
                file_input.bucket_name
            )
            
            if not file_info:
                logger.warning(f"File not found: {file_input.object_name}")
                return None
            
            # Determine actual MIME type
            actual_mime_type = self._determine_mime_type(file_input, file_info['object_name'])
            
            # Validate MIME type is supported
            if not self._is_supported_mime_type(actual_mime_type):
                logger.warning(f"Unsupported MIME type for {file_input.object_name}: {actual_mime_type.value}")
                return [f"⚠️ Unsupported file type: {file_input.object_name}"]
            
            # Download file data
            file_data = await self.minio_adapter.download_file(
                file_input.object_name,
                file_input.bucket_name
            )
            
            if not file_data:
                logger.error(f"Failed to download file data for {file_input.object_name}")
                return None
            
            # Create BinaryContent with proper MIME type
            content_type = get_content_type_string(actual_mime_type)
            binary_content = BinaryContent(
                data=file_data,
                media_type=content_type
            )
            
            # Prepare result
            result = [binary_content]
            
            # Add context string with file description
            context_parts = []
            
            # Get category for display
            file_category = mime_to_category(actual_mime_type)
            
            # Add file information
            file_size_mb = file_info.get('size', 0) / (1024 * 1024)
            context_parts.append(f"📎 **File**: {file_input.object_name}")
            context_parts.append(f"📊 **Type**: {file_category.title()} ({actual_mime_type.value})")
            context_parts.append(f"📏 **Size**: {file_size_mb:.2f} MB")
            

            # Add upload date if available
            if 'last_modified' in file_info:
                context_parts.append(f"📅 **Modified**: {file_info['last_modified'].strftime('%Y-%m-%d %H:%M')}")
            
            context_string = "\n".join(context_parts)
            result.append(f"\n{context_string}\n")
            
            logger.info(f"Successfully processed {file_category} file: {file_input.object_name} ({actual_mime_type.value})")
            return result
            
        except Exception as e:
            logger.error(f"Error processing file {file_input.object_name}: {e}")
            return None
    
    def _determine_mime_type(self, file_input: SFileInput, filename: str) -> MIMEType:
        """
        Determine the actual MIME type based on input specification and filename.
        
        Args:
            file_input: File input specification
            filename: Actual filename
            
        Returns:
            MIMEType enum value
        """
        # Use the resolve_mime_type utility function
        return resolve_mime_type(file_input.mime_type, filename)
    
    def _is_supported_mime_type(self, mime_type: MIMEType) -> bool:
        """
        Check if the MIME type is supported.
        
        Args:
            mime_type: MIMEType to check
            
        Returns:
            True if supported, False otherwise
        """
        return self.minio_adapter.is_supported_mime_type(mime_type)
    
    async def validate_file_inputs(self, file_inputs: List[SFileInput]) -> List[str]:
        """
        Validate file inputs and return list of validation errors.
        
        Args:
            file_inputs: List of file input specifications
            
        Returns:
            List of validation error messages (empty if all valid)
        """
        errors = []
        
        for i, file_input in enumerate(file_inputs):
            # Check if file exists
            file_info = await self.minio_adapter.get_file_info(
                file_input.object_name,
                file_input.bucket_name
            )
            
            if not file_info:
                errors.append(f"File {i+1}: '{file_input.object_name}' not found in storage")
                continue
            
            # Check if MIME type is supported
            actual_mime_type = self._determine_mime_type(file_input, file_info['object_name'])
            if not self._is_supported_mime_type(actual_mime_type):
                errors.append(f"File {i+1}: '{file_input.object_name}' has unsupported MIME type: {actual_mime_type.value}")
        
        return errors