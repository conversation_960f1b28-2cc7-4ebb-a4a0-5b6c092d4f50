import asyncio
import uuid

from const import EConnectionState, EAgent
from modules.agentic.agents.general_agent import GeneralAgent
from modules.agentic.agents.selective_agent import SelectiveAgent, SSelectiveResponse
from modules.agentic.attachment_service import AttachmentService
from modules.agentic.utils.response_processor import ResponseProcessor
from modules.agentic.utils.retry_handler import <PERSON><PERSON><PERSON><PERSON>ler
from modules.web.web_knowledge_service import WebKnowledgeService
from modules.web.web_searcher.tavily_searcher import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>cher
from schemas.assistant import SChatRequest
from utils.logger import logger
from utils.timer import Timer


class AgentService:
    @staticmethod
    async def process_chat(request: SChatRequest):
        # Initialize simplified processor and retry handler
        processor = ResponseProcessor(suppress_error_events=True)
        retry_handler = RetryHandler(max_retries=1)

        # Initial connection state
        await processor.add_event(processor.create_connection_event(EConnectionState.connecting, "Connecting to AI..."))

        # Selective node processing
        timer = Timer('Assistant Event')
        timer.start()

        selective_processor = ResponseProcessor(suppress_error_events=True)
        try:
            selective_node_res: SSelectiveResponse = await SelectiveAgent().run(request, selective_processor)
            logger.info(f"Selective Node: {selective_node_res}")
            timer.checkpoint("Selective Node")
        finally:
            # Clean up selective processor
            try:
                await selective_processor.cleanup()
            except Exception as selective_cleanup_error:
                logger.error(f"Error cleaning up selective processor: {str(selective_cleanup_error)}")

        # Process file attachments
        attachments = []
        if request.attachments:
            await processor.add_event(
                processor.create_connection_event(EConnectionState.processing, "Processing uploaded files..."))

            attachments_service = AttachmentService()
            attachments = await attachments_service.process_file_inputs(request.attachments)
            timer.checkpoint("File Processing")
            logger.info(f"Processed {len(attachments)} file content items")

        # Knowledge base (RAG) search - transparent to LLM
        kb_results = ""
        if selective_node_res.vector_search_query:
            await processor.add_event(
                processor.create_connection_event(EConnectionState.processing, "Searching knowledge base..."))

            try:
                from modules.knowledge_base.services.knowledge_base_service import KnowledgeBaseService
                kb_service = KnowledgeBaseService()

                # Get user context for access control
                user_id = None
                enterprise_id = None
                if request.personal_context:
                    user_id = str(request.personal_context.id)

                # Perform initial knowledge base search
                search_results = await kb_service.search_documents(
                    query=selective_node_res.vector_search_query,
                    user_id=user_id,
                    enterprise_id=enterprise_id,
                    limit=5,
                    include_chunks=True,
                    search_method="hybrid"
                )

                # Cache search results to prevent identical searches
                search_key = f"{selective_node_res.vector_search_query}:hybrid:5"
                request.search_cache[search_key] = search_results

                # Format results transparently for LLM
                if search_results.get('results'):
                    kb_results_parts = []
                    kb_results_parts.append(f"📚 **AUTOMATIC KNOWLEDGE BASE SEARCH RESULTS**")
                    kb_results_parts.append(f"Search Query: '{selective_node_res.vector_search_query}'")
                    kb_results_parts.append(f"Method: Hybrid (semantic + keyword)")
                    kb_results_parts.append(f"Found: {len(search_results['results'])} relevant document(s)")
                    kb_results_parts.append("")

                    for i, result in enumerate(search_results['results'], 1):
                        chunk_content = result.get('chunk_content', 'No content available')
                        document_title = result.get('document_title', 'Untitled Document')
                        relevance_score = result.get('relevance_score', 0.0)
                        document_id = result.get('document_id', 'unknown')
                        page_number = result.get('page_number')
                        
                        # Create citation info
                        citation_parts = [f"Source: {document_title}"]
                        if page_number:
                            citation_parts.append(f"Page {page_number}")
                        citation = " | ".join(citation_parts)

                        kb_results_parts.append(f"**Result {i}**: {citation} (Relevance: {relevance_score:.2f})")
                        kb_results_parts.append(f"Content: {chunk_content}")
                        kb_results_parts.append(f"Document ID: {document_id}")
                        kb_results_parts.append("")

                    kb_results_parts.append(
                        "💡 **Note**: You can use knowledge base tools to search for more specific information if needed.")

                    kb_results = "\n".join(kb_results_parts)
                    timer.checkpoint("Knowledge Base Search")
                    logger.info(f"Automatic KB search completed: {len(search_results['results'])} results")
                else:
                    kb_results = f"📚 **AUTOMATIC KNOWLEDGE BASE SEARCH RESULTS**\nSearch Query: '{selective_node_res.vector_search_query}'\nNo relevant documents found in your knowledge base.\n\n💡 **Note**: You can use knowledge base tools to try different search terms if needed."

            except Exception as e:
                logger.error(f"Knowledge base search failed: {e}")
                kb_results = f"📚 **AUTOMATIC KNOWLEDGE BASE SEARCH**\nSearch for '{selective_node_res.vector_search_query}' failed: {str(e)}\n\n💡 **Note**: You can try using knowledge base tools for manual search."

        # Web search with training data collection
        web_results = ""
        message_id = str(uuid.uuid4())  # Generate unique message ID

        if request.web and selective_node_res.web_search_query:
            await processor.add_event(
                processor.create_connection_event(EConnectionState.processing, "Searching the web..."))

            # Always perform fresh web search (no caching for time-sensitive data)
            web_searcher = TavilyWebSearcher()
            raw_results = await web_searcher.search(selective_node_res.web_search_query)
            timer.checkpoint("Web Search")
            web_results = web_searcher.format_for_llm(raw_results, 5)

            # Store high-quality results for fine-tuning (background task)
            web_knowledge_service = WebKnowledgeService()
            asyncio.create_task(
                web_knowledge_service.store_web_training_data(
                    message_id=message_id,
                    query=selective_node_res.web_search_query,
                    raw_results=raw_results
                )
            )

        # Prepare request query with context from both knowledge base and web search
        context_parts = []
        if kb_results:
            context_parts.append(kb_results)
        if web_results:
            context_parts.append(web_results)

        context_string = "\n\n".join(context_parts) if context_parts else ""
        request.query = f"{context_string} <UserQuery>\n{request.query}\n</UserQuery>\n (Sticky use given JSON Line templates in the system prompt to format your response)"

        if hasattr(selective_node_res, 'agent') and selective_node_res.agent:
            request.agent = selective_node_res.agent

        # Create agent
        match request.agent:
            case EAgent.general:
                agent = GeneralAgent(request.style)
            case _:
                agent = GeneralAgent(request.style)

        try:
            # Use simplified retry handler
            async for event_json in retry_handler.execute_with_retry(agent, request, processor, attachments):
                timer.checkpoint("Event")
                yield event_json

            # Final completion event
            if processor.completed:
                event = processor.create_connection_event(EConnectionState.completed, "Completed")
                timer.stop()
                yield event.model_dump_json(exclude_none=True)
                timer.checkpoint("Completed")

        except asyncio.CancelledError:
            logger.info("Client disconnected - cleaning up resources")
            # Ensure cleanup on cancellation
            try:
                await processor.cleanup()
            except Exception as cleanup_error:
                logger.error(f"Error during cancellation cleanup: {str(cleanup_error)}")
            raise  # Re-raise to properly handle cancellation

        except Exception as e:
            logger.error(f"Error in process_chat: {str(e)}", exc_info=True)

            # Clean up resources before error response
            try:
                await processor.cleanup()
            except Exception as cleanup_error:
                logger.error(f"Error during error cleanup: {str(cleanup_error)}")

            error_event = processor.create_connection_event(
                EConnectionState.error, f"Stream error: {str(e)}"
            )
            yield error_event.model_dump_json(exclude_none=True)

        finally:
            # Log the full response BEFORE cleanup
            try:
                logger.info(f"<LLM>{processor.response}</LLM>")
            except Exception as log_error:
                logger.error(f"Error logging response: {str(log_error)}")

            # Ensure cleanup always happens
            try:
                await processor.cleanup()
                timer.stop()
            except Exception as final_cleanup_error:
                logger.error(f"Error in final cleanup: {str(final_cleanup_error)}")
