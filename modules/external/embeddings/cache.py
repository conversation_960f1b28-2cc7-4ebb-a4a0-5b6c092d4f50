import hashlib
import time
from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any, Tuple
from dataclasses import dataclass, asdict
import json


@dataclass
class CacheEntry:
    """Cache entry with embedding data and metadata."""
    embedding: List[float]
    model: str
    timestamp: float
    dimensions: int
    ttl: Optional[float] = None
    
    def is_expired(self) -> bool:
        """Check if cache entry is expired."""
        if self.ttl is None:
            return False
        return time.time() - self.timestamp > self.ttl
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CacheEntry':
        """Create from dictionary."""
        return cls(**data)


class EmbeddingCache(ABC):
    """Abstract base class for embedding caches."""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[CacheEntry]:
        """Get cached embedding by key."""
        pass
    
    @abstractmethod
    async def set(self, key: str, entry: CacheEntry) -> None:
        """Set cached embedding."""
        pass
    
    @abstractmethod
    async def get_many(self, keys: List[str]) -> Dict[str, Optional[CacheEntry]]:
        """Get multiple cached embeddings."""
        pass
    
    @abstractmethod
    async def set_many(self, entries: Dict[str, CacheEntry]) -> None:
        """Set multiple cached embeddings."""
        pass
    
    @abstractmethod
    async def clear(self) -> None:
        """Clear all cached embeddings."""
        pass
    
    def generate_key(self, text: str, model: str) -> str:
        """Generate cache key from text and model."""
        content = f"{model}:{text}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()


class InMemoryEmbeddingCache(EmbeddingCache):
    """In-memory embedding cache implementation."""
    
    def __init__(self, max_size: int = 10000, default_ttl: Optional[float] = None):
        self.cache: Dict[str, CacheEntry] = {}
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.access_times: Dict[str, float] = {}
    
    async def get(self, key: str) -> Optional[CacheEntry]:
        """Get cached embedding by key."""
        if key not in self.cache:
            return None
        
        entry = self.cache[key]
        
        # Check if expired
        if entry.is_expired():
            await self._remove(key)
            return None
        
        # Update access time for LRU
        self.access_times[key] = time.time()
        return entry
    
    async def set(self, key: str, entry: CacheEntry) -> None:
        """Set cached embedding."""
        # Set default TTL if not specified
        if entry.ttl is None and self.default_ttl is not None:
            entry.ttl = self.default_ttl
        
        # Evict if at max size
        if len(self.cache) >= self.max_size and key not in self.cache:
            await self._evict_lru()
        
        self.cache[key] = entry
        self.access_times[key] = time.time()
    
    async def get_many(self, keys: List[str]) -> Dict[str, Optional[CacheEntry]]:
        """Get multiple cached embeddings."""
        results = {}
        for key in keys:
            results[key] = await self.get(key)
        return results
    
    async def set_many(self, entries: Dict[str, CacheEntry]) -> None:
        """Set multiple cached embeddings."""
        for key, entry in entries.items():
            await self.set(key, entry)
    
    async def clear(self) -> None:
        """Clear all cached embeddings."""
        self.cache.clear()
        self.access_times.clear()
    
    async def _remove(self, key: str) -> None:
        """Remove entry from cache."""
        self.cache.pop(key, None)
        self.access_times.pop(key, None)
    
    async def _evict_lru(self) -> None:
        """Evict least recently used entry."""
        if not self.access_times:
            return
        
        lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        await self._remove(lru_key)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            "size": len(self.cache),
            "max_size": self.max_size,
            "hit_ratio": getattr(self, "_hit_count", 0) / max(getattr(self, "_total_requests", 1), 1)
        }


class CacheManager:
    """Manager for embedding cache operations with batch processing."""
    
    def __init__(self, cache: EmbeddingCache):
        self.cache = cache
    
    async def get_cached_embeddings(
        self, 
        texts: List[str], 
        model: str
    ) -> Tuple[Dict[str, List[float]], List[str]]:
        """
        Get cached embeddings and return both cached results and missing texts.
        
        Returns:
            Tuple of (cached_embeddings_dict, missing_texts)
        """
        keys = [self.cache.generate_key(text, model) for text in texts]
        cached_entries = await self.cache.get_many(keys)
        
        cached_results = {}
        missing_texts = []
        
        for text, key in zip(texts, keys):
            entry = cached_entries.get(key)
            if entry is not None:
                cached_results[text] = entry.embedding
            else:
                missing_texts.append(text)
        
        return cached_results, missing_texts
    
    async def cache_embeddings(
        self, 
        texts: List[str], 
        embeddings: List[List[float]], 
        model: str,
        dimensions: int,
        ttl: Optional[float] = None
    ) -> None:
        """Cache embeddings for given texts."""
        if len(texts) != len(embeddings):
            raise ValueError("Number of texts and embeddings must match")
        
        entries = {}
        timestamp = time.time()
        
        for text, embedding in zip(texts, embeddings):
            key = self.cache.generate_key(text, model)
            entry = CacheEntry(
                embedding=embedding,
                model=model,
                timestamp=timestamp,
                dimensions=dimensions,
                ttl=ttl
            )
            entries[key] = entry
        
        await self.cache.set_many(entries)