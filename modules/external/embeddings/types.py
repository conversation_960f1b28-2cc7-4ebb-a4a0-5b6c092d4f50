from dataclasses import dataclass
from typing import List, Optional, Dict, Any


@dataclass
class EmbeddingResult:
    """Result from embedding operation containing embeddings and metadata."""
    embeddings: List[List[float]]
    model: str
    dimensions: int
    usage: Optional[Dict[str, int]] = None


@dataclass
class EmbeddingConfig:
    """Configuration for embedding adapters."""
    model: str
    api_key: Optional[str] = None
    max_retries: int = 3
    timeout: int = 30
    extra_config: Optional[Dict[str, Any]] = None