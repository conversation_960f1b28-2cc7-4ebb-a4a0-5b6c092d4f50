import json

from modules.automation.tools.purchase_order import purchase_order, PurchaseOrderInputs
from utils.timer import Timer


class AutomationService:
    def __init__(self):
        self.tasks = []


    async def generate_purchase_order(self, advisor_note):
        timer = Timer()
        timer.start()
        # Create input data
        inputs = PurchaseOrderInputs(
            advisor_notes=advisor_note,
            client_id="CLI-78452",
            advisor_id="ADV-24601",
            campaign_year=2025
        )

        # Run the purchase order generation
        result = await purchase_order.run("Generate a purchase order based on the advisor notes",deps=inputs)

        timer.stop()

        # print(result.all_messages())
        # Print the result nicely formatted
        print("\n=== PURCHASE ORDER GENERATION ===\n")

        # print("\nGenerated Purchase Order:")

        # Pretty print with indentation
        formatted_json = json.dumps(result.data.model_dump(), indent=2)
        # print(formatted_json)
        return result.data


