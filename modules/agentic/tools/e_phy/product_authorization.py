"""
Unified product authorization tool for agricultural agent.
Handles both "For which crops is PRODUCT authorized?" and "Is PRODUCT authorized for CROP?" queries.
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func, or_, case
from sqlalchemy.orm import selectinload
from pydantic import BaseModel, Field

from models.e_phy import Product, ProductUse, Crop, Target
from .types import DataScope, SearchConfidence, DataCompleteness


class AuthorizedUsage(BaseModel):
    """Individual authorized usage details."""
    usage_id: int
    target_name: str
    target_type: Optional[str] = None
    min_dose: Optional[float] = None
    max_dose: Optional[float] = None
    dose_unit: Optional[str] = None
    harvest_interval_days: Optional[int] = None
    max_applications: Optional[int] = None
    application_comments: Optional[str] = None
    data_completeness: float = Field(ge=0.0, le=1.0)


class CropAuthorizationDetails(BaseModel):
    """Authorization details for a specific crop."""
    crop_id: int
    crop_name: str
    crop_category: Optional[str] = None
    is_authorized: bool
    usage_count: int = 0
    authorized_targets: List[str] = Field(default_factory=list)
    usage_details: List[AuthorizedUsage] = Field(default_factory=list)
    data_quality_score: float = Field(ge=0.0, le=1.0)


class ProductAuthorizationResult(BaseModel):
    """Unified response for product authorization queries."""
    # Query processing
    query_type: str = Field(description="Type of query processed: 'all_crops' or 'specific_crop'")
    product_found: bool
    crop_found: Optional[bool] = None  # Only relevant for specific crop queries
    
    # Product information
    product_id: Optional[int] = None
    product_name: Optional[str] = None
    registration_number: Optional[str] = None
    is_currently_authorized: Optional[bool] = None
    authorization_status: Optional[str] = None
    function_category: Optional[str] = None
    
    # Authorization results
    crop_authorizations: List[CropAuthorizationDetails] = Field(default_factory=list)
    total_authorized_crops: int = 0
    total_authorized_uses: int = 0
    
    # Summary insights
    primary_function: Optional[str] = None
    most_common_targets: List[str] = Field(default_factory=list)
    crop_categories_covered: List[str] = Field(default_factory=list)
    
    # Quality and confidence
    search_confidence: float = Field(ge=0.0, le=1.0)
    overall_data_quality: float = Field(ge=0.0, le=1.0)
    
    # User guidance
    suggestions: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)


async def get_product_authorization_info(
    session: AsyncSession,
    product_query: str,
    crop_query: Optional[str] = None,
    include_usage_details: bool = True,
    limit_crops: int = 50,
    only_current_authorizations: bool = True
) -> ProductAuthorizationResult:
    """
    get product authorization information.
    
    Args:
        product_query: Product name, registration number, or partial match
        crop_query: Optional specific crop name (if None, returns all authorized crops)
        include_usage_details: Whether to include detailed usage information
        limit_crops: Maximum number of crops to return (for 'all_crops' queries)
        only_current_authorizations: Whether to include only current authorizations
        
    Returns:
        ProductAuthorizationResult with appropriate data based on query type
        
    Examples:
        # "For which crops is ALLOWIN QUATRO authorized?"
        get_product_authorization_info("ALLOWIN QUATRO")
        
        # "Is ALLOWIN QUATRO authorized for potato?"
        get_product_authorization_info("ALLOWIN QUATRO", "potato")
    """
    
    query_type = "specific_crop" if crop_query else "all_crops"
    
    # Step 1: Find the product with intelligent matching
    product_search_filters = [
        Product.product_name.ilike(f"%{product_query}%"),
        Product.alternative_names.ilike(f"%{product_query}%"),
        Product.registration_number == product_query
    ]
    
    product_search_stmt = select(Product).where(or_(*product_search_filters))
    
    if only_current_authorizations:
        # First try current products
        current_stmt = product_search_stmt.where(Product.is_currently_authorized == True)
        result = await session.execute(current_stmt)
        products = result.scalars().all()
        
        if not products:
            # Fallback to all products if no current authorization
            result = await session.execute(product_search_stmt)
            products = result.scalars().all()
    else:
        result = await session.execute(product_search_stmt)
        products = result.scalars().all()
    
    if not products:
        return ProductAuthorizationResult(
            query_type=query_type,
            product_found=False,
            crop_found=crop_query is not None,  # We can't check crop if no product found
            search_confidence=0.0,
            overall_data_quality=0.0,
            suggestions=[
                "Please check the product name spelling",
                "Try using the registration number instead",
                "Verify the product is in the E-Phy database"
            ]
        )
    
    # Take the best match (exact name match preferred, then first result)
    product = None
    search_confidence = 0.0
    
    for p in products:
        if p.product_name.lower() == product_query.lower():
            product = p
            search_confidence = 1.0
            break
        elif product_query.lower() in p.product_name.lower():
            product = p
            search_confidence = 0.9
            break
    
    if not product:
        product = products[0]
        search_confidence = 0.7
    
    # Step 2: Handle crop query if specified
    crop_found = True
    target_crops = []
    
    if crop_query:
        crop_search_stmt = select(Crop).where(
            or_(
                Crop.crop_name.ilike(f"%{crop_query}%"),
                Crop.normalized_name.ilike(f"%{crop_query.lower()}%"),
                Crop.common_synonyms.ilike(f"%{crop_query}%")
            )
        )
        
        crop_result = await session.execute(crop_search_stmt)
        target_crops = crop_result.scalars().all()
        crop_found = len(target_crops) > 0
        
        if not crop_found:
            return ProductAuthorizationResult(
                query_type=query_type,
                product_found=True,
                crop_found=False,
                product_id=product.id,
                product_name=product.product_name,
                registration_number=product.registration_number,
                is_currently_authorized=product.is_currently_authorized,
                search_confidence=search_confidence * 0.5,  # Reduce confidence if crop not found
                overall_data_quality=0.0,
                suggestions=[
                    f"Product '{product.product_name}' found but crop '{crop_query}' not recognized",
                    "Please check the crop name spelling",
                    "Try using alternative crop names (e.g., 'potato' or 'pomme de terre')"
                ]
            )
        
        # For specific crop queries, take the best crop match
        if len(target_crops) > 1:
            # Prefer exact matches
            for c in target_crops:
                if c.crop_name.lower() == crop_query.lower():
                    target_crops = [c]
                    break
            else:
                target_crops = [target_crops[0]]  # Take first if no exact match
    
    # Step 3: Get authorization data
    base_query = select(
        ProductUse,
        Crop.id.label('crop_id'),
        Crop.crop_name,
        Crop.crop_category,
        Target.target_name,
        Target.target_type
    ).select_from(
        ProductUse.__table__
        .join(Crop.__table__)
        .join(Target.__table__)
    ).where(
        ProductUse.product_id == product.id
    )
    
    if only_current_authorizations:
        base_query = base_query.where(ProductUse.is_currently_authorized == True)
    
    if target_crops:
        # Specific crop query
        crop_ids = [c.id for c in target_crops]
        base_query = base_query.where(Crop.id.in_(crop_ids))
    
    base_query = base_query.order_by(Crop.crop_name, Target.target_name)
    
    if query_type == "all_crops":
        base_query = base_query.limit(limit_crops * 10)  # Generous limit for processing
    
    usage_result = await session.execute(base_query)
    usage_data = usage_result.all()
    
    # Step 4: Process and organize results
    crop_auth_dict = {}
    all_targets = []
    all_categories = set()
    total_uses = 0
    
    for row in usage_data:
        product_use, crop_id, crop_name, crop_category, target_name, target_type = row
        
        if crop_id not in crop_auth_dict:
            crop_auth_dict[crop_id] = {
                'crop_id': crop_id,
                'crop_name': crop_name,
                'crop_category': crop_category,
                'usage_details': [],
                'targets': set()
            }
        
        # Calculate data completeness for this usage
        completeness = 0.0
        fields_checked = 0
        
        if product_use.min_dose is not None:
            completeness += 0.25
        if product_use.harvest_interval_days is not None:
            completeness += 0.25
        if product_use.max_applications is not None:
            completeness += 0.25
        if product_use.application_comments:
            completeness += 0.25
        
        usage_detail = AuthorizedUsage(
            usage_id=product_use.id,
            target_name=target_name,
            target_type=target_type,
            min_dose=product_use.min_dose,
            max_dose=product_use.max_dose,
            dose_unit=product_use.dose_unit,
            harvest_interval_days=product_use.harvest_interval_days,
            max_applications=product_use.max_applications,
            application_comments=product_use.application_comments,
            data_completeness=completeness
        )
        
        if include_usage_details:
            crop_auth_dict[crop_id]['usage_details'].append(usage_detail)
        
        crop_auth_dict[crop_id]['targets'].add(target_name)
        all_targets.append(target_name)
        
        if crop_category:
            all_categories.add(crop_category)
        
        total_uses += 1
    
    # Step 5: Create final crop authorization objects
    crop_authorizations = []
    total_data_scores = []
    
    for crop_data in crop_auth_dict.values():
        usage_details = crop_data['usage_details'] if include_usage_details else []
        
        # Calculate average data quality for this crop
        if usage_details:
            avg_completeness = sum(ud.data_completeness for ud in usage_details) / len(usage_details)
        else:
            avg_completeness = 0.5  # Default if no details
        
        total_data_scores.append(avg_completeness)
        
        crop_auth = CropAuthorizationDetails(
            crop_id=crop_data['crop_id'],
            crop_name=crop_data['crop_name'],
            crop_category=crop_data['crop_category'],
            is_authorized=True,
            usage_count=len(usage_details),
            authorized_targets=list(crop_data['targets']),
            usage_details=usage_details,
            data_quality_score=avg_completeness
        )
        crop_authorizations.append(crop_auth)
    
    # Limit results for "all_crops" queries
    if query_type == "all_crops" and len(crop_authorizations) > limit_crops:
        # Sort by usage count (most uses first) and take top N
        crop_authorizations.sort(key=lambda x: x.usage_count, reverse=True)
        crop_authorizations = crop_authorizations[:limit_crops]
    
    # Step 6: Generate summary insights
    from collections import Counter
    target_counts = Counter(all_targets)
    most_common_targets = [target for target, count in target_counts.most_common(5)]
    
    overall_data_quality = sum(total_data_scores) / len(total_data_scores) if total_data_scores else 0.0
    
    # Generate suggestions and warnings
    suggestions = []
    warnings = []
    
    if query_type == "specific_crop" and crop_authorizations:
        crop_auth = crop_authorizations[0]
        suggestions.append(f"Product is authorized for {crop_auth.usage_count} different targets on {crop_auth.crop_name}")
        if crop_auth.data_quality_score < 0.7:
            warnings.append("Some application details may be incomplete. Please verify with product label.")
    
    elif query_type == "specific_crop" and not crop_authorizations:
        suggestions.append(f"Product '{product.product_name}' is not currently authorized for '{crop_query}'")
        if target_crops:
            crop_name = target_crops[0].crop_name
            # Find similar crops in same category
            if target_crops[0].crop_category:
                alt_stmt = select(Crop.crop_name).where(
                    and_(
                        Crop.crop_category == target_crops[0].crop_category,
                        Crop.id != target_crops[0].id
                    )
                ).limit(3)
                alt_result = await session.execute(alt_stmt)
                alternatives = [alt[0] for alt in alt_result.all()]
                if alternatives:
                    suggestions.append(f"Consider checking similar crops: {', '.join(alternatives[:2])}")
    
    elif query_type == "all_crops":
        if crop_authorizations:
            suggestions.append(f"Product is authorized for {len(crop_authorizations)} different crops")
            if len(crop_authorizations) >= limit_crops:
                suggestions.append(f"Showing top {limit_crops} crops. There may be additional authorizations.")
        else:
            warnings.append("No current crop authorizations found for this product")
    
    if not product.is_currently_authorized:
        warnings.append("⚠️ This product is not currently authorized and may be withdrawn from the market")
    
    return ProductAuthorizationResult(
        query_type=query_type,
        product_found=True,
        crop_found=crop_found if crop_query else None,
        product_id=product.id,
        product_name=product.product_name,
        registration_number=product.registration_number,
        is_currently_authorized=product.is_currently_authorized,
        authorization_status=product.authorization_status,
        function_category=product.function_category,
        crop_authorizations=crop_authorizations,
        total_authorized_crops=len(crop_authorizations),
        total_authorized_uses=total_uses,
        primary_function=product.function_category,
        most_common_targets=most_common_targets,
        crop_categories_covered=list(all_categories),
        search_confidence=search_confidence,
        overall_data_quality=overall_data_quality,
        suggestions=suggestions,
        warnings=warnings
    )