from datetime import datetime

import logfire
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from routes.assistant import router as assistant_router
from routes.automation import router as automation_router
from routes.pipeline import router as pipeline_router
from routes.tools import router as tools_router
from routes.knowledge_base import router as knowledge_base_router

logfire.configure(scrubbing=False)
logfire.instrument_httpx(capture_all=True)
logfire.instrument_pydantic_ai()
# Initialize FastAPI app
app = FastAPI(title="Agri AI Agent")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
app.include_router(router=assistant_router)
app.include_router(prefix="/api", router=tools_router)
app.include_router(prefix="/api", router=automation_router)
app.include_router(prefix="/api", router=pipeline_router)
app.include_router(prefix="/api", router=knowledge_base_router)

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
