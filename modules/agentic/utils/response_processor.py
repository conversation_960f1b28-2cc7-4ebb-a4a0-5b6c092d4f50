import asyncio
import datetime
import j<PERSON>
from typing import List, Optional

import orjson
from pydantic_ai.messages import TextPart, ModelResponse

from const import EChatEventType, EConnectionState
from schemas.assistant import SChatEvent, SMetricContent, SChartContent, SChartData, SChartDataset, \
    STableContent, SConnectionContent
from utils.logger import logger


class ResponseProcessor:
    """Simplified response processor focused on JSON parsing and validation"""

    def __init__(self, suppress_error_events: bool = False):
        self.response_queue = asyncio.Queue(maxsize=100)  # Limit queue size to prevent memory issues
        self.text_buffer = ""
        self.full_response = ""
        self.response = ""  # Alias for backward compatibility
        self.error_occurred = False
        self.completed = False
        self.successful_events: List[SChatEvent] = []
        self.successful_messages = []  # For retry compatibility
        self.suppress_error_events = suppress_error_events
        self._processing_lock = asyncio.Lock()  # Prevent concurrent processing

    async def add_event(self, event: SChatEvent):
        """Add event to queue with race condition protection"""
        try:
            # Use timeout to prevent indefinite blocking
            await asyncio.wait_for(self.response_queue.put(event), timeout=5.0)
        except asyncio.TimeoutError:
            logger.warning("Event queue timeout - dropping event to prevent deadlock")
        except Exception as e:
            logger.error(f"Failed to add event to queue: {str(e)}")

    async def process_chunk(self, chunk: str):
        """Process streaming chunk from LLM with race condition protection"""
        async with self._processing_lock:
            try:
                # Validate chunk size to prevent memory issues
                if len(chunk) > 50000:  # 50KB limit per chunk
                    logger.warning(f"Large chunk received ({len(chunk)} chars), truncating")
                    chunk = chunk[:50000]

                self.text_buffer += chunk
                self.full_response += chunk
                self.response += chunk  # Keep both in sync

                # Check for early format errors
                if self._should_flag_early_error():
                    self.error_occurred = True
                    if not self.suppress_error_events:
                        await self._emit_error("Response does not follow required JSON Lines format")
                    return

                # Process complete JSON objects
                await self._extract_and_process_json()

            except Exception as e:
                logger.error(f"Error processing chunk: {str(e)}")
                self.error_occurred = True

    async def finalize(self):
        """Process remaining buffer content and finalize"""
        if self.text_buffer.strip() and not self.error_occurred:
            # Try to process remaining content
            if not await self._process_json_object(self.text_buffer.strip()):
                self.error_occurred = True
                if not self.suppress_error_events:
                    await self._emit_error("Invalid JSON format in remaining content")

        self.text_buffer = ""

        if not self.error_occurred:
            self.completed = True
            await self.add_event(self.create_connection_event(
                EConnectionState.completed, "Processing completed"
            ))

    def _should_flag_early_error(self) -> bool:
        """Check for early format errors"""
        stripped = self.text_buffer.strip()

        if not stripped:
            return False

        # Check for substantial content without JSON marker
        if len(stripped) > 50 and '{"type"' not in stripped:
            logger.debug(f"Early error: No JSON marker in {len(stripped)} chars")
            return True

        # Check for natural language patterns
        natural_patterns = [
            stripped.startswith(p) for p in [
                'I ', 'The ', 'This ', 'Here ', 'Based on ', 'According to ',
                'Let me ', 'Sorry, ', 'Please ', 'However, ', 'Unfortunately, '
            ]
        ]

        if any(natural_patterns):
            logger.debug(f"Early error: Natural language detected: {stripped[:50]}...")
            return True

        # Check for excessive non-JSON prefix
        json_start = stripped.find('{"type"')
        if json_start > 30:
            logger.debug(f"Early error: {json_start} chars before JSON")
            return True

        return False

    async def _extract_and_process_json(self):
        """Extract and process complete JSON objects from buffer"""
        while True:
            json_obj, remaining = self._extract_next_complete_json(self.text_buffer)
            if json_obj is None:
                break

            if await self._process_json_object(json_obj):
                self.text_buffer = remaining
            else:
                self.error_occurred = True
                if not self.suppress_error_events:
                    await self._emit_error("Invalid JSON object format")
                break

    def _extract_next_complete_json(self, text: str) -> tuple[Optional[str], str]:
        """Extract next complete JSON object using brace counting"""
        if not text.strip():
            return None, text

        # Find JSON start
        start_idx = text.find('{"type"')
        if start_idx == -1:
            # Clear large buffer without JSON
            if len(text) > 100:
                logger.debug("Clearing large buffer without JSON")
                return None, ""
            return None, text

        # Log skipped prefix if any
        if start_idx > 0:
            skipped = text[:start_idx].strip()
            if skipped:
                logger.debug(f"Skipping prefix: {skipped[:30]}...")

        # Extract complete JSON using brace counting
        json_text = text[start_idx:]
        brace_count = 0
        in_string = False
        escape_next = False

        for i, char in enumerate(json_text):
            if escape_next:
                escape_next = False
                continue

            if char == '\\':
                escape_next = True
                continue

            if char == '"' and not escape_next:
                in_string = not in_string
                continue

            if not in_string:
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        complete_json = json_text[:i + 1]
                        remaining = text[start_idx + i + 1:]
                        return complete_json, remaining

        return None, text

    async def _process_json_object(self, json_str: str) -> bool:
        """Parse and validate JSON object against templates"""
        try:
            data = orjson.loads(json_str)

            if not self._validate_template(data):
                logger.error(f"Invalid template: {json_str[:100]}")
                return False

            event = self._create_event_from_data(data)
            if event and not self._is_duplicate(event):
                await self.add_event(event)
                self.successful_events.append(event)

            return True

        except (json.JSONDecodeError, Exception) as e:
            logger.error(f"JSON processing error: {str(e)} for: {json_str[:100]}")
            return False

    def _validate_template(self, data: dict) -> bool:
        """Validate JSON against required templates"""
        event_type = data.get("type")
        content = data.get("content")

        if event_type == "markdown":
            return isinstance(content, str) and len(content.strip()) > 0

        elif event_type == "metric":
            return (isinstance(content, dict) and
                    isinstance(content.get("label"), str) and
                    isinstance(content.get("value"), (int, float, str)))

        elif event_type == "chart":
            if not isinstance(content, dict):
                return False
            chart_data = content.get("data", {})
            datasets = chart_data.get("datasets", [])
            return (isinstance(content.get("chartType"), str) and
                    isinstance(content.get("title"), str) and
                    isinstance(chart_data.get("labels"), list) and
                    isinstance(datasets, list) and len(datasets) > 0)

        elif event_type == "table":
            if not isinstance(content, dict):
                return False
            headers = content.get("headers", [])
            rows = content.get("rows", [])
            return (isinstance(headers, list) and len(headers) > 0 and
                    isinstance(rows, list) and len(rows) > 0 and
                    all(isinstance(row, list) and len(row) == len(headers) for row in rows))

        return False

    def _create_event_from_data(self, data: dict) -> Optional[SChatEvent]:
        """Create SChatEvent from validated data"""
        try:
            event_type = EChatEventType(data["type"])
            content = data["content"]

            if event_type == EChatEventType.markdown:
                return SChatEvent(type=event_type, content=content)

            elif event_type == EChatEventType.metric:
                return SChatEvent(
                    type=event_type,
                    content=SMetricContent(
                        label=content["label"],
                        value=content["value"],
                        unit=content.get("unit")
                    )
                )

            elif event_type == EChatEventType.chart:
                chart_data = content["data"]
                return SChatEvent(
                    type=event_type,
                    content=SChartContent(
                        chartType=content["chartType"],
                        title=content["title"],
                        data=SChartData(
                            labels=chart_data["labels"],
                            datasets=[
                                SChartDataset(
                                    label=ds["label"],
                                    data=ds["data"],
                                    backgroundColor=ds.get("backgroundColor"),
                                    borderColor=ds.get("borderColor")
                                ) for ds in chart_data["datasets"]
                            ]
                        ),
                        xAxisLabel=content.get("xAxisLabel"),
                        yAxisLabel=content.get("yAxisLabel")
                    )
                )

            elif event_type == EChatEventType.table:
                return SChatEvent(
                    type=event_type,
                    content=STableContent(
                        headers=content["headers"],
                        rows=content["rows"]
                    )
                )

        except Exception as e:
            logger.error(f"Error creating event: {str(e)}")

        return None

    def _is_duplicate(self, event: SChatEvent) -> bool:
        """Check for duplicate events"""
        for existing in self.successful_events:
            if existing.type == event.type:
                if event.type == EChatEventType.markdown and existing.content == event.content:
                    return True
                elif (event.type == EChatEventType.table and
                      hasattr(existing.content, 'headers') and
                      hasattr(event.content, 'headers') and
                      existing.content.headers == event.content.headers and
                      existing.content.rows == event.content.rows):
                    return True
        return False

    async def _emit_error(self, message: str):
        """Emit error event"""
        await self.add_event(self.create_connection_event(EConnectionState.error, message))

    @staticmethod
    def create_connection_event(state: EConnectionState, message: str = None) -> SChatEvent:
        """Create connection event"""
        return SChatEvent(
            type=EChatEventType.connection,
            content=SConnectionContent(
                state=state,
                message=message or state.value
            )
        )

    def create_partial_response_message(self):
        """Create partial response for retry continuation"""
        if not self.successful_events:
            return None

        # Convert successful events to JSON lines
        response_lines = []
        for event in self.successful_events:
            if event.type == EChatEventType.markdown:
                response_lines.append(json.dumps({
                    "type": "markdown",
                    "content": str(event.content)
                }))
            elif event.type == EChatEventType.metric:
                metric_data = {
                    "type": "metric",
                    "content": {
                        "label": event.content.label,
                        "value": event.content.value
                    }
                }
                if event.content.unit:
                    metric_data["content"]["unit"] = event.content.unit
                response_lines.append(json.dumps(metric_data))
            # Add other event types as needed

        if response_lines:
            response_content = "\n".join(response_lines)
            logger.info(f"Created partial response with {len(response_lines)} JSON lines")

            from pydantic_ai.messages import Usage
            return ModelResponse(
                parts=[TextPart(response_content)],
                timestamp=datetime.datetime.now(),
                usage=Usage(requests=0, request_tokens=0, response_tokens=len(response_lines))
            )

        return None

    async def cleanup(self):
        """Clean up resources and clear queues to prevent memory leaks"""
        try:
            # Clear event queue
            while not self.response_queue.empty():
                try:
                    self.response_queue.get_nowait()
                except asyncio.QueueEmpty:
                    break

            # Clear buffers and events
            self.text_buffer = ""
            self.full_response = ""
            self.response = ""
            self.successful_events.clear()
            self.successful_messages.clear()

            logger.debug("ResponseProcessor cleanup completed")

        except Exception as e:
            logger.error(f"Error during cleanup: {str(e)}")

    def __del__(self):
        """Destructor to ensure cleanup on garbage collection"""
        try:
            # Best effort cleanup for synchronous destruction
            if hasattr(self, 'text_buffer'):
                self.text_buffer = ""
            if hasattr(self, 'successful_events'):
                self.successful_events.clear()
        except Exception:
            pass  # Ignore errors during destruction
