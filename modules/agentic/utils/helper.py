from typing import Union

from pydantic_ai import RunContext
from pydantic_ai.tools import ToolDefinition

from schemas.agent import SAgentDeps
from schemas.assistant import SChatRequest
from utils.logger import logger


async def is_advisor(
        ctx: RunContext[SAgentDeps[SChatRequest]], tool_def: ToolDefinition
) -> Union[ToolDefinition, None]:
    if ctx.deps.data.personal_context and (ctx.deps.data.personal_context.email in [
        '<EMAIL>'] or ctx.deps.data.personal_context.role == 'advisor'):
        # logger.debug(f"Advisor: {ctx.deps.data.personal_context}")
        return tool_def


async def is_farmer(
        ctx: RunContext[SAgentDeps[SChatRequest]], tool_def: ToolDefinition
) -> Union[ToolDefinition, None]:
    if ctx.deps.data.personal_context and (
            ctx.deps.data.personal_context.email in ['<EMAIL>', '<EMAIL>',
                                                     '<EMAIL>'] or ctx.deps.data.personal_context.role == 'farmer'):
        # logger.debug(f"Farmer: {ctx.deps.data.personal_context}")
        return tool_def
