import asyncio
from typing import List, Callable, TypeVar, Generic, Optional
from dataclasses import dataclass

T = TypeVar('T')
R = TypeVar('R')


@dataclass
class BatchConfig:
    """Configuration for batch processing."""
    batch_size: int
    max_concurrent_batches: int = 3
    delay_between_batches: float = 0.1


class BatchProcessor(Generic[T, R]):
    """Generic batch processor for handling large datasets efficiently."""
    
    def __init__(self, config: BatchConfig):
        self.config = config
        self.semaphore = asyncio.Semaphore(config.max_concurrent_batches)
    
    async def process_batch(
        self, 
        batch: List[T], 
        processor: Callable[[List[T]], R]
    ) -> R:
        """Process a single batch with concurrency control."""
        async with self.semaphore:
            if asyncio.iscoroutinefunction(processor):
                result = await processor(batch)
            else:
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(None, processor, batch)
            
            # Add delay to prevent rate limiting
            if self.config.delay_between_batches > 0:
                await asyncio.sleep(self.config.delay_between_batches)
            
            return result
    
    async def process_all(
        self, 
        items: List[T], 
        processor: Callable[[List[T]], R],
        flatten_results: bool = True
    ) -> List[R]:
        """Process all items in batches."""
        if not items:
            return []
        
        # Create batches
        batches = [
            items[i:i + self.config.batch_size] 
            for i in range(0, len(items), self.config.batch_size)
        ]
        
        # Process batches concurrently
        tasks = [
            self.process_batch(batch, processor) 
            for batch in batches
        ]
        
        results = await asyncio.gather(*tasks)
        
        if flatten_results and results:
            # Flatten if results are lists
            if isinstance(results[0], list):
                flattened = []
                for result in results:
                    flattened.extend(result)
                return flattened
        
        return results
    
    def create_batches(self, items: List[T]) -> List[List[T]]:
        """Create batches from items list."""
        return [
            items[i:i + self.config.batch_size] 
            for i in range(0, len(items), self.config.batch_size)
        ]


class EmbeddingBatchProcessor(BatchProcessor[str, List[List[float]]]):
    """Specialized batch processor for embedding operations."""
    
    def __init__(self, batch_size: int, max_concurrent_batches: int = 3):
        config = BatchConfig(
            batch_size=batch_size,
            max_concurrent_batches=max_concurrent_batches,
            delay_between_batches=0.1
        )
        super().__init__(config)
    
    async def embed_texts_batched(
        self, 
        texts: List[str], 
        embed_function: Callable[[List[str]], List[List[float]]]
    ) -> List[List[float]]:
        """Embed texts in batches and return flattened results."""
        return await self.process_all(
            texts, 
            embed_function, 
            flatten_results=True
        )