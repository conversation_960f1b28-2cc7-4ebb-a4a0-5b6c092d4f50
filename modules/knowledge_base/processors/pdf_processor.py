"""
PDF document processor with memory management and layout-aware extraction.
Handles large documents efficiently with streaming and OCR fallback.
"""
import asyncio
import io
import time
from contextlib import contextmanager
from typing import List, Dict, Any, Optional
from uuid import UUID

import psutil

try:
    import fitz  # PyMuPDF

    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False
    fitz = None

try:
    import pytesseract
    from PIL import Image

    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
    pytesseract = None
    Image = None

from modules.knowledge_base.processors.base_processor import BaseDocumentProcessor, ProcessingResult
from modules.knowledge_base.processors.image_processor import ImageProcessor
from utils.logger import logger
from uuid import uuid4


class PDFProcessor(BaseDocumentProcessor):
    """Processor for PDF documents with memory management and layout extraction."""

    def __init__(self):
        super().__init__()
        self.supported_extensions = {'.pdf'}

        # Processing limits
        self.max_memory_mb = 512
        self.max_file_size_mb = 100
        self.processing_timeout = 1200  # 20 minutes
        self.chunk_size_pages = 50  # Process in chunks

        # Initialize enhanced ImageProcessor for embedded images
        self.image_processor = ImageProcessor()

        if not PYMUPDF_AVAILABLE:
            logger.warning("PyMuPDF not available. PDF processing will be limited.")
        if not OCR_AVAILABLE:
            logger.warning("OCR not available. Image-only PDFs cannot be processed.")

    async def process(self, file_content: bytes, document_id: UUID) -> ProcessingResult:
        """
        Process PDF with memory management, layout extraction, and OCR fallback.
        
        Args:
            file_content: PDF file content as bytes
            document_id: Document ID for reference
            
        Returns:
            ProcessingResult with extracted text and metadata
        """
        if not PYMUPDF_AVAILABLE:
            return ProcessingResult(
                success=False,
                error="PyMuPDF library not available for PDF processing"
            )

        # Check file size
        file_size_mb = len(file_content) / (1024 * 1024)
        if file_size_mb > self.max_file_size_mb:
            return ProcessingResult(
                success=False,
                error=f"PDF file too large: {file_size_mb:.1f}MB (max: {self.max_file_size_mb}MB)"
            )

        start_time = time.time()
        pdf_document = None

        try:
            # Process with timeout
            result = await asyncio.wait_for(
                self._process_with_memory_management(file_content, document_id),
                timeout=self.processing_timeout
            )

            processing_time = time.time() - start_time
            if result.metadata:
                result.metadata['processing_time_seconds'] = processing_time

            return result

        except asyncio.TimeoutError:
            return ProcessingResult(
                success=False,
                error=f"PDF processing timeout after {self.processing_timeout} seconds"
            )
        except Exception as e:
            logger.error(f"Error processing PDF document {document_id}: {e}")
            return ProcessingResult(
                success=False,
                error=f"PDF processing error: {str(e)}"
            )
        finally:
            # Ensure cleanup
            if pdf_document:
                try:
                    pdf_document.close()
                except:
                    pass

    async def _process_with_memory_management(self, file_content: bytes, document_id: UUID) -> ProcessingResult:
        """Process PDF with memory monitoring and streaming approach."""
        with self._memory_monitor():
            pdf_document = fitz.open(stream=file_content, filetype="pdf")

            try:
                total_pages = len(pdf_document)
                if total_pages > 1000:  # Arbitrary large document threshold
                    logger.warning(f"Large PDF detected: {total_pages} pages. Processing in chunks.")

                # Initialize extraction variables
                full_text = ""
                pages_text = []
                tables_extracted = []
                ocr_pages_count = 0

                # Process pages in chunks to manage memory
                for chunk_start in range(0, total_pages, self.chunk_size_pages):
                    chunk_end = min(chunk_start + self.chunk_size_pages, total_pages)

                    chunk_text, chunk_pages, chunk_tables, chunk_ocr = await self._process_page_chunk(
                        pdf_document, chunk_start, chunk_end
                    )

                    full_text += chunk_text
                    pages_text.extend(chunk_pages)
                    tables_extracted.extend(chunk_tables)
                    ocr_pages_count += chunk_ocr

                    # Memory check after each chunk
                    if self._get_memory_usage() > self.max_memory_mb:
                        logger.warning(f"Memory usage exceeded {self.max_memory_mb}MB")
                        # break

                # Check if we got any text
                if not full_text.strip():
                    # Try OCR fallback for image-only PDFs
                    if OCR_AVAILABLE:
                        logger.info(f"No text found in PDF {document_id}, attempting OCR")
                        return await self._ocr_fallback(pdf_document, document_id)
                    else:
                        return ProcessingResult(
                            success=False,
                            error="No extractable text found and OCR not available"
                        )

                # Extract metadata
                title = self._extract_title(full_text)
                language = self._detect_language(full_text)
                agricultural_metadata = await self._extract_agricultural_metadata(full_text)

                return ProcessingResult(
                    success=True,
                    content=full_text,
                    title=title,
                    language=language,
                    total_pages=total_pages,
                    crop_categories=agricultural_metadata.get('crop_categories', []),
                    topics=agricultural_metadata.get('topics', []),
                    geographic_scope=agricultural_metadata.get('geographic_scope', []),
                    seasonal_relevance=agricultural_metadata.get('seasonal_relevance', []),
                    metadata={
                        'pages_text': pages_text,
                        'tables_extracted': len(tables_extracted),
                        'ocr_pages': ocr_pages_count,
                        'processor': 'EnhancedPDFProcessor',
                        'extraction_method': 'PyMuPDF+Layout+OCR'
                    }
                )

            finally:
                pdf_document.close()

    async def _process_page_chunk(self, pdf_document, start_page: int, end_page: int) -> tuple:
        """Process a chunk of pages with layout-aware extraction."""
        chunk_text = ""
        chunk_pages = []
        chunk_tables = []
        ocr_count = 0

        for page_num in range(start_page, end_page):
            page = pdf_document.load_page(page_num)

            # Try standard text extraction first
            page_text = page.get_text()

            # Enhanced layout-aware extraction
            if page_text.strip():
                # Extract with layout preservation
                layout_text = self._extract_with_layout(page)
                tables = self._extract_tables(page)

                # Also extract text from images on the same page
                image_text = ""
                if OCR_AVAILABLE:
                    image_text = await self._extract_image_text(page)

                # Combine all text sources
                combined_text = layout_text
                if image_text:
                    combined_text += f"\n\n[Image Content]\n{image_text}"

                cleaned_text = self._clean_text(combined_text)
                if cleaned_text:
                    page_info = {
                        'page_number': page_num + 1,
                        'text': cleaned_text,
                        'tables': len(tables)
                    }
                    if image_text:
                        page_info['has_image_text'] = True
                        page_info['image_text_length'] = len(image_text)

                    chunk_pages.append(page_info)
                    chunk_text += f"\n\n--- Page {page_num + 1} ---\n\n{cleaned_text}"
                    chunk_tables.extend(tables)
            else:
                # Try OCR for pages with no extractable text
                if OCR_AVAILABLE:
                    ocr_text = await self._ocr_page(page)
                    if ocr_text:
                        cleaned_text = self._clean_text(ocr_text)
                        chunk_pages.append({
                            'page_number': page_num + 1,
                            'text': cleaned_text,
                            'extraction_method': 'OCR'
                        })
                        chunk_text += f"\n\n--- Page {page_num + 1} (OCR) ---\n\n{cleaned_text}"
                        ocr_count += 1

        return chunk_text, chunk_pages, chunk_tables, ocr_count

    def _extract_with_layout(self, page) -> str:
        """Extract text with layout preservation."""
        # Get text with layout information
        text_dict = page.get_text("dict")

        # Process blocks to preserve structure
        blocks = []
        for block in text_dict["blocks"]:
            if "lines" in block:  # Text block
                block_text = ""
                for line in block["lines"]:
                    line_text = ""
                    for span in line["spans"]:
                        line_text += span["text"]
                    if line_text.strip():
                        block_text += line_text + "\n"

                if block_text.strip():
                    blocks.append(block_text.strip())

        return "\n\n".join(blocks)

    def _extract_tables(self, page) -> List[Dict[str, Any]]:
        """Extract table information from page."""
        tables = []
        try:
            # Find tables using PyMuPDF
            tabs = page.find_tables()
            for tab in tabs:
                table_data = tab.extract()
                if table_data and len(table_data) > 1:  # At least header + 1 row
                    tables.append({
                        'bbox': tab.bbox,
                        'rows': len(table_data),
                        'cols': len(table_data[0]) if table_data else 0,
                        'data': table_data[:5]  # First 5 rows for metadata
                    })
        except Exception as e:
            logger.debug(f"Table extraction failed: {e}")

        return tables

    async def _extract_image_text(self, page) -> str:
        """Extract content from images embedded in the page using enhanced ImageProcessor."""
        image_analyses = []
        try:
            # Get all image blocks from the page
            image_list = page.get_images()

            for img_index, img in enumerate(image_list):
                pix = None
                try:
                    # Get image data
                    xref = img[0]
                    pix = fitz.Pixmap(page.parent, xref)

                    # Skip if image is too small (likely decorative)
                    if pix.width < 100 or pix.height < 50:
                        continue

                    # Convert pixmap to proper format for PNG output
                    # Always convert to RGB for consistency and PNG compatibility
                    try:
                        if pix.colorspace and pix.colorspace.name in ['DeviceCMYK', 'DeviceLAB']:
                            # Convert CMYK/LAB to RGB
                            rgb_pix = fitz.Pixmap(fitz.csRGB, pix)
                            pix = rgb_pix
                        elif pix.n > 4:  # Other complex colorspaces
                            # Convert to RGB
                            rgb_pix = fitz.Pixmap(fitz.csRGB, pix)
                            pix = rgb_pix
                    except Exception as e:
                        logger.debug(f"Failed to convert colorspace for image {img_index}: {e}")
                        # Try to force conversion to RGB
                        try:
                            rgb_pix = fitz.Pixmap(fitz.csRGB, pix)
                            pix = rgb_pix
                        except Exception as e2:
                            logger.debug(f"Failed to force RGB conversion for image {img_index}: {e2}")
                            continue

                    # Ensure we have a valid format for PNG (RGB or Grayscale)
                    if pix.n not in [1, 3, 4]:  # Not grayscale, RGB, or RGBA
                        logger.debug(f"Skipping image {img_index}: unsupported format (n={pix.n})")
                        continue

                    # Convert to bytes for ImageProcessor
                    try:
                        img_data = pix.tobytes("png")
                    except Exception as e:
                        logger.debug(f"Failed to convert image {img_index} to PNG: {e}")
                        continue

                    # Use enhanced ImageProcessor for intelligent analysis
                    image_analysis = await self._analyze_embedded_image(
                        img_data, img_index, page_context=f"PDF page content"
                    )

                    if image_analysis:
                        image_analyses.append(image_analysis)

                except Exception as e:
                    logger.debug(f"Failed to analyze embedded image {img_index}: {e}")
                    continue
                finally:
                    # Always clean up pixmap
                    if pix:
                        pix = None

        except Exception as e:
            logger.debug(f"Embedded image analysis failed for page: {e}")

        return "\n\n".join(image_analyses) if image_analyses else ""

    async def _analyze_embedded_image(self, img_data: bytes, img_index: int, page_context: str) -> Optional[str]:
        """Analyze embedded image using enhanced ImageProcessor with GPT-4V and OCR fallback."""
        try:
            # Create a temporary document ID for the embedded image
            temp_doc_id = uuid4()

            # Use the enhanced ImageProcessor (which already has OCR fallback built-in)
            result = await self.image_processor.process(img_data, temp_doc_id)

            if result.success:
                # Format the analysis for PDF context
                analysis_parts = []

                # Add title if available
                if result.title:
                    analysis_parts.append(f"[Embedded Image {img_index + 1}]: {result.title}")

                # Initialize variables
                agricultural_info = []
                visible_text = []

                # Add key agricultural content
                if result.content:
                    # Extract agricultural analysis section
                    content_lines = result.content.split('\n')

                    current_section = None
                    for line in content_lines:
                        line = line.strip()
                        if line.startswith('AGRICULTURAL ANALYSIS:'):
                            current_section = 'agricultural'
                            continue
                        elif line.startswith('VISIBLE TEXT:'):
                            current_section = 'text'
                            continue
                        elif line.startswith('IMAGE DESCRIPTION:'):
                            current_section = 'description'
                            continue

                        if current_section == 'agricultural' and line:
                            agricultural_info.append(line)
                        elif current_section == 'text' and line and line != "No visible text":
                            visible_text.append(line)

                # Add the most relevant information
                if agricultural_info:
                    analysis_parts.append(
                        f"Agricultural content: {' '.join(agricultural_info[:2])}")  # Limit to avoid too much text

                if visible_text:
                    analysis_parts.append(f"Text in image: {' '.join(visible_text)}")

                # Add crop/topic metadata if available
                if result.crop_categories:
                    analysis_parts.append(f"Crops: {', '.join(result.crop_categories[:3])}")

                if result.topics:
                    analysis_parts.append(f"Topics: {', '.join(result.topics[:3])}")

                return ' | '.join(analysis_parts) if analysis_parts else None

            return None

        except Exception as e:
            logger.debug(f"Error analyzing embedded image {img_index} with ImageProcessor: {e}")
            return None

    async def _ocr_page(self, page) -> Optional[str]:
        """Perform OCR on a page."""
        if not OCR_AVAILABLE:
            return None

        try:
            # Convert page to image
            pix = page.get_pixmap()
            img_data = pix.tobytes("png")
            image = Image.open(io.BytesIO(img_data))

            # Perform OCR
            text = pytesseract.image_to_string(image, lang='eng')
            return text if text.strip() else None

        except Exception as e:
            logger.debug(f"OCR failed for page: {e}")
            return None

    async def _ocr_fallback(self, pdf_document, document_id: UUID) -> ProcessingResult:
        """Fallback OCR processing for image-only PDFs."""
        logger.info(f"Attempting OCR fallback for PDF {document_id}")

        full_text = ""
        pages_text = []
        total_pages = len(pdf_document)

        # Limit OCR to reasonable number of pages
        max_ocr_pages = min(50, total_pages)

        for page_num in range(max_ocr_pages):
            page = pdf_document.load_page(page_num)
            ocr_text = await self._ocr_page(page)

            if ocr_text:
                cleaned_text = self._clean_text(ocr_text)
                if cleaned_text and len(cleaned_text) > 50:  # Minimum meaningful text
                    pages_text.append({
                        'page_number': page_num + 1,
                        'text': cleaned_text,
                        'extraction_method': 'OCR'
                    })
                    full_text += f"\n\n--- Page {page_num + 1} (OCR) ---\n\n{cleaned_text}"

        if not full_text.strip():
            return ProcessingResult(
                success=False,
                error="No text could be extracted even with OCR"
            )

        # Extract metadata from OCR text
        title = self._extract_title(full_text)
        language = self._detect_language(full_text)
        agricultural_metadata = await self._extract_agricultural_metadata(full_text)

        return ProcessingResult(
            success=True,
            content=full_text,
            title=title,
            language=language,
            total_pages=total_pages,
            crop_categories=agricultural_metadata.get('crop_categories', []),
            topics=agricultural_metadata.get('topics', []),
            geographic_scope=agricultural_metadata.get('geographic_scope', []),
            seasonal_relevance=agricultural_metadata.get('seasonal_relevance', []),
            metadata={
                'pages_text': pages_text,
                'processor': 'EnhancedPDFProcessor',
                'extraction_method': 'OCR_Fallback',
                'ocr_pages': len(pages_text)
            }
        )

    @contextmanager
    def _memory_monitor(self):
        """Context manager for memory monitoring."""
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        try:
            yield
        finally:
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_used = final_memory - initial_memory
            if memory_used > 100:  # Log if significant memory used
                logger.info(f"PDF processing used {memory_used:.1f}MB memory")

    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except:
            return 0.0

    def _clean_text(self, text: str) -> str:
        """Clean extracted text while preserving semantic paragraph boundaries."""
        if not text:
            return ""

        # Remove common PDF artifacts first
        artifacts = [
            '\x0c',  # Form feed character
            '\ufeff',  # BOM
        ]
        
        for artifact in artifacts:
            text = text.replace(artifact, '')

        # Preserve paragraph breaks by detecting semantic boundaries
        lines = text.split('\n')
        cleaned_lines = []
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            if not line:
                # Empty line - check if it should be preserved as paragraph break
                if (i > 0 and i < len(lines) - 1 and 
                    lines[i-1].strip() and lines[i+1].strip()):
                    # Preserve paragraph break between non-empty lines
                    if cleaned_lines and cleaned_lines[-1] != '':
                        cleaned_lines.append('')
            else:
                cleaned_lines.append(line)

        # Join with preservation of paragraph structure
        # Double newlines indicate paragraph breaks for chunker
        result_lines = []
        for i, line in enumerate(cleaned_lines):
            if line == '' and i > 0 and i < len(cleaned_lines) - 1:
                # This is a paragraph break
                if result_lines and result_lines[-1] != '':
                    result_lines.append('')
            elif line:
                result_lines.append(line)
        
        # Join lines preserving paragraph structure
        text_parts = []
        current_paragraph = []
        
        for line in result_lines:
            if line == '':
                if current_paragraph:
                    text_parts.append(' '.join(current_paragraph))
                    current_paragraph = []
            else:
                current_paragraph.append(line)
        
        # Add final paragraph
        if current_paragraph:
            text_parts.append(' '.join(current_paragraph))
        
        # Join paragraphs with double newlines for optimal chunking
        return '\n\n'.join(text_parts)

    def _extract_title(self, content: str) -> Optional[str]:
        """
        Extract title from PDF content.
        Tries to find the most likely title from the first page.
        """
        lines = content.split('\n')
        potential_titles = []

        # Look for potential titles in first 20 lines
        for i, line in enumerate(lines[:20]):
            line = line.strip()

            # Skip page headers and very short lines
            if not line or len(line) < 10 or 'Page' in line:
                continue

            # Skip lines that look like metadata
            if any(indicator in line.lower() for indicator in ['date:', 'author:', 'version:', 'file:']):
                continue

            # Prefer lines that are:
            # - Not too long (likely not body text)
            # - Contain title-like words
            # - Are near the beginning
            if len(line) < 100:
                score = 100 - i  # Earlier = higher score

                # Boost score for title-like characteristics
                if line[0].isupper():
                    score += 10
                if any(word in line.lower() for word in ['guide', 'manual', 'report', 'study', 'analysis']):
                    score += 15

                potential_titles.append((score, line))

        if potential_titles:
            # Return highest scoring potential title
            potential_titles.sort(reverse=True)
            return potential_titles[0][1]

        return None
