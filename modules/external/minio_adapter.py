"""
MinIO adapter for file operations with document and image support.
Provides a reusable interface for MinIO operations across the application.
"""
import io
import asyncio
from typing import Optional, List, Dict, Any, BinaryIO
from datetime import datetime, timedelta
import mimetypes
from pathlib import Path

from minio import Minio
from minio.error import S3Error
from config import CONFIG
from schemas.assistant import MIMEType
from utils.logger import logger
from utils.mime_utils import detect_mime_type_from_filename, is_image_mime_type, is_document_mime_type


class MinIOAdapter:
    """
    Reusable MinIO adapter for file operations.
    Handles document and image storage/retrieval with proper error handling.
    """
    
    def __init__(self, bucket_name: str = None):
        """
        Initialize MinIO adapter.
        
        Args:
            bucket_name: Default bucket to use. If None, uses CONFIG.MINIO_EPHY_BUCKET
        """
        self.client = self._create_client()
        self.default_bucket = bucket_name or CONFIG.MINIO_EPHY_BUCKET
        
        # Supported file types for document and image processing
        self.supported_image_types = {
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp', '.svg'
        }
        self.supported_document_types = {
            '.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt', 
            '.xls', '.xlsx', '.ppt', '.pptx', '.csv'
        }
        
    def _create_client(self) -> Minio:
        """Create and configure MinIO client."""
        if not all([CONFIG.MINIO_ENDPOINT, CONFIG.MINIO_ACCESS_KEY_ID, CONFIG.MINIO_SECRET_ACCESS_KEY]):
            raise ValueError("MinIO configuration is incomplete. Check MINIO_* environment variables.")
            
        return Minio(
            CONFIG.MINIO_ENDPOINT,
            access_key=CONFIG.MINIO_ACCESS_KEY_ID,
            secret_key=CONFIG.MINIO_SECRET_ACCESS_KEY,
            secure=CONFIG.MINIO_SSLMODE
        )
    
    async def ensure_bucket_exists(self, bucket_name: str = None) -> bool:
        """
        Ensure bucket exists, create if it doesn't.
        
        Args:
            bucket_name: Bucket name to check/create
            
        Returns:
            True if bucket exists or was created successfully
        """
        bucket = bucket_name or self.default_bucket
        
        try:
            # Run in thread pool to avoid blocking
            found = await asyncio.get_event_loop().run_in_executor(
                None, self.client.bucket_exists, bucket
            )
            
            if not found:
                await asyncio.get_event_loop().run_in_executor(
                    None, self.client.make_bucket, bucket
                )
                logger.info(f"Created MinIO bucket: {bucket}")
            
            return True
            
        except S3Error as e:
            logger.error(f"Error ensuring bucket {bucket} exists: {e}")
            return False
    
    async def upload_file(
        self, 
        file_data: bytes | BinaryIO, 
        object_name: str, 
        bucket_name: str = None,
        content_type: str = None,
        metadata: Dict[str, str] = None
    ) -> bool:
        """
        Upload a file to MinIO.
        
        Args:
            file_data: File data as bytes or file-like object
            object_name: Name for the object in MinIO
            bucket_name: Target bucket (uses default if None)
            content_type: MIME type of the file
            metadata: Additional metadata to store with the file
            
        Returns:
            True if upload successful, False otherwise
        """
        bucket = bucket_name or self.default_bucket
        
        try:
            # Ensure bucket exists
            if not await self.ensure_bucket_exists(bucket):
                return False
            
            # Handle different input types
            if isinstance(file_data, bytes):
                file_stream = io.BytesIO(file_data)
                file_size = len(file_data)
            else:
                file_stream = file_data
                # Try to get size
                file_stream.seek(0, 2)  # Seek to end
                file_size = file_stream.tell()
                file_stream.seek(0)  # Seek back to beginning
            
            # Auto-detect content type if not provided
            if not content_type:
                content_type, _ = mimetypes.guess_type(object_name)
                if not content_type:
                    content_type = 'application/octet-stream'
            
            # Prepare metadata
            file_metadata = metadata or {}
            file_metadata.update({
                'upload_timestamp': datetime.utcnow().isoformat(),
                'original_name': Path(object_name).name
            })
            
            # Upload file
            await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.put_object(
                    bucket, object_name, file_stream, file_size,
                    content_type=content_type,
                    metadata=file_metadata
                )
            )
            
            logger.info(f"Successfully uploaded {object_name} to bucket {bucket}")
            return True
            
        except S3Error as e:
            logger.error(f"Error uploading {object_name} to MinIO: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error uploading {object_name}: {e}")
            return False
    
    async def download_file(self, object_name: str, bucket_name: str = None) -> Optional[bytes]:
        """
        Download a file from MinIO.
        
        Args:
            object_name: Name of the object in MinIO
            bucket_name: Source bucket (uses default if None)
            
        Returns:
            File data as bytes, or None if download failed
        """
        bucket = bucket_name or self.default_bucket
        
        try:
            response = await asyncio.get_event_loop().run_in_executor(
                None, self.client.get_object, bucket, object_name
            )
            
            data = response.read()
            response.close()
            response.release_conn()
            
            logger.debug(f"Successfully downloaded {object_name} from bucket {bucket}")
            return data
            
        except S3Error as e:
            logger.error(f"Error downloading {object_name} from MinIO: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error downloading {object_name}: {e}")
            return None
    
    async def get_file_info(self, object_name: str, bucket_name: str = None) -> Optional[Dict[str, Any]]:
        """
        Get file information and metadata.
        
        Args:
            object_name: Name of the object in MinIO
            bucket_name: Source bucket (uses default if None)
            
        Returns:
            Dictionary with file info, or None if not found
        """
        bucket = bucket_name or self.default_bucket
        
        try:
            stat = await asyncio.get_event_loop().run_in_executor(
                None, self.client.stat_object, bucket, object_name
            )
            
            return {
                'object_name': stat.object_name,
                'size': stat.size,
                'etag': stat.etag,
                'last_modified': stat.last_modified,
                'content_type': stat.content_type,
                'metadata': stat.metadata,
                'bucket_name': bucket
            }
            
        except S3Error as e:
            logger.error(f"Error getting info for {object_name}: {e}")
            return None
    
    async def list_files(
        self, 
        prefix: str = "", 
        bucket_name: str = None,
        recursive: bool = True
    ) -> List[Dict[str, Any]]:
        """
        List files in a bucket with optional prefix filter.
        
        Args:
            prefix: Object name prefix to filter by
            bucket_name: Source bucket (uses default if None)
            recursive: Whether to list recursively
            
        Returns:
            List of file information dictionaries
        """
        bucket = bucket_name or self.default_bucket
        files = []
        
        try:
            objects = await asyncio.get_event_loop().run_in_executor(
                None, 
                lambda: list(self.client.list_objects(bucket, prefix=prefix, recursive=recursive))
            )
            
            for obj in objects:
                files.append({
                    'object_name': obj.object_name,
                    'size': obj.size,
                    'etag': obj.etag,
                    'last_modified': obj.last_modified,
                    'is_dir': obj.is_dir
                })
            
            logger.debug(f"Listed {len(files)} files from bucket {bucket} with prefix '{prefix}'")
            return files
            
        except S3Error as e:
            logger.error(f"Error listing files from bucket {bucket}: {e}")
            return []
    
    async def delete_file(self, object_name: str, bucket_name: str = None) -> bool:
        """
        Delete a file from MinIO.
        
        Args:
            object_name: Name of the object to delete
            bucket_name: Source bucket (uses default if None)
            
        Returns:
            True if deletion successful, False otherwise
        """
        bucket = bucket_name or self.default_bucket
        
        try:
            await asyncio.get_event_loop().run_in_executor(
                None, self.client.remove_object, bucket, object_name
            )
            
            logger.info(f"Successfully deleted {object_name} from bucket {bucket}")
            return True
            
        except S3Error as e:
            logger.error(f"Error deleting {object_name} from MinIO: {e}")
            return False
    
    async def generate_presigned_url(
        self, 
        object_name: str, 
        bucket_name: str = None,
        expires: timedelta = timedelta(hours=1)
    ) -> Optional[str]:
        """
        Generate a presigned URL for file access.
        
        Args:
            object_name: Name of the object
            bucket_name: Source bucket (uses default if None)
            expires: URL expiration time
            
        Returns:
            Presigned URL string, or None if generation failed
        """
        bucket = bucket_name or self.default_bucket
        
        try:
            url = await asyncio.get_event_loop().run_in_executor(
                None, 
                lambda: self.client.presigned_get_object(bucket, object_name, expires)
            )
            
            logger.debug(f"Generated presigned URL for {object_name}")
            return url
            
        except S3Error as e:
            logger.error(f"Error generating presigned URL for {object_name}: {e}")
            return None
    
    def is_image_file(self, filename: str) -> bool:
        """Check if file is a supported image type."""
        return Path(filename).suffix.lower() in self.supported_image_types
    
    def is_document_file(self, filename: str) -> bool:
        """Check if file is a supported document type."""
        return Path(filename).suffix.lower() in self.supported_document_types
    
    def is_supported_file(self, filename: str) -> bool:
        """Check if file is a supported type (image or document)."""
        return self.is_image_file(filename) or self.is_document_file(filename)
    
    def get_mime_type(self, filename: str) -> MIMEType:
        """
        Get MIME type for a filename.
        
        Args:
            filename: Name of the file
            
        Returns:
            MIMEType enum value
        """
        return detect_mime_type_from_filename(filename)
    
    def is_image_mime_type(self, mime_type: MIMEType) -> bool:
        """Check if MIME type is an image."""
        return is_image_mime_type(mime_type)
    
    def is_document_mime_type(self, mime_type: MIMEType) -> bool:
        """Check if MIME type is a document."""
        return is_document_mime_type(mime_type)
    
    def is_supported_mime_type(self, mime_type: MIMEType) -> bool:
        """Check if MIME type is supported (image or document)."""
        return self.is_image_mime_type(mime_type) or self.is_document_mime_type(mime_type)