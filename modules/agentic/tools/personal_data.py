import json
from typing import Op<PERSON>, List, Dict, Any

import httpx

from config import CONF<PERSON>
from schemas import BaseModel
from utils.logger import logger


class AgricultureApiResponse(BaseModel):
    """Flexible model for parsing agriculture API response"""
    errors: Optional[List[Dict[str, Any]]] = None
    farms: Optional[List[Dict[str, Any]]] = None
    interventions: Optional[List[Dict[str, Any]]] = None
    parcels: Optional[List[Dict[str, Any]]] = None
    user_id: Optional[int] = None

    # Additional fields that might appear in the response
    class Config:
        extra = "ignore"


class FarmInsight(BaseModel):
    """Structured farm data with citation information"""
    data: Dict[str, Any]
    citation: str
    timestamp: str
    source: str = "Agriculture API"


async def fetch_farm_data(user_id: int) -> Dict[str, Any]:
    """Fetch farm data from the API"""
    async with httpx.AsyncClient(timeout=10.0) as client:
        response = await client.get(f"{CONFIG.BACKEND_HOST}/v1/farmers/{user_id}/rawdata")
        response.raise_for_status()
        return response.json()


async def fetch_purchase_order(user_id: int):
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(f"{CONFIG.BACKEND_HOST}/v1/notes/{user_id}")
            response.raise_for_status()
            return response.json()
    except httpx.RequestError as e:
        logger.error(f"Error fetching data: {str(e)}")
        return {"error": f"API request failed: {str(e)}"}
    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error: {e.response.status_code}")
        return {"error": f"API returned status code {e.response.status_code}"}
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return {"error": f"Unexpected error: {str(e)}"}


async def get_purchase_orders_history(user_id):
    """
    Returns overview of purchase orders history in a structured format
    optimized for LLM understanding.
    """
    logger.debug(f"Fetching purchase orders for user: {user_id}")
    raw_data = await fetch_purchase_order(user_id)

    formatted_orders = []
    for order in raw_data:
        po = order.get('purchase_order', {})

        # Format order items for better readability
        items_text = ""
        for item in po.get('order_items', []):
            items_text += f"- {item.get('quantity')} {item.get('unit')} of {item.get('description')} (Ref: {item.get('product_ref')}) at {item.get('unit_price_ht')} per unit, total: {item.get('line_total_ht')} EUR\n"

        # Create a structured summary of the purchase order
        order_summary = {
            "order_id": order.get('id'),
            "date_created": order.get('date_created', '').split('T')[0],  # Just keep the date part
            "client": {
                "name": po.get('client_info', {}).get('farmer_name'),
                "farm": po.get('client_info', {}).get('farm_name'),
                "address": po.get('client_info', {}).get('farm_address'),
                "contact": f"Phone: {po.get('client_info', {}).get('phone')}, Email: {po.get('client_info', {}).get('email')}",
            },
            "advisor": {
                "name": po.get('advisor_info', {}).get('name'),
                "company": po.get('advisor_info', {}).get('company'),
                "contact": f"Phone: {po.get('advisor_info', {}).get('phone')}, Email: {po.get('advisor_info', {}).get('email')}",
            },
            "order_items": items_text.strip(),
            "totals": {
                "subtotal": f"{po.get('totals', {}).get('subtotal_ht')} EUR",
                "vat": f"{po.get('totals', {}).get('vat_amount')} EUR ({po.get('totals', {}).get('vat_rate')})",
                "total": f"{po.get('totals', {}).get('total_ttc')} EUR",
            },
            "delivery": {
                "expected_date": po.get('delivery', {}).get('expected_date_range'),
                "address": po.get('delivery', {}).get('delivery_address'),
                "conditions": po.get('delivery', {}).get('delivery_conditions'),
            },
            "payment": po.get('payment_terms', {}).get('payment_type'),
            "additional_notes": po.get('additional_notes', {}),
            "status": "Signed" if po.get('signatures', {}).get('client_signature') else "Pending signature",
        }
        formatted_orders.append(order_summary)

    # Return the formatted data in a structured context block
    return f"""You can use this data if user asks anything about purchase orders history:
<PurchaseOrdersHistory>
{json.dumps(formatted_orders, indent=2, ensure_ascii=False)}
</PurchaseOrdersHistory>
"""


def format_farm_data_for_llm(farm_data):
    """
    Converts raw farm data from MesParcelles into a token-efficient format
    that's easily processable by LLMs while maintaining all critical information.

    Args:
        farm_data (dict): Raw farm data JSON object from MesParcelles

    Returns:
        str: Formatted farm data optimized for LLM token efficiency
    """
    # Extract the date created
    date_created = farm_data.get('date_created', '')

    # Create a dictionary to organize parcels by UUID for easy reference
    parcels_by_id = {}
    for parcel in farm_data.get('metadata', {}).get('parcels', []):
        uuid = parcel.get('uuid_parcelle', '')
        if uuid:
            parcels_by_id[uuid] = {
                'name': parcel.get('nom', ''),
                'area_ha': parcel.get('surface_mesuree_ha', 0),
                'location_code': parcel.get('insee_commune', ''),
                'current_crop': parcel.get('succession_cultures', [{}])[0].get('libelle', '') if parcel.get(
                    'succession_cultures') else '',
                'interventions': []
            }

    # Group interventions by parcel
    for intervention in farm_data.get('metadata', {}).get('interventions', []):
        parcel_id = intervention.get('uuid_parcelle', '')
        if parcel_id in parcels_by_id:
            parcels_by_id[parcel_id]['interventions'].append({
                'type': intervention.get('type_intervention', {}).get('libelle', ''),
                'crop': intervention.get('culture', {}).get('libelle', ''),
                'date': intervention.get('date_debut', ''),
                'area_ha': intervention.get('surface_travaillee_ha', 0),
                'lot_number': intervention.get('numero_lot', '')
            })

    # Calculate farm statistics
    total_area = sum(parcel['area_ha'] for parcel in parcels_by_id.values())
    crops_summary = {}
    for parcel in parcels_by_id.values():
        crop = parcel['current_crop']
        if crop:
            if crop in crops_summary:
                crops_summary[crop]['area_ha'] += parcel['area_ha']
                crops_summary[crop]['parcel_count'] += 1
            else:
                crops_summary[crop] = {
                    'area_ha': parcel['area_ha'],
                    'parcel_count': 1
                }

    # Create timeline of recent interventions
    all_interventions = []
    for parcel_id, parcel in parcels_by_id.items():
        for intervention in parcel['interventions']:
            all_interventions.append({
                'parcel_name': parcel['name'],
                'date': intervention['date'],
                'crop': intervention['crop'],
                'type': intervention['type'],
                'area_ha': intervention['area_ha']
            })

    # Sort interventions by date (newest first)
    all_interventions.sort(key=lambda x: x['date'], reverse=True)

    # Begin building the output in a token-efficient format
    lines = []

    # Header with source citation
    lines.append("FARM MANAGEMENT DATA (Source: MesParcelles, " + date_created + ")")

    # Farm Summary - concise key-value format
    lines.append("\nFARM SUMMARY")
    lines.append(f"Total parcels: {len(parcels_by_id)}")
    lines.append(f"Total area: {total_area:.2f} ha")

    # Crop Distribution - compact format
    lines.append("\nCROP DISTRIBUTION")
    for crop, data in crops_summary.items():
        percentage = (data['area_ha'] / total_area) * 100 if total_area else 0
        lines.append(f"{crop}: {data['area_ha']:.2f} ha ({percentage:.1f}%), {data['parcel_count']} parcels")

    # Parcels - minimal formatting, key information only
    lines.append("\nPARCELS")
    for parcel_id, parcel in parcels_by_id.items():
        lines.append(
            f"\n{parcel['name']} - {parcel['area_ha']:.2f} ha - {parcel['current_crop']} - Location: {parcel['location_code']}")

        if parcel['interventions']:
            # Sort interventions by date (newest first)
            sorted_interventions = sorted(parcel['interventions'], key=lambda x: x['date'], reverse=True)
            lines.append("Interventions:")
            for intervention in sorted_interventions:
                lines.append(
                    f"- {intervention['date']}: {intervention['type']} of {intervention['crop']} ({intervention['area_ha']:.2f} ha)")

    # Recent Farm Activity - simplified timeline format
    lines.append("\nRECENT FARM ACTIVITY (chronological)")
    for i, intervention in enumerate(all_interventions[:10], 1):
        lines.append(
            f"{i}. {intervention['date']}: {intervention['type']} of {intervention['crop']} on {intervention['parcel_name']} ({intervention['area_ha']:.2f} ha)")

    # Agricultural Calendar - categorized by intervention type
    lines.append("\nAGRICULTURAL CALENDAR")

    # Recent plantings
    plantings = [i for i in all_interventions if 'Semis' in i['type']]
    if plantings:
        lines.append("\nRecent Plantings:")
        for planting in plantings[:5]:  # Limit to 5 most recent
            lines.append(
                f"- {planting['date']}: {planting['crop']} on {planting['parcel_name']} ({planting['area_ha']:.2f} ha)")

    # Recent fertilizations
    fertilizations = [i for i in all_interventions if 'Fertilisation' in i['type']]
    if fertilizations:
        lines.append("\nRecent Fertilizations:")
        for fertilization in fertilizations[:5]:  # Limit to 5 most recent
            lines.append(
                f"- {fertilization['date']}: {fertilization['crop']} on {fertilization['parcel_name']} ({fertilization['area_ha']:.2f} ha)")

    return "\n".join(lines)


async def get_farm_raw_data(user_id) -> str:
    """
    Returns overview of my farm's current state and recent activities for LLM context.
    """
    try:
        logger.debug(f"Fetching farm insights for user: {user_id}")
        raw_data = await fetch_farm_data(user_id)

        # Parse the data
        structured_data = format_farm_data_for_llm(raw_data)

        return f"You can use the following information about the farmer to answer the user's question if needed:\n<FarmDataContext>\n{structured_data}\n</FarmDataContext>"
    except Exception as e:
        logger.error(f"Error fetching farm insights: {str(e)}")
        return "No farm data available."
