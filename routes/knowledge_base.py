"""
Knowledge Base API routes for document processing and RAG retrieval.
Provides endpoints for document upload, processing, and search operations.
"""
from typing import List, Dict, Any, Optional
from uuid import UUID
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel

from modules.knowledge_base import KnowledgeBaseService
from models.knowledge_base import AccessLevel, DocumentType
from schemas.assistant import SFileInput
from utils.logger import logger


# Pydantic models for API
class DocumentUploadRequest(BaseModel):
    files: List[SFileInput]
    user_id: Optional[str] = None
    enterprise_id: Optional[str] = None
    access_level: AccessLevel = AccessLevel.PRIVATE
    metadata: Optional[Dict[str, Any]] = None


class DocumentSearchRequest(BaseModel):
    query: str
    user_id: Optional[str] = None
    enterprise_id: Optional[str] = None
    access_levels: Optional[List[AccessLevel]] = None
    filters: Optional[Dict[str, Any]] = None
    limit: int = 10
    include_chunks: bool = True


class ProcessingStatusResponse(BaseModel):
    document_id: str
    status: str
    progress: Optional[float] = None
    error_message: Optional[str] = None
    chunks_count: Optional[int] = None


class SearchResultResponse(BaseModel):
    query: str
    total_results: int
    results: List[Dict[str, Any]]
    retrieval_time_ms: float


class DeleteDocumentRequest(BaseModel):
    user_id: Optional[str] = None
    enterprise_id: Optional[str] = None
    force: bool = False  # Admin delete bypass access control


class DeleteUserDocumentsRequest(BaseModel):
    user_id: str
    enterprise_id: Optional[str] = None


# Create router
router = APIRouter(prefix="/knowledge-base", tags=["Knowledge Base"])

# Initialize service
knowledge_service = KnowledgeBaseService()


@router.post("/documents/upload", response_model=Dict[str, Any])
async def upload_documents(
    request: DocumentUploadRequest,
    background_tasks: BackgroundTasks
):
    """
    Upload and process documents from MinIO.
    Processing happens in background for better performance.
    """
    try:
        # Start batch processing in background
        background_tasks.add_task(
            _process_documents_background,
            request.files,
            request.user_id,
            request.enterprise_id,
            request.access_level,
            request.metadata
        )
        
        return {
            "message": f"Started processing {len(request.files)} documents",
            "file": [file_input.object_name for file_input in request.files],
            "status": "processing_started"
        }
        
    except Exception as e:
        logger.error(f"Error initiating document upload: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to initiate document processing: {str(e)}"
        )

#
# @router.post("/documents/process-sync", response_model=Dict[str, Any])
# async def process_documents_sync(request: DocumentUploadRequest):
#     """
#     Process documents synchronously (for small batches).
#     Returns processing results immediately.
#     """
#     try:
#         if len(request.files) > 5:
#             raise HTTPException(
#                 status_code=400,
#                 detail="Use async processing for more than 5 documents"
#             )
#
#         results = await knowledge_service.batch_process_documents(
#             file_inputs=request.files,
#             user_id=request.user_id,
#             enterprise_id=request.enterprise_id,
#             access_level=request.access_level,
#             batch_size=3
#         )
#
#         # Count successful/failed processing
#         successful = sum(1 for r in results.values() if r['success'])
#         failed = len(results) - successful
#
#         return {
#             "message": f"Processed {len(request.files)} documents",
#             "successful": successful,
#             "failed": failed,
#             "results": results
#         }
#
#     except Exception as e:
#         logger.error(f"Error processing documents synchronously: {e}")
#         raise HTTPException(
#             status_code=500,
#             detail=f"Failed to process documents: {str(e)}"
#         )


@router.post("/search", response_model=SearchResultResponse)
async def search_documents(request: DocumentSearchRequest):
    """
    Search documents using hybrid semantic + keyword approach.
    Returns relevant document chunks with context.
    """
    try:
        results = await knowledge_service.search_documents(
            query=request.query,
            user_id=request.user_id,
            enterprise_id=request.enterprise_id,
            access_levels=request.access_levels,
            filters=request.filters,
            limit=request.limit,
            include_chunks=request.include_chunks
        )
        
        return SearchResultResponse(**results)
        
    except Exception as e:
        logger.error(f"Error searching documents: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Search failed: {str(e)}"
        )


@router.get("/documents/{document_id}/chunks")
async def get_document_chunks(
    document_id: UUID,
    user_id: Optional[str] = None,
    enterprise_id: Optional[str] = None
):
    """
    Get all chunks for a specific document.
    Requires appropriate access permissions.
    """
    try:
        chunks = await knowledge_service.get_document_chunks(
            document_id=document_id,
            user_id=user_id,
            enterprise_id=enterprise_id
        )
        
        if chunks is None:
            raise HTTPException(
                status_code=404,
                detail="Document not found or access denied"
            )
        
        return {
            "document_id": str(document_id),
            "chunks_count": len(chunks),
            "chunks": chunks
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving document chunks: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve chunks: {str(e)}"
        )


@router.get("/documents/{document_id}/status")
async def get_processing_status(document_id: UUID):
    """
    Get processing status for a specific document.
    """
    try:
        # This would need to be implemented in the service
        # For now, return a placeholder
        return {
            "document_id": str(document_id),
            "status": "completed",
            "message": "Document processing status endpoint - to be implemented"
        }
        
    except Exception as e:
        logger.error(f"Error getting processing status: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get status: {str(e)}"
        )


@router.delete("/documents/{document_id}")
async def delete_document(
    document_id: UUID,
    request: DeleteDocumentRequest
):
    """
    Delete a document and all its chunks from the knowledge base.
    
    Access control:
    - Users can only delete their own documents
    - Enterprise users can delete enterprise documents they have access to
    - force=True bypasses access control (admin only)
    """
    try:
        success, error = await knowledge_service.delete_document(
            document_id=document_id,
            user_id=request.user_id,
            enterprise_id=request.enterprise_id,
            force=request.force
        )
        
        if not success:
            if error == "Document not found or access denied":
                raise HTTPException(
                    status_code=404,
                    detail=error
                )
            else:
                raise HTTPException(
                    status_code=500,
                    detail=error
                )
        
        return {
            "message": "Document successfully deleted",
            "document_id": str(document_id),
            "deleted": True
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting document {document_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete document: {str(e)}"
        )


@router.delete("/users/{user_id}/documents")
async def delete_user_documents(
    user_id: str,
    enterprise_id: Optional[str] = None
):
    """
    Delete all documents for a specific user.
    
    This is typically used for:
    - User account deletion (GDPR compliance)
    - Data cleanup operations
    - Admin bulk operations
    """
    try:
        success, deleted_count, error = await knowledge_service.delete_documents_by_user(
            user_id=user_id,
            enterprise_id=enterprise_id
        )
        
        if not success:
            raise HTTPException(
                status_code=500,
                detail=error
            )
        
        return {
            "message": f"Successfully deleted {deleted_count} documents for user {user_id}",
            "user_id": user_id,
            "enterprise_id": enterprise_id,
            "deleted_count": deleted_count,
            "deleted": True
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting documents for user {user_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete user documents: {str(e)}"
        )


@router.post("/documents/delete-batch")
async def delete_documents_batch(
    document_ids: List[UUID],
    request: DeleteDocumentRequest
):
    """
    Delete multiple documents in a batch operation.
    
    Useful for:
    - Bulk cleanup operations
    - UI multi-select delete
    - Administrative tasks
    """
    try:
        results = {}
        successful_deletes = 0
        failed_deletes = 0
        
        for doc_id in document_ids:
            try:
                success, error = await knowledge_service.delete_document(
                    document_id=doc_id,
                    user_id=request.user_id,
                    enterprise_id=request.enterprise_id,
                    force=request.force
                )
                
                if success:
                    results[str(doc_id)] = {"deleted": True, "error": None}
                    successful_deletes += 1
                else:
                    results[str(doc_id)] = {"deleted": False, "error": error}
                    failed_deletes += 1
                    
            except Exception as e:
                results[str(doc_id)] = {"deleted": False, "error": str(e)}
                failed_deletes += 1
        
        return {
            "message": f"Batch delete completed: {successful_deletes} successful, {failed_deletes} failed",
            "total_requested": len(document_ids),
            "successful_deletes": successful_deletes,
            "failed_deletes": failed_deletes,
            "results": results
        }
        
    except Exception as e:
        logger.error(f"Error in batch delete operation: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Batch delete failed: {str(e)}"
        )



# Background task functions
async def _process_documents_background(
    file_inputs: List[SFileInput],
    user_id: Optional[str],
    enterprise_id: Optional[str], 
    access_level: AccessLevel,
    metadata: Optional[Dict[str, Any]]
):
    """Background task for document processing."""
    try:
        logger.info(f"Starting background processing of {len(file_inputs)} documents")
        
        results = await knowledge_service.batch_process_documents(
            file_inputs=file_inputs,
            user_id=user_id,
            enterprise_id=enterprise_id,
            access_level=access_level,
            batch_size=3
        )
        
        successful = sum(1 for r in results.values() if r['success'])
        failed = len(results) - successful
        
        logger.info(f"Background processing completed: {successful} successful, {failed} failed")
        
        # Here you could emit events, send notifications, etc.
        
    except Exception as e:
        logger.error(f"Background document processing failed: {e}")


# Additional utility endpoints
@router.get("/stats/search")
async def get_search_stats(
    user_id: Optional[str] = None,
    enterprise_id: Optional[str] = None,
    days: int = 30
):
    """
    Get search statistics for analytics.
    """
    try:
        # This would need to be implemented in the service
        return {
            "message": "Search statistics endpoint - to be implemented",
            "user_id": user_id,
            "enterprise_id": enterprise_id,
            "days": days
        }
        
    except Exception as e:
        logger.error(f"Error getting search stats: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get statistics: {str(e)}"
        )


@router.get("/documents/types")
async def get_supported_document_types():
    """Get list of supported document types."""
    return {
        "supported_types": [doc_type.value for doc_type in DocumentType],
        "descriptions": {
            DocumentType.PDF.value: "PDF documents with text extraction",
            DocumentType.TXT.value: "Plain text files",
            DocumentType.IMAGE.value: "Images with OCR text extraction",
            DocumentType.CSV.value: "Structured CSV data files",
            DocumentType.JSON.value: "JSON data files",
            DocumentType.HTML.value: "HTML web documents",
            DocumentType.DOCX.value: "Microsoft Word documents",
            DocumentType.OTHER.value: "Other file types (fallback processing)"
        }
    }