# E-Phy Pipeline Package
# Handles extraction, transformation, loading and enhancement of agricultural product data

import asyncio
import os
from pathlib import Path

from models.dbm import DatabaseManager
from modules.pipelines.e_phy.data_enhancer import DataEnhancer
from modules.pipelines.e_phy.data_loader import DataLoader
from utils.logger import logger


async def load_e_phy_data():
    logger.info("Starting E-Phy data refresh")

    # Initialize database manager
    db = DatabaseManager()
    await db.drop_db()
    await db.init_db()

    # Get the project root directory and construct the data directory path
    project_root = Path(__file__).resolve().parent.parent.parent.parent
    data_dir = project_root / "e-phy"

    # Check if the data directory exists
    if not data_dir.exists():
        logger.info(f"Creating data directory at {data_dir}")
        os.makedirs(data_dir, exist_ok=True)

    async with db.session_scope() as session:
        # Load data
        data_loader = DataLoader(session, str(data_dir))
        await data_loader.load_all_data()

        # # # Enhance data
        try:
            enhancer = DataEnhancer(session)
            await enhancer.enhance_all_data()
        except Exception as e:
            logger.error(f"Error enhancing data: {e}")

    logger.info("E-Phy data is already up to date")


# Example usage
if __name__ == "__main__":
    # Run the async function using asyncio
    asyncio.run(load_e_phy_data())
