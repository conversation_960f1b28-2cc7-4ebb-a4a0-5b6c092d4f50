from sqlalchemy import (
    Column, Integer, String, Text, Boolean, Date,
    Float, ForeignKey, UniqueConstraint, Index
)
from sqlalchemy.dialects.postgresql import TSVECTOR
from sqlalchemy.orm import relationship

from models import Base


class Product(Base):
    __tablename__ = "products"

    id = Column(Integer, primary_key=True)
    product_type = Column(String(50))  # Increased from 10 to 50
    registration_number = Column(String(50), unique=True, nullable=False)  # Increased size
    product_name = Column(String(255), nullable=False)
    alternative_names = Column(Text)
    holder = Column(String(255))
    commercial_type = Column(String(50))
    usage_range = Column(String(50))
    authorized_mentions = Column(Text)
    authorization_status = Column(String(100))  # Increased size
    withdrawal_date = Column(Date)
    first_authorization_date = Column(Date)
    reference_product_number = Column(String(50))  # Increased size
    reference_product_name = Column(String(255))
    formulation_type = Column(String(100))
    function_category = Column(String(255))
    search_vector = Column(TSVECTOR)
    is_currently_authorized = Column(Boolean)
    product_summary = Column(Text)

    substances = relationship("ProductSubstance", back_populates="product")
    compositions = relationship("ProductComposition", back_populates="product")
    uses = relationship("ProductUse", back_populates="product")
    hazards = relationship("ProductHazard", back_populates="product")
    conditions = relationship("UsageCondition", back_populates="product")
    parallel_trade_permits = relationship("ParallelTradePermit", back_populates="reference_product")

    __table_args__ = (
        Index("idx_product_name", product_name),
        Index("idx_product_search", search_vector, postgresql_using="gin"),
        Index("idx_product_authorization", is_currently_authorized),
        Index("idx_registration_number", registration_number),
    )


class ActiveSubstance(Base):
    __tablename__ = "active_substances"

    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    cas_number = Column(String(100))  # Increased size
    authorization_status = Column(String(100))  # Increased size
    variants = Column(Text)
    normalized_name = Column(String(255))
    is_currently_authorized = Column(Boolean)

    products = relationship("ProductSubstance", back_populates="substance")

    __table_args__ = (
        Index("idx_substance_normalized", normalized_name),
    )


class ProductSubstance(Base):
    __tablename__ = "product_substances"

    id = Column(Integer, primary_key=True)
    product_id = Column(Integer, ForeignKey("products.id"))
    substance_id = Column(Integer, ForeignKey("active_substances.id"))
    concentration = Column(Float(precision=10, decimal_return_scale=4))
    concentration_unit = Column(String(50))  # Increased size
    primary_substance = Column(Boolean, default=False)

    product = relationship("Product", back_populates="substances")
    substance = relationship("ActiveSubstance", back_populates="products")

    __table_args__ = (
        UniqueConstraint("product_id", "substance_id", name="uq_product_substance"),
    )


class ProductComposition(Base):
    __tablename__ = "product_compositions"

    id = Column(Integer, primary_key=True)
    product_id = Column(Integer, ForeignKey("products.id"))
    component_name = Column(String(255))
    min_value = Column(Float(precision=10, decimal_return_scale=4))
    max_value = Column(Float(precision=10, decimal_return_scale=4))
    unit = Column(String(50))  # Increased from 20 to 50
    class_denomination = Column(String(100))
    component_category = Column(String(50))

    product = relationship("Product", back_populates="compositions")


class Crop(Base):
    __tablename__ = "crops"

    id = Column(Integer, primary_key=True)
    crop_name = Column(String(255), unique=True, nullable=False)
    normalized_name = Column(String(255))
    crop_category = Column(String(100))
    common_synonyms = Column(Text)
    usage_count = Column(Integer)

    uses = relationship("ProductUse", back_populates="crop")
    usage_patterns = relationship("UsagePattern", back_populates="crop")

    __table_args__ = (
        Index("idx_crop_normalized", normalized_name),
    )


class Target(Base):
    __tablename__ = "targets"

    id = Column(Integer, primary_key=True)
    target_name = Column(String(255), unique=True, nullable=False)
    target_type = Column(String(50))
    normalized_name = Column(String(255))
    common_synonyms = Column(Text)

    uses = relationship("ProductUse", back_populates="target")
    usage_patterns = relationship("UsagePattern", back_populates="target")

    __table_args__ = (
        Index("idx_target_normalized", normalized_name),
    )


class ProductUse(Base):
    __tablename__ = "product_uses"

    id = Column(Integer, primary_key=True)
    product_id = Column(Integer, ForeignKey("products.id"))
    crop_id = Column(Integer, ForeignKey("crops.id"))
    target_id = Column(Integer, ForeignKey("targets.id"))
    usage_id = Column(String(255))  # Increased from 50 to 255
    usage_description = Column(Text)
    application_part = Column(String(100))
    decision_date = Column(Date)
    min_growth_stage = Column(Integer)
    max_growth_stage = Column(Integer)
    usage_status = Column(String(50))
    min_dose = Column(Float(precision=10, decimal_return_scale=4))
    max_dose = Column(Float(precision=10, decimal_return_scale=4))
    dose_unit = Column(String(50))  # Increased from 20 to 50
    harvest_interval_days = Column(Integer)
    max_applications = Column(Integer)
    distribution_end_date = Column(Date)
    usage_end_date = Column(Date)
    application_conditions = Column(Text)
    aquatic_buffer_zone = Column(Integer)
    arthropod_buffer_zone = Column(Integer)
    plant_buffer_zone = Column(Integer)
    min_interval_between_applications = Column(Integer)
    application_season_min = Column(String(100))
    application_season_max = Column(String(100))
    application_comments = Column(Text)
    is_currently_authorized = Column(Boolean)
    max_buffer_zone = Column(Integer)
    has_special_conditions = Column(Boolean)
    usage_summary = Column(Text)

    product = relationship("Product", back_populates="uses")
    crop = relationship("Crop", back_populates="uses")
    target = relationship("Target", back_populates="uses")

    __table_args__ = (
        Index("idx_product_use_crop_target", crop_id, target_id),
        Index("idx_product_use_authorized", is_currently_authorized),
    )


class ProductHazard(Base):
    __tablename__ = "product_hazards"

    id = Column(Integer, primary_key=True)
    product_id = Column(Integer, ForeignKey("products.id"))
    hazard_code = Column(String(50))  # Increased from 20 to 50
    hazard_description = Column(Text)
    hazard_category = Column(String(100))
    hazard_severity = Column(Integer)
    requires_special_equipment = Column(Boolean)

    product = relationship("Product", back_populates="hazards")

    __table_args__ = (
        Index("idx_product_hazard_category", hazard_category),
    )


class UsageCondition(Base):
    __tablename__ = "usage_conditions"

    id = Column(Integer, primary_key=True)
    product_id = Column(Integer, ForeignKey("products.id"))
    condition_category = Column(String(100))
    condition_description = Column(Text)
    condition_type = Column(String(50))
    condition_importance = Column(String(50))  # Increased from 20 to 50

    product = relationship("Product", back_populates="conditions")


class ParallelTradePermit(Base):
    __tablename__ = "parallel_trade_permits"

    id = Column(Integer, primary_key=True)
    permit_number = Column(String(50), unique=True, nullable=False)  # Ensure it's handled as string in code
    product_name = Column(String(255), nullable=False)
    authorization_status = Column(String(100))  # Increased size
    permit_holder = Column(String(255))
    french_reference_product = Column(String(255))
    french_reference_registration = Column(String(50))
    imported_product_name = Column(String(255))
    imported_product_registration = Column(String(50))
    origin_member_state = Column(String(50))
    labeling_mentions = Column(Text)
    french_reference_product_id = Column(Integer, ForeignKey("products.id"))
    is_currently_authorized = Column(Boolean)

    reference_product = relationship("Product", back_populates="parallel_trade_permits")


class UsagePattern(Base):
    __tablename__ = "usage_patterns"

    id = Column(Integer, primary_key=True)
    crop_id = Column(Integer, ForeignKey("crops.id"))
    target_id = Column(Integer, ForeignKey("targets.id"))
    avg_dose = Column(Float(precision=10, decimal_return_scale=4))
    avg_max_applications = Column(Float(precision=5, decimal_return_scale=2))
    common_application_timing = Column(Text)
    avg_harvest_interval = Column(Float(precision=5, decimal_return_scale=2))
    authorized_product_count = Column(Integer)
    pattern_insights = Column(Text)

    crop = relationship("Crop", back_populates="usage_patterns")
    target = relationship("Target", back_populates="usage_patterns")

    __table_args__ = (
        Index("idx_usage_pattern_crop_target", crop_id, target_id),
    )
