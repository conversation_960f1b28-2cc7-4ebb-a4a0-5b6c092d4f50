from typing import Optional, Type, Dict, Any, Literal

from pydantic import create_model, Field, BaseModel
from pydantic_ai import Agent, RunContext
from pydantic_ai.settings import ModelSettings

from const import EAgent, EConnectionState
from modules.agentic.agents.abc_agent import ABCAgent
from modules.agentic.prompts.selective_agent import selective_agent, SelectiveAgentInputs
from modules.agentic.utils.response_processor import ResponseProcessor
from schemas.agent import SAgentDeps
from schemas.assistant import SChatRequest
from utils.logger import logger


class SSelectiveResponse(BaseModel):
    """Base response model for the selective agent_type."""
    vector_search_query: Optional[str] = Field(description='keywords for similarity search in vector database',
                                               default=None)
    web_search_query: Optional[str] = Field(description='search query for web search', default=None)
    topic: Optional[Literal['general', 'finance', 'news']] = Field(
        description='topic for web search: general or finance or news ', default=None)
    # freshness: Optional[Union[FreshnessOption, str]] = None
    # time_range: Optional[Union[TimeRangeOptions, str]] = None,
    agent: Optional[EAgent] = None
    # additional_context: Optional[str] = None


class SelectiveAgent(ABCAgent):
    """Agent that selects the appropriate specialized agent_type to handle a query."""

    def __init__(self, agent_type: EAgent = EAgent.general, web: bool = True):
        """
        Initialize and configure an agent_type instance.

        Args:
            agent_type: The type of agent_type to create
            web: Whether web search capability should be enabled
        """
        self.agent_type = agent_type
        self.web = web
        super().__init__()

    def run_stream(self, **kwargs):
        raise NotImplementedError

    def _get_model(self) -> str:
        return "mistral:open-mistral-nemo"

    def _get_system_prompt(self):
        return ()

    def _get_model_settings(self) -> ModelSettings:
        return ModelSettings(
            temperature=0.5,
            parallel_tool_calls=True,
            # max_tokens=500,
        )

    def _get_response_model(self) -> Type[BaseModel]:
        """
        Create a dynamic response model based on agent_type type and capabilities.

        Returns:
            A dynamically created Pydantic model class
        """
        agent_type = self.agent_type
        web = self.web
        fields: Dict[str, Any] = {
            'vector_search_query': (Optional[str], Field(
                description='search query for similarity search in vector database', default=None)),
            # 'additional_context': (Optional[str], Field(description='Additional context', default=None))
        }

        if agent_type == EAgent.selective:
            fields['agent_type'] = (EAgent, Field(description='Agent to use'))

        if web:
            fields['web_search_query'] = (Optional[str], Field(description='Web search query', default=None))
            fields['topic'] = (Optional[Literal['general', 'finance', 'news']],
                               Field(description='topic for web search: general or finance or news', default=None))

        return create_model(
            'SSelectiveResponse',
            **fields,
            # __base__=SSelectiveResponse
        )

    def _configure_agent(self, agent: Agent):
        @agent.system_prompt
        async def system_prompt(ctx: RunContext[SAgentDeps]) -> str:
            agent_type = self.agent_type
            web = self.web
            agent_types = '|'.join([e.value for e in EAgent if e != EAgent.selective])
            request = ctx.deps.data
            messages = ""
            if request.message_chain:
                messages = self.get_history_context(request.message_chain)
            prompt = selective_agent.format(
                SelectiveAgentInputs(agent_type=agent_type, web=web, agent_types=agent_types,
                                     previous_messages=messages)
            )
            return prompt

        return agent

    @staticmethod
    def _get_tools():
        return []
        # return [
        #     Tool(with_event(get_current_agricultural_weather, "Analysing weather data..."), takes_ctx=True),
        #     Tool(with_event(get_agricultural_forecast, "Analysing weather forecast..."), takes_ctx=True),
        #     Tool(with_event(get_precipitation_forecast, "Analysing precipitation forecast..."), takes_ctx=True),
        #     Tool(with_event(get_agricultural_historical_weather, "Analysing historical weather data..."),
        #          takes_ctx=True),
        #     Tool(with_event(check_frost_risk, "Analysing frost risk..."), takes_ctx=True),
        #     Tool(with_event(get_growing_condition_assessment, "Analysing growing conditions..."), takes_ctx=True),
        # ]

    async def run(self, request: SChatRequest, processor: ResponseProcessor) -> SSelectiveResponse:

        # Update connection state
        await processor.add_event(processor.create_connection_event(EConnectionState.connected, "Connected to AI"))

        res = await self.agent.run(request.query, deps=SAgentDeps(data=request, meta=processor))
        logger.debug(f"Usage Selective: {res.usage()}")

        # print(f"Metadata : {json.dumps(serialize_messages(res.all_messages()), indent=2)}")
        return res.data
