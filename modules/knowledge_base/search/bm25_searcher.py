"""
BM25 search implementation for contextual retrieval.
Implements keyword-based search on contextual content to complement semantic search.
"""
import pickle
import re
from dataclasses import dataclass
from pathlib import Path
from typing import List, Dict, Any, Optional
from uuid import UUID

from rank_bm25 import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from models.knowledge_base import DocumentChunk, Document
from utils.logger import logger


@dataclass
class BM25Result:
    """Result from BM25 search."""
    chunk_id: str
    document_id: str
    score: float
    chunk_content: str
    contextual_content: str
    document_title: Optional[str] = None


class ContextualBM25Searcher:
    """
    BM25 searcher for contextual retrieval.
    Searches on contextual content (context + original content) for keyword matching.
    """

    def __init__(self, cache_dir: Optional[str] = None):
        self.cache_dir = Path(cache_dir) if cache_dir else Path("/tmp/bm25_cache")
        self.cache_dir.mkdir(exist_ok=True)

        self.bm25_index: Optional[BM25Okapi] = None
        self.chunk_metadata: List[Dict[str, Any]] = []
        self.index_version: Optional[str] = None

    async def build_index(self, session: AsyncSession, force_rebuild: bool = False) -> bool:
        """
        Build BM25 index from all document chunks with contextual content.
        
        Args:
            session: Database session
            force_rebuild: Force rebuild even if cache exists
            
        Returns:
            True if index built successfully
        """
        try:
            cache_file = self.cache_dir / "bm25_index.pkl"
            metadata_file = self.cache_dir / "chunk_metadata.pkl"

            # Check if we can load from cache
            if not force_rebuild and cache_file.exists() and metadata_file.exists():
                try:
                    with open(cache_file, 'rb') as f:
                        self.bm25_index = pickle.load(f)
                    with open(metadata_file, 'rb') as f:
                        self.chunk_metadata = pickle.load(f)

                    logger.info(f"Loaded BM25 index from cache with {len(self.chunk_metadata)} chunks")
                    return True
                except Exception as e:
                    logger.warning(f"Failed to load BM25 cache: {e}. Rebuilding...")

            # Fetch all chunks with contextual content
            query = (
                select(DocumentChunk, Document.title)
                .join(Document, DocumentChunk.document_id == Document.id)
                .where(
                    DocumentChunk.contextual_content.isnot(None)
                )
                .order_by(DocumentChunk.created_at)
            )

            result = await session.execute(query)
            chunks_data = result.all()

            if not chunks_data:
                logger.warning("No chunks with contextual content found for BM25 indexing")
                return False

            # Prepare documents for BM25
            documents = []
            chunk_metadata = []

            for chunk, doc_title in chunks_data:
                # Tokenize contextual content for BM25
                tokenized_doc = self._tokenize_text(chunk.contextual_content)
                documents.append(tokenized_doc)

                # Store metadata for result mapping
                chunk_metadata.append({
                    'chunk_id': str(chunk.id),
                    'document_id': str(chunk.document_id),
                    'chunk_content': chunk.content,
                    'contextual_content': chunk.contextual_content,
                    'document_title': doc_title,
                    'chunk_index': chunk.chunk_index,
                    'page_number': chunk.page_number
                })

            # Build BM25 index
            self.bm25_index = BM25Okapi(documents)
            self.chunk_metadata = chunk_metadata

            # Cache the index
            try:
                with open(cache_file, 'wb') as f:
                    pickle.dump(self.bm25_index, f)
                with open(metadata_file, 'wb') as f:
                    pickle.dump(self.chunk_metadata, f)

                logger.info(f"Cached BM25 index with {len(chunk_metadata)} chunks")
            except Exception as e:
                logger.warning(f"Failed to cache BM25 index: {e}")

            logger.info(f"Built BM25 index with {len(chunk_metadata)} chunks")
            return True

        except Exception as e:
            logger.error(f"Error building BM25 index: {e}")
            return False

    def search(
            self,
            query: str,
            limit: int = 10,
            min_score: float = 0.0
    ) -> List[BM25Result]:
        """
        Search using BM25 on contextual content.
        
        Args:
            query: Search query
            limit: Maximum results to return
            min_score: Minimum BM25 score threshold
            
        Returns:
            List of BM25 search results
        """
        if not self.bm25_index or not self.chunk_metadata:
            logger.warning("BM25 index not built. Call build_index() first.")
            return []

        try:
            # Tokenize query
            tokenized_query = self._tokenize_text(query)

            # Get BM25 scores
            scores = self.bm25_index.get_scores(tokenized_query)

            # Get top results with scores above threshold
            scored_results = [
                (idx, score) for idx, score in enumerate(scores)
                if score >= min_score
            ]

            # Sort by score (descending) and limit
            scored_results.sort(key=lambda x: x[1], reverse=True)
            scored_results = scored_results[:limit]

            # Convert to BM25Result objects
            results = []
            for idx, score in scored_results:
                metadata = self.chunk_metadata[idx]

                result = BM25Result(
                    chunk_id=metadata['chunk_id'],
                    document_id=metadata['document_id'],
                    score=score,
                    chunk_content=metadata['chunk_content'],
                    contextual_content=metadata['contextual_content'],
                    document_title=metadata['document_title']
                )
                results.append(result)

            return results

        except Exception as e:
            logger.error(f"Error in BM25 search: {e}")
            return []

    def _tokenize_text(self, text: str) -> List[str]:
        """
        Tokenize text for BM25 indexing.
        Implements basic tokenization with agricultural domain considerations.
        """
        if not text:
            return []

        # Convert to lowercase
        text = text.lower()

        # Remove special characters but keep agricultural terms intact
        # Pattern preserves: letters, numbers, hyphens (for compound terms), periods (for decimals)
        text = re.sub(r'[^\w\s\-\.]', ' ', text)

        # Split on whitespace
        tokens = text.split()

        # Filter out very short tokens and common stop words
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'this', 'that', 'these', 'those', 'it', 'its', 'they', 'them', 'their'
        }

        filtered_tokens = [
            token for token in tokens
            if len(token) >= 2 and token not in stop_words and not token.isdigit()
        ]

        return filtered_tokens

    def get_index_stats(self) -> Dict[str, Any]:
        """Get statistics about the BM25 index."""
        if not self.bm25_index or not self.chunk_metadata:
            return {'indexed': False}

        return {
            'indexed': True,
            'total_chunks': len(self.chunk_metadata),
            'vocabulary_size': len(self.bm25_index.idf),
            'average_document_length': self.bm25_index.avgdl,
            'cache_dir': str(self.cache_dir)
        }

    async def update_index_incremental(
            self,
            session: AsyncSession,
            new_chunk_ids: List[UUID]
    ) -> bool:
        """
        Update index incrementally with new chunks.
        Note: This is a simplified implementation. For production,
        you might want to use a more sophisticated incremental update.
        
        Args:
            session: Database session
            new_chunk_ids: List of new chunk IDs to add
            
        Returns:
            True if update successful
        """
        try:
            if not new_chunk_ids:
                return True

            # For now, we rebuild the entire index when new chunks are added
            # This could be optimized for large-scale deployments
            logger.info(f"Rebuilding BM25 index due to {len(new_chunk_ids)} new chunks")
            return await self.build_index(session, force_rebuild=True)

        except Exception as e:
            logger.error(f"Error updating BM25 index: {e}")
            return False

    def clear_cache(self):
        """Clear BM25 index cache."""
        try:
            cache_file = self.cache_dir / "bm25_index.pkl"
            metadata_file = self.cache_dir / "chunk_metadata.pkl"

            if cache_file.exists():
                cache_file.unlink()
            if metadata_file.exists():
                metadata_file.unlink()

            self.bm25_index = None
            self.chunk_metadata = []

            logger.info("Cleared BM25 index cache")

        except Exception as e:
            logger.error(f"Error clearing BM25 cache: {e}")
