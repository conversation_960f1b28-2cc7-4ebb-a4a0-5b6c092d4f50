from sqlalchemy import Column, String, DateTime, Index, JSON, Integer
from datetime import datetime

from models import Base


class WebKnowledge(Base):
    __tablename__ = "web_knowledge"
    __table_args__ = (
        # Simple indexes for retrieval and deduplication
        Index('idx_web_knowledge_message_id', 'message_id'),
        Index('idx_web_knowledge_content_hash', 'content_hash'),
    )
    
    id = Column(Integer, primary_key=True)
    message_id = Column(String, index=True, nullable=False)      # Link to conversation/message system
    query = Column(String, nullable=False)                       # Web search query
    results = Column(JSON, nullable=False)          # Only high-score, deduplicated results
    content_hash = Column(String, index=True, nullable=False)    # For deduplication
    created_at = Column(DateTime, default=datetime.utcnow)       # When stored