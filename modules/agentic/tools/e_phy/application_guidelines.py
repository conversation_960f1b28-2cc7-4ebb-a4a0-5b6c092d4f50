"""
Application Guidelines Tool for E-Phy Agricultural Database.
Provides dosage, timing, and application instructions for specific crop-product combinations.
"""
from dataclasses import dataclass
from typing import List, Optional, Dict, Any

from sqlalchemy import select, and_, distinct
from sqlalchemy.ext.asyncio import AsyncSession

from models.e_phy import Product, ProductUse, Crop, Target, UsagePattern
from .types import ApplicationStage, DataQuality, EquipmentType


@dataclass
class DoseInformation:
    """Dosage information with normalization."""
    min_dose: Optional[float]
    max_dose: Optional[float]
    unit: str
    unit_normalized: str
    recommended_dose: Optional[float]
    dose_notes: List[str]
    confidence_level: float


@dataclass
class ApplicationTiming:
    """Application timing information."""
    growth_stage_min: Optional[int]
    growth_stage_max: Optional[int]
    optimal_season: List[str]
    application_window: Optional[str]
    timing_notes: List[str]
    bbch_stages: List[str]


@dataclass
class ApplicationRestrictions:
    """Application restrictions and limitations."""
    harvest_interval_days: Optional[int]
    max_applications_per_season: Optional[int]
    min_interval_between_applications: Optional[int]
    weather_restrictions: List[str]
    equipment_restrictions: List[str]
    regulatory_restrictions: List[str]


@dataclass
class EnvironmentalSafeguards:
    """Environmental protection requirements."""
    aquatic_buffer_zone_m: Optional[int]
    arthropod_buffer_zone_m: Optional[int]
    plant_buffer_zone_m: Optional[int]
    max_buffer_zone_m: Optional[int]
    special_conditions: List[str]
    environmental_risk: str


@dataclass
class FieldCalculation:
    """Field-specific calculations."""
    total_product_needed: Optional[str]
    water_volume_range: Optional[str]
    application_rate_per_hectare: Optional[str]
    cost_estimate_range: Optional[str]
    equipment_settings: Dict[str, Any]


@dataclass
class DataQualityIndicator:
    """Data quality assessment for guidelines."""
    dose_info_quality: DataQuality
    timing_info_quality: DataQuality
    restriction_info_quality: DataQuality
    overall_reliability: float
    missing_critical_data: List[str]
    inferred_values: List[str]
    data_completeness_score: float
    reliability_category: str  # "high", "medium", "low"
    trust_factors: List[str]  # What makes this data trustworthy
    caution_flags: List[str]  # What the AI should be cautious about


@dataclass
class ApplicationGuidelineResponse:
    """Complete application guidelines response optimized for AI agent decision-making."""
    product_name: str
    crop_name: str
    target_name: Optional[str]
    dose_information: DoseInformation
    application_timing: ApplicationTiming
    restrictions: ApplicationRestrictions
    environmental_safeguards: EnvironmentalSafeguards
    field_calculation: FieldCalculation
    data_quality_indicators: DataQualityIndicator
    weather_considerations: List[str]
    equipment_recommendations: List[str]
    expert_tips: List[str]
    alternative_strategies: List[str]

    # Enhanced AI-friendly fields
    decision_summary: str  # Clear summary for AI decision making
    safety_alerts: List[str]  # Critical safety information
    regulatory_compliance: Dict[str, Any]  # Compliance requirements
    application_feasibility: str  # "recommended", "possible_with_caution", "not_recommended"
    confidence_level: str  # "high", "medium", "low"
    key_success_factors: List[str]  # What's critical for success
    potential_issues: List[str]  # What could go wrong


async def get_application_guidelines(
        session: AsyncSession,
        product_id: int,
        crop_name: str,
        target_pest: Optional[str] = None,
        application_stage: Optional[ApplicationStage] = None,
        field_size: Optional[float] = None,
        equipment_type: Optional[EquipmentType] = None,
        weather_conditions: Optional[Dict[str, Any]] = None,
        _recursion_depth: int = 0
) -> Optional[ApplicationGuidelineResponse]:
    """
    Get comprehensive application guidelines for agricultural AI agents.
    
    Provides dosage, timing, safety, and regulatory information optimized for AI decision-making.
    Includes data quality assessment, safety alerts, and potential issues to help AI agents
    make informed recommendations to farmers.
    
    Args:
        product_id: Product identifier (must be positive integer)
        crop_name: Target crop name (supports fuzzy matching and synonyms)
        target_pest: Optional specific target pest (supports fuzzy matching)
        application_stage: Application stage (preventive, curative, systematic)
        field_size: Field size in hectares for calculations (must be positive)
        equipment_type: Application equipment type (sprayer, drone, etc.)
        weather_conditions: Current weather conditions for contextual advice
    
    Returns:
        ApplicationGuidelineResponse with detailed guidelines optimized for AI agents,
        or None if no suitable guidelines found
    
    Raises:
        ValueError: If input parameters are invalid
        DatabaseError: If database query fails
    """

    # Input validation
    if product_id <= 0:
        raise ValueError("Product ID must be a positive integer")

    if not crop_name or not crop_name.strip():
        raise ValueError("Crop name cannot be empty")

    if field_size is not None and field_size <= 0:
        raise ValueError("Field size must be positive")

    crop_name = crop_name.strip()

    # Find the product
    product_query = select(Product).where(Product.id == product_id)
    result = await session.execute(product_query)
    product = result.scalar_one_or_none()

    if not product:
        return None

    # Find matching crop
    crop = await _find_matching_crop(session, crop_name)
    if not crop:
        return None

    # Find matching target if specified
    target = None
    if target_pest:
        target = await _find_matching_target(session, target_pest)

    # Find product use records
    product_uses = await _find_product_uses(session, product_id, crop.id, target.id if target else None)

    if not product_uses:
        # Try to find alternative products for same crop-target combination (avoid recursion)
        if _recursion_depth < 1:  # Limit recursion depth
            alternative_products = await _find_alternative_products(session, crop.id, target.id if target else None,
                                                                    product.function_category)
            if alternative_products:
                return await _generate_alternative_product_response(session, alternative_products, crop, target,
                                                                    product, _recursion_depth)

        # If no alternatives found or max recursion reached, provide general guidance
        return await _generate_general_guidance(session, crop, target, product)

    # If we have product uses but none are authorized, still try alternatives
    authorized_uses = [pu for pu in product_uses if pu.is_currently_authorized]
    if not authorized_uses and _recursion_depth < 1:
        alternative_products = await _find_alternative_products(session, crop.id, target.id if target else None,
                                                                product.function_category)
        if alternative_products:
            return await _generate_alternative_product_response(session, alternative_products, crop, target, product,
                                                                _recursion_depth)

    # Select best matching product use
    best_use = _select_best_product_use(product_uses, application_stage)

    # Get usage patterns for statistical inference
    usage_patterns = await _get_usage_patterns(session, crop.id, target.id if target else None)

    # Build response components
    dose_info = await _build_dose_information(session, best_use, usage_patterns, application_stage)
    timing_info = _build_timing_information(best_use, usage_patterns, application_stage)
    restrictions = await _build_restrictions(session, best_use, usage_patterns)
    env_safeguards = _build_environmental_safeguards(best_use)
    field_calc = _calculate_field_requirements(dose_info, field_size, equipment_type)
    data_quality = _assess_data_quality(best_use, dose_info, timing_info, restrictions)

    # Generate recommendations and considerations
    weather_considerations = _generate_weather_considerations(weather_conditions, product.function_category)
    equipment_recommendations = _generate_equipment_recommendations(dose_info, equipment_type)
    expert_tips = _generate_expert_tips(best_use, application_stage, data_quality)
    alternative_strategies = await _generate_alternative_strategies(session, product, crop, target)

    # Generate AI-friendly decision support fields
    decision_summary = _generate_decision_summary(product, crop, target, dose_info, data_quality)
    safety_alerts = _generate_safety_alerts(best_use, env_safeguards, restrictions)
    regulatory_compliance = _generate_regulatory_compliance(best_use, product)
    application_feasibility = _assess_application_feasibility(data_quality, dose_info, restrictions)
    confidence_level = _determine_confidence_level(data_quality)
    key_success_factors = _identify_success_factors(best_use, application_stage, dose_info)
    potential_issues = _identify_potential_issues(best_use, data_quality, env_safeguards)

    return ApplicationGuidelineResponse(
        product_name=product.product_name,
        crop_name=crop.crop_name,
        target_name=target.target_name if target else None,
        dose_information=dose_info,
        application_timing=timing_info,
        restrictions=restrictions,
        environmental_safeguards=env_safeguards,
        field_calculation=field_calc,
        data_quality_indicators=data_quality,
        weather_considerations=weather_considerations,
        equipment_recommendations=equipment_recommendations,
        expert_tips=expert_tips,
        alternative_strategies=alternative_strategies,
        decision_summary=decision_summary,
        safety_alerts=safety_alerts,
        regulatory_compliance=regulatory_compliance,
        application_feasibility=application_feasibility,
        confidence_level=confidence_level,
        key_success_factors=key_success_factors,
        potential_issues=potential_issues
    )


async def _find_matching_crop(session: AsyncSession, crop_name: str) -> Optional[Crop]:
    """Find matching crop with fuzzy matching."""
    # Exact match
    query = select(Crop).where(Crop.crop_name.ilike(crop_name))
    result = await session.execute(query)
    crop = result.scalar_one_or_none()
    if crop:
        return crop

    # Normalized match
    query = select(Crop).where(Crop.normalized_name.ilike(f"%{crop_name.lower()}%"))
    result = await session.execute(query)
    crop = result.scalar_one_or_none()
    if crop:
        return crop

    # Synonym match
    query = select(Crop).where(Crop.common_synonyms.ilike(f"%{crop_name}%"))
    result = await session.execute(query)
    crop = result.scalar_one_or_none()

    return crop


async def _find_matching_target(session: AsyncSession, target_pest: str) -> Optional[Target]:
    """Find matching target with enhanced fuzzy matching and synonym support."""
    # Exact match
    query = select(Target).where(Target.target_name.ilike(target_pest))
    result = await session.execute(query)
    target = result.scalars().first()
    if target:
        return target

    # Normalized match
    query = select(Target).where(Target.normalized_name.ilike(f"%{target_pest.lower()}%")).limit(1)
    result = await session.execute(query)
    target = result.scalars().first()
    if target:
        return target

    # Synonym match (if synonyms field exists)
    query = select(Target).where(Target.common_synonyms.ilike(f"%{target_pest}%")).limit(1)
    result = await session.execute(query)
    target = result.scalars().first()
    if target:
        return target

    # Partial match (with limit to prevent crashes)
    query = select(Target).where(Target.target_name.ilike(f"%{target_pest}%")).limit(1)
    result = await session.execute(query)
    target = result.scalars().first()

    return target


async def _find_product_uses(
        session: AsyncSession,
        product_id: int,
        crop_id: int,
        target_id: Optional[int]
) -> List[ProductUse]:
    """Find product use records for the combination."""
    query = select(ProductUse).where(
        and_(
            ProductUse.product_id == product_id,
            ProductUse.crop_id == crop_id
        )
    )

    if target_id:
        query = query.where(ProductUse.target_id == target_id)

    # Prefer currently authorized uses
    query = query.order_by(ProductUse.is_currently_authorized.desc())

    result = await session.execute(query)
    return result.scalars().all()


def _select_best_product_use(
        product_uses: List[ProductUse],
        application_stage: Optional[ApplicationStage]
) -> Optional[ProductUse]:
    """Select the most appropriate product use record with enhanced logic for AI agents."""
    if not product_uses:
        return None

    # Convert to list to handle Sequence type
    product_uses = list(product_uses)

    # Filter by authorization status (prefer authorized)
    authorized_uses = [pu for pu in product_uses if pu.is_currently_authorized]
    if authorized_uses:
        product_uses = authorized_uses

    # Select based on application stage with AI-friendly logic
    if application_stage == "preventive":
        # Prefer lower doses for preventive applications
        valid_uses = [pu for pu in product_uses if pu.min_dose is not None]
        if valid_uses:
            return min(valid_uses, key=lambda x: x.min_dose)
        else:
            return max(product_uses, key=lambda x: _score_data_completeness(x))
    elif application_stage == "curative":
        # Prefer higher doses for curative applications
        valid_uses = [pu for pu in product_uses if pu.max_dose is not None]
        if valid_uses:
            return max(valid_uses, key=lambda x: x.max_dose)
        else:
            return max(product_uses, key=lambda x: _score_data_completeness(x))
    else:
        # Default: select use with most complete data for best AI decision support
        return max(product_uses, key=lambda x: _score_data_completeness(x))


def _score_data_completeness(product_use: ProductUse) -> int:
    """Score product use record based on data completeness."""
    score = 0
    if product_use.min_dose is not None:
        score += 1
    if product_use.max_dose is not None:
        score += 1
    if product_use.dose_unit:
        score += 1
    if product_use.harvest_interval_days is not None:
        score += 1
    if product_use.max_applications is not None:
        score += 1
    if product_use.application_comments:
        score += 1
    return score


async def _get_usage_patterns(
        session: AsyncSession,
        crop_id: int,
        target_id: Optional[int]
) -> Optional[UsagePattern]:
    """Get usage patterns for statistical inference."""
    if not target_id:
        return None

    query = select(UsagePattern).where(
        and_(
            UsagePattern.crop_id == crop_id,
            UsagePattern.target_id == target_id
        )
    )

    result = await session.execute(query)
    return result.scalar_one_or_none()


async def _build_dose_information(
        session: AsyncSession,
        product_use: ProductUse,
        usage_patterns: Optional[UsagePattern],
        application_stage: Optional[ApplicationStage]
) -> DoseInformation:
    """Build comprehensive dose information."""

    # Get base dose information
    min_dose = product_use.min_dose
    max_dose = product_use.max_dose
    unit = product_use.dose_unit or "Unknown"

    # Normalize dose unit
    unit_normalized = _normalize_dose_unit(unit)

    # Calculate recommended dose
    recommended_dose = None
    if min_dose is not None and max_dose is not None:
        if application_stage == "preventive":
            recommended_dose = min_dose + (max_dose - min_dose) * 0.3
        elif application_stage == "curative":
            recommended_dose = min_dose + (max_dose - min_dose) * 0.8
        else:
            recommended_dose = (min_dose + max_dose) / 2
    elif usage_patterns and usage_patterns.avg_dose:
        recommended_dose = usage_patterns.avg_dose

    # Generate dose notes with validation
    dose_notes = []

    # Basic dose availability checks
    if min_dose is None or max_dose is None:
        if usage_patterns and usage_patterns.avg_dose:
            dose_notes.append(f"Dose inferred from usage patterns: {usage_patterns.avg_dose:.2f}")
        else:
            dose_notes.append("Specific dose information not available - consult label")

    if unit == "Unknown" or not unit:
        dose_notes.append("Dose unit not specified - verify with product label")

    # Validate extreme dosage values (from database analysis)
    if min_dose and min_dose > 1000 and unit_normalized in ["liters_per_hectare", "kilograms_per_hectare"]:
        dose_notes.append(f"⚠️ High dosage detected: {min_dose} {unit} - verify with product label")

    if max_dose and max_dose > 10000 and unit_normalized in ["liters_per_hectare", "kilograms_per_hectare"]:
        dose_notes.append(f"⚠️ Very high dosage detected: {max_dose} {unit} - double-check requirements")

    # Check for extremely low doses that might be errors
    if min_dose and min_dose < 0.001 and unit_normalized in ["liters_per_hectare", "kilograms_per_hectare"]:
        dose_notes.append(f"⚠️ Very low dosage: {min_dose} {unit} - confirm unit is correct")

    # Assess confidence level with enhanced logic for AI decision support
    confidence_level = 1.0
    if min_dose is None or max_dose is None:
        confidence_level *= 0.7
    if not unit or unit == "Unknown":
        confidence_level *= 0.8
    if not product_use.is_currently_authorized:
        confidence_level *= 0.9  # Slight reduction for non-authorized products

    # Additional confidence adjustments for extreme values
    if min_dose and min_dose > 1000 and unit_normalized in ["liters_per_hectare", "kilograms_per_hectare"]:
        confidence_level *= 0.8  # Reduce confidence for suspicious high values
    if min_dose and min_dose < 0.001 and unit_normalized in ["liters_per_hectare", "kilograms_per_hectare"]:
        confidence_level *= 0.8  # Reduce confidence for suspicious low values

    return DoseInformation(
        min_dose=min_dose,
        max_dose=max_dose,
        unit=unit,
        unit_normalized=unit_normalized,
        recommended_dose=recommended_dose,
        dose_notes=dose_notes,
        confidence_level=confidence_level
    )


def _normalize_dose_unit(unit: str) -> str:
    """Normalize dose units to standard formats for AI processing."""
    if not unit:
        return "unknown_unit"

    # Enhanced unit mapping covering more cases found in database
    unit_mapping = {
        "L/ha": "liters_per_hectare",
        "kg/ha": "kilograms_per_hectare",
        "L/hL": "liters_per_hectoliter",
        "kg/hL": "kilograms_per_hectoliter",
        "mL/m²": "milliliters_per_square_meter",
        "g/m²": "grams_per_square_meter",
        "mL/L": "milliliters_per_liter",
        "g/L": "grams_per_liter",
        "mL/10 m²": "milliliters_per_10_square_meters",
        "g/10 m²": "grams_per_10_square_meters",
        "L/q": "liters_per_quintal",
        "L/t": "liters_per_tonne",
        "g/m3": "grams_per_cubic_meter",
        "diffuseurs/ha": "diffusers_per_hectare",
        "g/plant": "grams_per_plant"
    }

    return unit_mapping.get(unit, unit.lower().replace("/", "_per_").replace("²", "_squared").replace("³", "_cubed"))


def _build_timing_information(
        product_use: ProductUse,
        usage_patterns: Optional[UsagePattern],
        application_stage: Optional[ApplicationStage]
) -> ApplicationTiming:
    """Build application timing information."""

    # Get growth stage information
    growth_stage_min = product_use.min_growth_stage
    growth_stage_max = product_use.max_growth_stage

    # Get seasonal information
    optimal_season = []
    if product_use.application_season_min and product_use.application_season_max:
        optimal_season = [product_use.application_season_min, product_use.application_season_max]

    # Generate application window
    application_window = None
    if growth_stage_min and growth_stage_max:
        application_window = f"BBCH {growth_stage_min}-{growth_stage_max}"
    elif growth_stage_min:
        application_window = f"BBCH {growth_stage_min}+"

    # Generate timing notes
    timing_notes = []
    if product_use.application_comments:
        timing_notes.append(product_use.application_comments)

    if application_stage == "preventive":
        timing_notes.append("Apply as preventive measure before disease/pest pressure")
    elif application_stage == "curative":
        timing_notes.append("Apply at first signs of disease/pest presence")

    if usage_patterns and usage_patterns.common_application_timing:
        timing_notes.append(f"Common timing: {usage_patterns.common_application_timing}")

    # Convert growth stages to BBCH descriptions
    bbch_stages = []
    if growth_stage_min:
        bbch_stages.append(_get_bbch_description(growth_stage_min))
    if growth_stage_max and growth_stage_max != growth_stage_min:
        bbch_stages.append(_get_bbch_description(growth_stage_max))

    return ApplicationTiming(
        growth_stage_min=growth_stage_min,
        growth_stage_max=growth_stage_max,
        optimal_season=optimal_season,
        application_window=application_window,
        timing_notes=timing_notes,
        bbch_stages=bbch_stages
    )


def _get_bbch_description(stage: int) -> str:
    """Get BBCH stage description."""
    # Simplified BBCH stage descriptions
    stage_descriptions = {
        10: "First leaves unfolding",
        20: "Tillering/Side shoots",
        30: "Stem elongation",
        40: "Booting/Inflorescence emergence",
        50: "Heading/Flowering",
        60: "Flowering/Anthesis",
        70: "Fruit development",
        80: "Fruit ripening",
        90: "Senescence"
    }

    # Find closest stage
    closest_stage = min(stage_descriptions.keys(), key=lambda x: abs(x - stage))
    return f"BBCH {stage}: {stage_descriptions.get(closest_stage, 'Growth stage')}"


async def _build_restrictions(
        session: AsyncSession,
        product_use: ProductUse,
        usage_patterns: Optional[UsagePattern]
) -> ApplicationRestrictions:
    """Build application restrictions."""

    # Get harvest interval with inference
    harvest_interval = product_use.harvest_interval_days
    if harvest_interval is None and usage_patterns:
        harvest_interval = int(usage_patterns.avg_harvest_interval) if usage_patterns.avg_harvest_interval else None

    # Get application limits
    max_applications = product_use.max_applications
    if max_applications is None and usage_patterns:
        max_applications = int(usage_patterns.avg_max_applications) if usage_patterns.avg_max_applications else None

    # Get interval between applications
    min_interval = product_use.min_interval_between_applications

    # Generate restrictions
    weather_restrictions = []
    equipment_restrictions = []
    regulatory_restrictions = []

    # Weather restrictions (general guidance)
    weather_restrictions.extend([
        "Avoid application during strong winds (>15 km/h)",
        "Do not apply if rain expected within 6 hours",
        "Avoid application during extreme temperatures"
    ])

    # Equipment restrictions based on formulation
    equipment_restrictions.append("Use appropriate spray equipment for formulation type")

    # Regulatory restrictions
    if harvest_interval:
        regulatory_restrictions.append(f"Respect harvest interval: {harvest_interval} days")

    if max_applications:
        regulatory_restrictions.append(f"Maximum {max_applications} applications per season")

    return ApplicationRestrictions(
        harvest_interval_days=harvest_interval,
        max_applications_per_season=max_applications,
        min_interval_between_applications=min_interval,
        weather_restrictions=weather_restrictions,
        equipment_restrictions=equipment_restrictions,
        regulatory_restrictions=regulatory_restrictions
    )


def _build_environmental_safeguards(product_use: ProductUse) -> EnvironmentalSafeguards:
    """Build environmental protection requirements."""

    aquatic_buffer = product_use.aquatic_buffer_zone
    arthropod_buffer = product_use.arthropod_buffer_zone
    plant_buffer = product_use.plant_buffer_zone
    max_buffer = product_use.max_buffer_zone

    # Special conditions
    special_conditions = []
    if aquatic_buffer:
        special_conditions.append(f"Maintain {aquatic_buffer}m distance from water bodies")

    if arthropod_buffer:
        special_conditions.append(f"Protect beneficial arthropods - {arthropod_buffer}m buffer")

    if product_use.has_special_conditions:
        special_conditions.append("Additional special conditions apply - check label")

    # Assess environmental risk
    risk_level = "moderate"
    if any([aquatic_buffer and aquatic_buffer > 10,
            arthropod_buffer and arthropod_buffer > 5,
            max_buffer and max_buffer > 20]):
        risk_level = "high"
    elif not any([aquatic_buffer, arthropod_buffer, plant_buffer]):
        risk_level = "low"

    return EnvironmentalSafeguards(
        aquatic_buffer_zone_m=aquatic_buffer,
        arthropod_buffer_zone_m=arthropod_buffer,
        plant_buffer_zone_m=plant_buffer,
        max_buffer_zone_m=max_buffer,
        special_conditions=special_conditions,
        environmental_risk=risk_level
    )


def _calculate_field_requirements(
        dose_info: DoseInformation,
        field_size: Optional[float],
        equipment_type: Optional[EquipmentType]
) -> FieldCalculation:
    """Calculate field-specific requirements."""

    if not field_size or not dose_info.recommended_dose:
        return FieldCalculation(
            total_product_needed=None,
            water_volume_range=None,
            application_rate_per_hectare=None,
            cost_estimate_range=None,
            equipment_settings={}
        )

    # Calculate product needed
    total_product = dose_info.recommended_dose * field_size
    unit_short = dose_info.unit.split('/')[0] if '/' in dose_info.unit else dose_info.unit
    total_product_needed = f"{total_product:.2f} {unit_short}"

    # Estimate water volume (typical spray volumes)
    if dose_info.unit_normalized == "liters_per_hectare":
        water_volume_range = f"{200 * field_size:.0f}-{400 * field_size:.0f} L"
    else:
        water_volume_range = f"{200 * field_size:.0f}-{300 * field_size:.0f} L"

    # Application rate
    application_rate = f"{dose_info.recommended_dose:.2f} {dose_info.unit}"

    # Equipment settings
    equipment_settings = {}
    if equipment_type == "sprayer":
        equipment_settings = {
            "spray_pressure": "2-3 bar",
            "nozzle_type": "flat fan or hollow cone",
            "travel_speed": "8-12 km/h"
        }
    elif equipment_type == "drone":
        equipment_settings = {
            "flight_height": "2-3 meters",
            "spray_rate": "1-2 L/min",
            "overlap": "20-30%"
        }

    return FieldCalculation(
        total_product_needed=total_product_needed,
        water_volume_range=water_volume_range,
        application_rate_per_hectare=application_rate,
        cost_estimate_range=None,  # Would need pricing data
        equipment_settings=equipment_settings
    )


def _assess_data_quality(
        product_use: ProductUse,
        dose_info: DoseInformation,
        timing_info: ApplicationTiming,
        restrictions: ApplicationRestrictions
) -> DataQualityIndicator:
    """Assess data quality for the guidelines with enhanced AI decision support."""

    # Dose information quality
    dose_quality = "excellent"
    if dose_info.confidence_level < 0.8:
        dose_quality = "fair"
    elif dose_info.confidence_level < 0.9:
        dose_quality = "good"

    # Timing information quality
    timing_quality = "good"
    if not timing_info.growth_stage_min and not timing_info.optimal_season:
        timing_quality = "poor"
    elif not timing_info.growth_stage_min or not timing_info.optimal_season:
        timing_quality = "fair"

    # Restriction information quality
    restriction_quality = "good"
    if not restrictions.harvest_interval_days and not restrictions.max_applications_per_season:
        restriction_quality = "poor"
    elif not restrictions.harvest_interval_days or not restrictions.max_applications_per_season:
        restriction_quality = "fair"

    # Overall reliability
    quality_scores = {
        "excellent": 1.0, "good": 0.8, "fair": 0.6, "poor": 0.4, "unavailable": 0.0
    }

    overall_reliability = (
            quality_scores[dose_quality] * 0.4 +
            quality_scores[timing_quality] * 0.3 +
            quality_scores[restriction_quality] * 0.3
    )

    # Calculate data completeness score
    completeness_factors = [
        dose_info.min_dose is not None,
        dose_info.max_dose is not None,
        dose_info.unit is not None,
        restrictions.harvest_interval_days is not None,
        restrictions.max_applications_per_season is not None,
        timing_info.growth_stage_min is not None,
        product_use.is_currently_authorized
    ]
    data_completeness_score = sum(completeness_factors) / len(completeness_factors)

    # Determine reliability category
    if overall_reliability >= 0.8:
        reliability_category = "high"
    elif overall_reliability >= 0.6:
        reliability_category = "medium"
    else:
        reliability_category = "low"

    # Identify trust factors
    trust_factors = []
    if product_use.is_currently_authorized:
        trust_factors.append("Product is currently authorized")
    if dose_info.confidence_level >= 0.9:
        trust_factors.append("High confidence dosage data")
    if restrictions.harvest_interval_days:
        trust_factors.append("Harvest interval specified")
    if timing_info.growth_stage_min:
        trust_factors.append("Growth stage timing available")

    # Identify caution flags
    caution_flags = []
    if not product_use.is_currently_authorized:
        caution_flags.append("Product authorization status uncertain")
    if dose_info.confidence_level < 0.8:
        caution_flags.append("Dosage information incomplete or inferred")
    if not restrictions.harvest_interval_days:
        caution_flags.append("Harvest interval not specified - check label")
    if overall_reliability < 0.6:
        caution_flags.append("Low data reliability - verify with local advisor")

    # Identify missing critical data
    missing_critical_data = []
    if dose_info.min_dose is None or dose_info.max_dose is None:
        missing_critical_data.append("Specific dosage information")

    if not restrictions.harvest_interval_days:
        missing_critical_data.append("Harvest interval")

    if not timing_info.growth_stage_min:
        missing_critical_data.append("Application timing")

    # Identify inferred values
    inferred_values = []
    if dose_info.confidence_level < 1.0:
        inferred_values.extend(dose_info.dose_notes)

    return DataQualityIndicator(
        dose_info_quality=dose_quality,
        timing_info_quality=timing_quality,
        restriction_info_quality=restriction_quality,
        overall_reliability=overall_reliability,
        missing_critical_data=missing_critical_data,
        inferred_values=inferred_values,
        data_completeness_score=data_completeness_score,
        reliability_category=reliability_category,
        trust_factors=trust_factors,
        caution_flags=caution_flags
    )


def _generate_weather_considerations(
        weather_conditions: Optional[Dict[str, Any]],
        function_category: Optional[str]
) -> List[str]:
    """Generate weather-specific considerations."""
    considerations = []

    # General weather considerations
    considerations.extend([
        "Check weather forecast before application",
        "Avoid application before rain or during high humidity",
        "Wind speed should be less than 15 km/h"
    ])

    # Function-specific considerations
    if function_category and "fongicide" in function_category.lower():
        considerations.append("Avoid application during dewfall for fungicides")

    if function_category and "herbicide" in function_category.lower():
        considerations.append("Optimal herbicide activity requires good growing conditions")

    # Current weather considerations
    if weather_conditions:
        temp = weather_conditions.get("temperature")
        if temp and temp > 25:
            considerations.append("⚠️ High temperature - risk of volatilization")
        elif temp and temp < 5:
            considerations.append("⚠️ Low temperature - reduced efficacy possible")

        humidity = weather_conditions.get("humidity")
        if humidity and humidity > 80:
            considerations.append("⚠️ High humidity - delay application if possible")

    return considerations


def _generate_equipment_recommendations(
        dose_info: DoseInformation,
        equipment_type: Optional[EquipmentType]
) -> List[str]:
    """Generate equipment-specific recommendations."""
    recommendations = []

    # General recommendations
    recommendations.extend([
        "Calibrate spray equipment before application",
        "Use appropriate nozzle type for product formulation",
        "Maintain consistent travel speed and pressure"
    ])

    # Dose-specific recommendations
    if dose_info.unit_normalized == "liters_per_hectare":
        recommendations.append("Ensure accurate volume delivery for liquid formulations")
    elif dose_info.unit_normalized == "kilograms_per_hectare":
        recommendations.append("Use appropriate spreader settings for granular products")

    # Equipment-specific recommendations
    if equipment_type == "drone":
        recommendations.extend([
            "Ensure proper droplet size for drone application",
            "Account for drift with appropriate buffer zones"
        ])
    elif equipment_type == "sprayer":
        recommendations.append("Regular maintenance and cleaning of spray lines")

    return recommendations


def _generate_expert_tips(
        product_use: ProductUse,
        application_stage: Optional[ApplicationStage],
        data_quality: DataQualityIndicator
) -> List[str]:
    """Generate expert application tips."""
    tips = []

    # Application stage tips
    if application_stage == "preventive":
        tips.append("💡 Preventive applications are most effective before pressure builds")
    elif application_stage == "curative":
        tips.append("💡 Curative applications require thorough coverage and optimal conditions")

    # Data quality tips
    if data_quality.overall_reliability < 0.7:
        tips.append("⚠️ Limited data available - verify recommendations with local advisor")

    # Product-specific tips
    if product_use.max_applications and product_use.max_applications <= 2:
        tips.append("🎯 Limited applications allowed - ensure optimal timing")

    if product_use.harvest_interval_days and product_use.harvest_interval_days > 14:
        tips.append("⏰ Long harvest interval - plan application timing carefully")

    # General tips
    tips.extend([
        "📊 Keep application records for regulatory compliance",
        "🔄 Rotate modes of action to prevent resistance"
    ])

    return tips


async def _generate_alternative_strategies(
        session: AsyncSession,
        product: Product,
        crop: Crop,
        target: Optional[Target]
) -> List[str]:
    """Generate alternative management strategies."""
    strategies = []

    # Look for biological alternatives
    if product.authorized_mentions and "biologique" not in product.authorized_mentions.lower():
        strategies.append("Consider biological control options")

    # Function-specific alternatives
    if product.function_category:
        if "fongicide" in product.function_category.lower():
            strategies.extend([
                "Improve air circulation and reduce humidity",
                "Use resistant varieties when available"
            ])
        elif "herbicide" in product.function_category.lower():
            strategies.extend([
                "Implement mechanical weed control",
                "Use cover crops for weed suppression"
            ])
        elif "insecticide" in product.function_category.lower():
            strategies.extend([
                "Monitor pest populations with traps",
                "Encourage beneficial insects"
            ])

    # General IPM strategies
    strategies.extend([
        "Implement integrated pest management (IPM)",
        "Consider cultural control methods"
    ])

    return strategies[:4]  # Limit to most relevant


def _generate_decision_summary(
        product: Product,
        crop: Crop,
        target: Optional[Target],
        dose_info: DoseInformation,
        data_quality: DataQualityIndicator
) -> str:
    """Generate a clear decision summary for AI agents."""
    target_text = f" against {target.target_name}" if target else ""

    if data_quality.reliability_category == "high":
        confidence_text = "with high confidence"
    elif data_quality.reliability_category == "medium":
        confidence_text = "with moderate confidence"
    else:
        confidence_text = "with limited confidence - verification recommended"

    if dose_info.recommended_dose:
        dose_text = f"at {dose_info.recommended_dose:.2f} {dose_info.unit}"
    else:
        dose_text = f"at {dose_info.min_dose}-{dose_info.max_dose} {dose_info.unit}"

    return f"Apply {product.product_name} on {crop.crop_name}{target_text} {dose_text} {confidence_text}."


def _generate_safety_alerts(
        product_use: ProductUse,
        env_safeguards: EnvironmentalSafeguards,
        restrictions: ApplicationRestrictions
) -> List[str]:
    """Generate critical safety alerts for AI decision making."""
    alerts = []

    # Buffer zone alerts
    if env_safeguards.aquatic_buffer_zone_m and env_safeguards.aquatic_buffer_zone_m > 10:
        alerts.append(f"🚨 CRITICAL: Maintain {env_safeguards.aquatic_buffer_zone_m}m buffer from water bodies")

    if env_safeguards.arthropod_buffer_zone_m and env_safeguards.arthropod_buffer_zone_m > 5:
        alerts.append(f"⚠️ Protect beneficial insects: {env_safeguards.arthropod_buffer_zone_m}m buffer required")

    # Harvest interval alerts
    if restrictions.harvest_interval_days and restrictions.harvest_interval_days > 21:
        alerts.append(f"⏰ LONG harvest interval: {restrictions.harvest_interval_days} days - plan carefully")

    # Application limit alerts
    if restrictions.max_applications_per_season and restrictions.max_applications_per_season <= 2:
        alerts.append(f"🎯 LIMITED applications: Only {restrictions.max_applications_per_season} per season allowed")

    # Environmental risk alerts
    if env_safeguards.environmental_risk == "high":
        alerts.append("🌿 HIGH environmental risk - strict precautions required")

    return alerts


def _generate_regulatory_compliance(
        product_use: ProductUse,
        product: Product
) -> Dict[str, Any]:
    """Generate regulatory compliance information."""
    return {
        "authorization_status": "authorized" if product_use.is_currently_authorized else "check_status",
        "product_registration": product.registration_number,
        "usage_authorization": "current" if product_use.is_currently_authorized else "verify_required",
        "harvest_interval_mandatory": product_use.harvest_interval_days is not None,
        "max_applications_enforced": product_use.max_applications is not None,
        "buffer_zones_required": any([
            product_use.aquatic_buffer_zone,
            product_use.arthropod_buffer_zone,
            product_use.plant_buffer_zone
        ]),
        "special_conditions": product_use.has_special_conditions or False
    }


def _assess_application_feasibility(
        data_quality: DataQualityIndicator,
        dose_info: DoseInformation,
        restrictions: ApplicationRestrictions
) -> str:
    """Assess overall application feasibility."""

    # Check critical missing data
    if "Specific dosage information" in data_quality.missing_critical_data:
        return "not_recommended"

    # Check data reliability
    if data_quality.overall_reliability < 0.5:
        return "not_recommended"

    # Check if basic requirements are met
    if dose_info.min_dose is None or dose_info.max_dose is None:
        return "not_recommended"

    # Check for concerning restrictions
    concerning_factors = 0
    if not restrictions.harvest_interval_days:
        concerning_factors += 1
    if not restrictions.max_applications_per_season:
        concerning_factors += 1
    if data_quality.overall_reliability < 0.7:
        concerning_factors += 1

    if concerning_factors >= 2:
        return "possible_with_caution"

    return "recommended"


def _determine_confidence_level(data_quality: DataQualityIndicator) -> str:
    """Determine overall confidence level for AI decision making."""
    if data_quality.overall_reliability >= 0.8 and data_quality.data_completeness_score >= 0.8:
        return "high"
    elif data_quality.overall_reliability >= 0.6 and data_quality.data_completeness_score >= 0.6:
        return "medium"
    else:
        return "low"


def _identify_success_factors(
        product_use: ProductUse,
        application_stage: Optional[ApplicationStage],
        dose_info: DoseInformation
) -> List[str]:
    """Identify key factors for successful application."""
    factors = []

    # Authorization status
    if product_use.is_currently_authorized:
        factors.append("Product is currently authorized for this use")

    # Dose precision
    if dose_info.confidence_level >= 0.9:
        factors.append("Precise dosage information available")

    # Application timing
    if product_use.min_growth_stage:
        factors.append(f"Apply at BBCH growth stage {product_use.min_growth_stage}+")

    # Weather conditions
    factors.append("Ensure favorable weather conditions (no rain, low wind)")

    # Equipment calibration
    factors.append("Calibrate application equipment accurately")

    # Stage-specific factors
    if application_stage == "preventive":
        factors.append("Apply before pest/disease pressure develops")
    elif application_stage == "curative":
        factors.append("Ensure thorough coverage for curative treatment")

    return factors


def _identify_potential_issues(
        product_use: ProductUse,
        data_quality: DataQualityIndicator,
        env_safeguards: EnvironmentalSafeguards
) -> List[str]:
    """Identify potential issues and risks."""
    issues = []

    # Data quality issues
    if data_quality.overall_reliability < 0.7:
        issues.append("Limited data reliability - recommendations may be incomplete")

    if "Harvest interval" in data_quality.missing_critical_data:
        issues.append("Harvest interval unknown - risk of residue violations")

    # Authorization issues
    if not product_use.is_currently_authorized:
        issues.append("Product authorization status unclear - verify before use")

    # Environmental risks
    if env_safeguards.environmental_risk == "high":
        issues.append("High environmental risk - potential for ecological damage")

    if env_safeguards.aquatic_buffer_zone_m and env_safeguards.aquatic_buffer_zone_m > 20:
        issues.append("Large buffer zones required - may limit application area")

    # Application limitations
    if product_use.max_applications and product_use.max_applications <= 2:
        issues.append("Limited application opportunities - timing is critical")

    # Resistance risk
    if product_use.max_applications and product_use.max_applications > 4:
        issues.append("Multiple applications - consider resistance management")

    return issues


async def _find_alternative_products(
        session: AsyncSession,
        crop_id: int,
        target_id: Optional[int],
        function_category: Optional[str]
) -> List[Product]:
    """Find alternative products that work for the same crop-target combination."""

    # Find products with uses for this crop-target
    query = select(distinct(ProductUse.product_id)).where(
        and_(
            ProductUse.crop_id == crop_id,
            ProductUse.target_id == target_id if target_id else ProductUse.target_id.is_(None),
            ProductUse.is_currently_authorized == True
        )
    )

    result = await session.execute(query)
    product_ids = result.scalars().all()

    if not product_ids:
        # If no exact crop-target match, try just crop
        query = select(distinct(ProductUse.product_id)).where(
            and_(
                ProductUse.crop_id == crop_id,
                ProductUse.is_currently_authorized == True
            )
        ).limit(10)

        result = await session.execute(query)
        product_ids = result.scalars().all()

    if not product_ids:
        return []

    # Get products, preferring similar function category
    if function_category:
        # First try to find products with similar function category
        main_category = function_category.split()[0] if function_category else ""
        products_query = select(Product).where(
            and_(
                Product.id.in_(product_ids),
                Product.function_category.ilike(f"%{main_category}%"),
                Product.is_currently_authorized == True
            )
        ).limit(5)

        products_result = await session.execute(products_query)
        products = products_result.scalars().all()

        if products:
            return list(products)

    # If no similar category products, get any products that work
    products_query = select(Product).where(
        and_(
            Product.id.in_(product_ids),
            Product.is_currently_authorized == True
        )
    ).limit(5)

    products_result = await session.execute(products_query)
    return list(products_result.scalars().all())


async def _generate_alternative_product_response(
        session: AsyncSession,
        products: List[Product],
        crop: Crop,
        target: Optional[Target],
        original_product: Product,
        recursion_depth: int = 0
) -> Optional[ApplicationGuidelineResponse]:
    """Generate response suggesting alternative products."""

    # Pick the best alternative (first one, could add more sophisticated selection)
    best_product = products[0]

    # Get guidelines for the alternative product (prevent recursion)
    guidelines = await get_application_guidelines(
        session=session,
        product_id=best_product.id,
        crop_name=crop.crop_name,
        target_pest=target.target_name if target else None,
        _recursion_depth=recursion_depth + 1
    )

    if guidelines:
        # Modify the response to indicate it's an alternative
        guidelines.decision_summary = f"Alternative recommendation: {guidelines.decision_summary}"

        # Add explanation about the alternative
        explanation = f"💡 {original_product.product_name} is not authorized for {crop.crop_name}"
        if target:
            explanation += f" against {target.target_name}"
        explanation += f". Recommending {best_product.product_name} instead."

        guidelines.expert_tips.insert(0, explanation)

        # Add list of other alternatives
        other_products = [p.product_name for p in products[1:4] if p.id != best_product.id]
        if other_products:
            guidelines.alternative_strategies.insert(0, f"Other suitable products: {', '.join(other_products)}")

        # Update feasibility and confidence for alternative recommendation
        guidelines.application_feasibility = "possible_with_caution"
        if guidelines.confidence_level == "high":
            guidelines.confidence_level = "medium"  # Lower confidence for alternatives

        # Add caution flag
        guidelines.data_quality_indicators.caution_flags.append(
            "This is an alternative product recommendation - verify suitability"
        )

        return guidelines

    return None


async def _generate_general_guidance(
        session: AsyncSession,
        crop: Crop,
        target: Optional[Target],
        original_product: Product
) -> Optional[ApplicationGuidelineResponse]:
    """Generate general guidance when no specific product matches are found."""

    # Look for any products that work with this crop (without target specificity)
    general_query = select(distinct(ProductUse.product_id)).where(
        and_(
            ProductUse.crop_id == crop.id,
            ProductUse.is_currently_authorized == True
        )
    ).limit(5)

    result = await session.execute(general_query)
    general_product_ids = result.scalars().all()

    if not general_product_ids:
        return None  # Really no options available

    # Get one representative product for this crop
    product_query = select(Product).where(
        and_(
            Product.id.in_(general_product_ids),
            Product.is_currently_authorized == True
        )
    ).limit(1)

    product_result = await session.execute(product_query)
    representative_product = product_result.scalar_one_or_none()

    if not representative_product:
        return None

    # Create a basic guidance response
    dose_info = DoseInformation(
        min_dose=None,
        max_dose=None,
        unit="Check product label",
        unit_normalized="check_label",
        recommended_dose=None,
        dose_notes=["Specific dosage not available for this combination - consult product label"],
        confidence_level=0.3
    )

    timing_info = ApplicationTiming(
        growth_stage_min=None,
        growth_stage_max=None,
        optimal_season=[],
        application_window="Check product label for timing",
        timing_notes=["Timing information not available for this specific use"],
        bbch_stages=[]
    )

    restrictions = ApplicationRestrictions(
        harvest_interval_days=None,
        max_applications_per_season=None,
        min_interval_between_applications=None,
        weather_restrictions=["Follow general weather precautions"],
        equipment_restrictions=["Use appropriate equipment for product type"],
        regulatory_restrictions=["Verify authorization for your specific use"]
    )

    env_safeguards = EnvironmentalSafeguards(
        aquatic_buffer_zone_m=None,
        arthropod_buffer_zone_m=None,
        plant_buffer_zone_m=None,
        max_buffer_zone_m=None,
        special_conditions=["Check product label for environmental precautions"],
        environmental_risk="unknown"
    )

    field_calc = FieldCalculation(
        total_product_needed=None,
        water_volume_range=None,
        application_rate_per_hectare=None,
        cost_estimate_range=None,
        equipment_settings={}
    )

    data_quality = DataQualityIndicator(
        dose_info_quality="poor",
        timing_info_quality="poor",
        restriction_info_quality="poor",
        overall_reliability=0.3,
        missing_critical_data=["All specific usage data"],
        inferred_values=[],
        data_completeness_score=0.2,
        reliability_category="low",
        trust_factors=[],
        caution_flags=[
            "No specific authorization found for this use",
            "General guidance only - verify before use",
            "Consult local agricultural advisor"
        ]
    )

    # Generate basic recommendations
    target_text = f" for {target.target_name}" if target else ""
    decision_summary = f"No specific authorization found for {original_product.product_name} on {crop.crop_name}{target_text}. Seek alternative products or expert advice."

    safety_alerts = [
        "⚠️ CRITICAL: No specific authorization found for this use",
        "⚠️ Verify product authorization before application"
    ]

    regulatory_compliance = {
        "authorization_status": "not_found",
        "product_registration": original_product.registration_number,
        "usage_authorization": "verify_required",
        "harvest_interval_mandatory": False,
        "max_applications_enforced": False,
        "buffer_zones_required": True,
        "special_conditions": True
    }

    key_success_factors = [
        "Find an authorized alternative product",
        "Consult with local agricultural advisor",
        "Verify current product authorizations"
    ]

    potential_issues = [
        "Product may not be authorized for this specific use",
        "Efficacy not guaranteed without proper authorization",
        "Regulatory compliance risk"
    ]

    alternative_strategies = [
        "Search for products specifically authorized for this crop-target combination",
        "Consider integrated pest management approaches",
        "Consult local agricultural extension service"
    ]

    return ApplicationGuidelineResponse(
        product_name=original_product.product_name,
        crop_name=crop.crop_name,
        target_name=target.target_name if target else None,
        dose_information=dose_info,
        application_timing=timing_info,
        restrictions=restrictions,
        environmental_safeguards=env_safeguards,
        field_calculation=field_calc,
        data_quality_indicators=data_quality,
        weather_considerations=["Follow general weather precautions"],
        equipment_recommendations=["Use appropriate equipment for product formulation"],
        expert_tips=["💡 No specific authorization found - seek expert advice before application"],
        alternative_strategies=alternative_strategies,
        decision_summary=decision_summary,
        safety_alerts=safety_alerts,
        regulatory_compliance=regulatory_compliance,
        application_feasibility="not_recommended",
        confidence_level="low",
        key_success_factors=key_success_factors,
        potential_issues=potential_issues
    )
