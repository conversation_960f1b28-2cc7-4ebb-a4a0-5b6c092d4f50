"""
Simple, practical embedding cache - no over-engineering.
"""
import hashlib
from typing import Dict, List, Optional


class SimpleEmbeddingCache:
    """Dead simple embedding cache - just what you need."""
    
    def __init__(self, max_size: int = 1000):
        self.cache: Dict[str, List[float]] = {}
        self.max_size = max_size
        self.access_order: List[str] = []  # For LRU
    
    def _make_key(self, text: str, model: str) -> str:
        """Generate cache key."""
        return hashlib.md5(f"{model}:{text}".encode()).hexdigest()
    
    def get(self, text: str, model: str) -> Optional[List[float]]:
        """Get cached embedding."""
        key = self._make_key(text, model)
        if key in self.cache:
            # Move to end (most recently used)
            self.access_order.remove(key)
            self.access_order.append(key)
            return self.cache[key]
        return None
    
    def set(self, text: str, model: str, embedding: List[float]) -> None:
        """Cache embedding."""
        key = self._make_key(text, model)
        
        # Evict oldest if at capacity
        if len(self.cache) >= self.max_size and key not in self.cache:
            oldest = self.access_order.pop(0)
            del self.cache[oldest]
        
        # Add/update
        if key in self.cache:
            self.access_order.remove(key)
        
        self.cache[key] = embedding
        self.access_order.append(key)
    
    def clear(self) -> None:
        """Clear cache."""
        self.cache.clear()
        self.access_order.clear()
    
    def stats(self) -> Dict[str, int]:
        """Get simple stats."""
        return {
            "size": len(self.cache),
            "max_size": self.max_size
        }


# Even simpler for development/testing
class NoCache:
    """Null cache - no caching at all."""
    
    def get(self, text: str, model: str) -> Optional[List[float]]:
        return None
    
    def set(self, text: str, model: str, embedding: List[float]) -> None:
        pass
    
    def clear(self) -> None:
        pass
    
    def stats(self) -> Dict[str, int]:
        return {"size": 0, "max_size": 0}