from enum import Enum
from typing import List, Optional, Dict, Any, Union, Literal

from pydantic import Field

from const import EAgent, EChatRole, EAIStyle, EConnectionState, EChatEventType
from schemas import BaseModel


class MIMEType(str, Enum):
    """MIME types for file uploads"""
    # Images
    IMAGE_JPEG = "image/jpeg"
    IMAGE_PNG = "image/png"
    IMAGE_GIF = "image/gif"
    IMAGE_BMP = "image/bmp"
    IMAGE_TIFF = "image/tiff"
    IMAGE_WEBP = "image/webp"
    IMAGE_SVG = "image/svg+xml"

    # Documents
    APPLICATION_PDF = "application/pdf"
    APPLICATION_MSWORD = "application/msword"
    APPLICATION_DOCX = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    APPLICATION_EXCEL = "application/vnd.ms-excel"
    APPLICATION_XLSX = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    APPLICATION_POWERPOINT = "application/vnd.ms-powerpoint"
    APPLICATION_PPTX = "application/vnd.openxmlformats-officedocument.presentationml.presentation"

    # Text
    TEXT_PLAIN = "text/plain"
    TEXT_CSV = "text/csv"
    TEXT_HTML = "text/html"
    TEXT_RTF = "text/rtf"

    # Data
    APPLICATION_JSON = "application/json"
    APPLICATION_XML = "application/xml"

    # Archives
    APPLICATION_ZIP = "application/zip"
    APPLICATION_RAR = "application/x-rar-compressed"

    # Auto-detect
    AUTO = "auto"


class SPersonalContext(BaseModel):
    id: int = None  # user id
    user_info: Optional[Dict[str, Any]] = None
    role: Literal["farmer", "advisor"] = "farmer"
    subscription: Literal["unregistered", "free", "paid"] = "unregistered"
    email: Optional[str] = None


class SChatMessage(BaseModel):
    role: EChatRole
    content: str


class SFileInput(BaseModel):
    """File input for chat requests supporting images and documents from MinIO."""
    id: Optional[str] = Field(None, description="Optional file ID")
    object_name: str = Field(..., description="Object name of the file in MinIO storage")
    bucket_name: Optional[str] = Field(None, description="Optional bucket name (uses default if None)")
    mime_type: Optional[MIMEType] = Field(MIMEType.AUTO,
                                          description="MIME type of the file - auto-detects if not specified")


class SChatRequest(BaseModel):
    id: Optional[str] = Field(default=None)  # thread id
    agent: EAgent = EAgent.general
    style: EAIStyle = EAIStyle.deep
    web: bool = True
    personal_context: Optional[SPersonalContext] = None
    message_chain: Optional[List[SChatMessage]] = Field(default=[])
    query: str
    attachments: Optional[List[SFileInput]] = Field(default=[],
                                                    description="Optional files to include in the conversation")
    # Simple cache for preventing identical searches within same request
    search_cache: Dict[str, Dict[str, Any]] = Field(default_factory=dict, exclude=True)


class SConnectionContent(BaseModel):
    state: EConnectionState
    message: Optional[str] = None
    code: str = None
    meta: Optional[Dict[str, Any]] = None


class SConversationContent(BaseModel):
    id: Optional[str] = None
    name: Optional[str] = None
    agent: Optional[EAgent] = None
    user_query: Optional[str] = None


class SMetricContent(BaseModel):
    label: str
    value: Union[int, float, str]
    unit: Optional[str] = None


class STableContent(BaseModel):
    headers: List[str]
    rows: List[List[Any]]


class SChartDataset(BaseModel):
    label: str
    data: List[Union[int, float, None]]
    backgroundColor: Optional[Union[str, List[str]]] = None
    borderColor: Optional[str] = None


class SChartData(BaseModel):
    labels: List[str]
    datasets: List[SChartDataset]


class SChartContent(BaseModel):
    chartType: Literal["line", "bar", "pie", "radar", "doughnut"]
    title: str
    data: SChartData
    xAxisLabel: Optional[str] = None
    yAxisLabel: Optional[str] = None


class SChatEvent(BaseModel):
    type: EChatEventType
    content: Union[
        str, SConnectionContent, SConversationContent, SChartContent, SMetricContent, STableContent
    ]
