from enum import Enum
from typing import Optional, Union, List, Dict, Any

import aiohttp

from modules.web.web_searcher.abc_searcher import AbstractSearcher, SafeSearchLevel, ResultType, MeasurementUnit, \
    FreshnessOption


class BraveWebSearcher(AbstractSearcher):
    """
    Implementation of AbstractSearcher for Brave Search API.
    """

    class ResultFilterOptions(str, Enum):
        DISCUSSIONS = "discussions"
        FAQ = "faq"
        INFOBOX = "infobox"
        NEWS = "news"
        QUERY = "query"
        SUMMARIZER = "summarizer"
        VIDEOS = "videos"
        WEB = "web"
        LOCATIONS = "locations"

    def __init__(self, api_key: Optional[str] = None):
        self.endpoint = "https://api.search.brave.com/res/v1/web/search"
        super().__init__(api_key)

    async def search(
            self,
            query: str,
            count: Optional[int] = 10,
            offset: Optional[int] = 0,
            safe_search: Optional[SafeSearchLevel] = SafeSearchLevel.MODERATE,
            language: Optional[str] = "en",
            country: Optional[str] = "US",
            freshness: Optional[Union[FreshnessOption, str]] = None,
            timeout: Optional[float] = 10.0,
            ui_language: Optional[str] = None,
            text_decorations: Optional[bool] = True,
            spellcheck: Optional[bool] = True,
            result_filter: Optional[Union[ResultFilterOptions, str, List[Union[ResultFilterOptions, str]]]] = None,
            goggles: Optional[List[str]] = None,
            units: Optional[MeasurementUnit] = None,
            extra_snippets: Optional[bool] = False,
            summary: Optional[bool] = False
    ) -> List[Dict[str, Any]]:
        """
        Perform a web search using Brave Search API.

        Args:
            query: The search query term
            count: Number of search results to return (max 20)
            offset: Number of results to skip for pagination (max 9)
            safe_search: Filter for adult content (off, moderate, strict)
            language: Language for results (maps to search_lang)
            country: Country code for localized results
            freshness: Filter results by recency
            timeout: Request timeout in seconds
            ui_language: User interface language for response
            text_decorations: Whether to include decoration markers
            spellcheck: Whether to check spelling in query
            result_filter: Comma-delimited types to include
            goggles: List of goggles for custom re-ranking
            units: Measurement units (metric or imperial)
            extra_snippets: Whether to include extra snippets
            summary: Enable summary generation in results

        Returns:
            List of search result dictionaries
        """
        if not self.api_key:
            raise ValueError("Brave API key is required.")

        if not query:
            raise ValueError("Search query cannot be empty.")

        # Build query parameters
        params: Dict[str, Any] = {"q": query}

        # Map common parameters to Brave-specific parameters
        params["country"] = country
        params["search_lang"] = language
        params["count"] = min(count, 20)  # Brave max is 20
        params["offset"] = min(offset, 9)  # Brave max is 9

        # Handle enums properly
        if isinstance(safe_search, SafeSearchLevel):
            params["safesearch"] = safe_search.value
        else:
            params["safesearch"] = safe_search.lower() if safe_search else SafeSearchLevel.MODERATE.value

        # Add other parameters if provided
        if freshness is not None:
            params["freshness"] = freshness.value if isinstance(freshness, FreshnessOption) else freshness

        if ui_language is not None:
            params["ui_lang"] = ui_language

        if text_decorations is not None:
            params["text_decorations"] = 1 if text_decorations else 0

        if spellcheck is not None:
            params["spellcheck"] = 1 if spellcheck else 0

        if result_filter is not None:
            if isinstance(result_filter, list):
                filter_values = [f.value if isinstance(f, Enum) else f for f in result_filter]
                params["result_filter"] = ",".join(filter_values)
            else:
                params["result_filter"] = result_filter.value if isinstance(result_filter, Enum) else result_filter

        if units is not None:
            params["units"] = units.value if isinstance(units, MeasurementUnit) else units

        if extra_snippets is not None:
            params["extra_snippets"] = 1 if extra_snippets else 0

        if summary is not None:
            params["summary"] = 1 if summary else 0

        # Handle goggles (list parameter that needs special handling)
        if goggles:
            # Create a new list to hold the parameters in the format aiohttp expects
            for goggle in goggles:
                if "goggles" not in params:
                    params["goggles"] = []
                params["goggles"].append(goggle)

        headers = {
            "X-Subscription-Token": self.api_key,
            "Accept": "application/json",
            "Accept-Encoding": "gzip"
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                        self.endpoint,
                        params=params,
                        headers=headers,
                        timeout=timeout
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"Brave search failed: {response.status}, {error_text}")

                    result = await response.json()
                    return self._process_response(result)
        except Exception as e:
            raise Exception(f"Error during Brave search: {str(e)}")

    def _process_response(self, response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Process the Brave API response into a standardized format.

        Args:
            response: The raw API response

        Returns:
            Processed list of search results
        """
        results: List[Dict[str, Any]] = []

        # Process web results
        if "web" in response and "results" in response["web"]:
            for page in response["web"]["results"]:
                results.append({
                    "type": ResultType.WEBPAGE,
                    "title": page.get("title", ""),
                    "snippet": page.get("description", ""),
                    "url": page.get("url", ""),
                    "display_url": page.get("url", ""),
                    "date_published": page.get("age", "")
                })

        # Process news results
        if "news" in response and "results" in response["news"]:
            for item in response["news"]["results"]:
                results.append({
                    "type": ResultType.NEWS,
                    "title": item.get("title", ""),
                    "description": item.get("description", ""),
                    "url": item.get("url", ""),
                    "publisher": item.get("source", ""),
                    "date_published": item.get("age", "")
                })

        # Process video results
        if "videos" in response and "results" in response["videos"]:
            for video in response["videos"]["results"]:
                results.append({
                    "type": ResultType.VIDEO,
                    "title": video.get("title", ""),
                    "description": video.get("description", ""),
                    "url": video.get("url", ""),
                    "thumbnail": video.get("thumbnail", {}).get("src", "") if video.get("thumbnail") else "",
                    "publisher": video.get("source", ""),
                    "duration": video.get("duration", "")
                })

        # Process discussion results
        if "discussions" in response and "results" in response["discussions"]:
            for discussion in response["discussions"]["results"]:
                results.append({
                    "type": ResultType.DISCUSSION,
                    "title": discussion.get("title", ""),
                    "snippet": discussion.get("description", ""),
                    "url": discussion.get("url", ""),
                    "source": discussion.get("source", ""),
                    "date_published": discussion.get("age", "")
                })

        # Process location results
        if "locations" in response and "results" in response["locations"]:
            for location in response["locations"]["results"]:
                results.append({
                    "type": ResultType.LOCATION,
                    "title": location.get("title", ""),
                    "id": location.get("id", ""),
                    "address": location.get("address", ""),
                    "phone": location.get("phone", ""),
                    "url": location.get("website", "")
                })

        # Process infobox if present
        if "infobox" in response and "content" in response["infobox"]:
            content = response["infobox"]["content"]
            infobox_result = {
                "type": ResultType.INFOBOX,
                "title": content.get("title", ""),
                "description": content.get("description", ""),
                "url": content.get("url", "")
            }

            # Add image if available
            if "image" in content and "url" in content["image"]:
                infobox_result["image"] = content["image"]["url"]

            results.append(infobox_result)

        return results
