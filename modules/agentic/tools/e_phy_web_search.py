from typing import Dict, Any

from modules.web.web_searcher.tavily_searcher import <PERSON><PERSON><PERSON>eb<PERSON><PERSON>cher
from utils.logger import logger


async def search_e_phy_data(query: str, max_results: int = 5) -> Dict[str, Any]:
    """
    Search for information from the E-Phy catalogue of plant protection products, fertilizing materials 
    and growing media, adjuvants, mixed products and mixtures using official ANSES sources.
    
    This tool retrieves real-time data from the official E-Phy catalogue maintained by ANSES (French Agency 
    for Food, Environmental and Occupational Health & Safety). The E-Phy catalogue contains comprehensive 
    information about:
    - Authorized plant protection products and their regulatory status
    - Active substances, their properties, toxicology, and environmental impact  
    - Product authorization status (AMM), withdrawal dates, usage restrictions
    - Approved uses, application conditions, and safety measures
    - Regulatory updates and compliance information
    - Crop protection solutions for specific pests, diseases, and crops
    
    The search focuses specifically on official ANSES sources to ensure data accuracy and regulatory compliance.
    This is particularly useful for obtaining up-to-date information about product authorizations, 
    regulatory changes, and safety guidelines that may not be reflected in cached databases.
    
    Use this tool for any E-Phy related queries including: product names (e.g. "Roundup authorization"), 
    active substances (e.g. "glyphosate toxicology"), regulatory status, crop protection advice, etc.
    
    Args:
        query: Search query related to plant protection products, active substances, crops, pests, 
               diseases, authorization status, or regulatory information
        max_results: Maximum number of search results to return (default: 5, max: 10)
    
    Returns:
        Dict containing search results from official E-Phy sources with citations
    """
    logger.debug(f"Tool: search_e_phy_data called with query: {query}")

    try:
        # Validate inputs
        if not query or not query.strip():
            return {
                "error": "Search query cannot be empty",
                "error_type": "validation_error",
                "message": "Please provide a search query related to plant protection products or E-Phy catalogue."
            }

        query = query.strip()
        max_results = min(max(1, max_results), 10)  # Ensure between 1-10

        # Initialize Tavily searcher
        searcher = TavilyWebSearcher()

        # Perform search with E-Phy specific domains
        search_results = await searcher.search(
            query=query,
            count=max_results,
            include_domains=["http://www.anses.fr", "https://ephy.anses.fr", "https://food.ec.europa.eu/"],
            search_depth="advanced",
            include_answer=True,
            include_raw_content=True,
            timeout=30.0
        )

        # Process and format results
        formatted_results = []
        for result in search_results:
            if result.get("type") == "infobox" and result.get("title") == "Tavily Answer":
                # This is the AI-generated answer, keep it at the top
                formatted_results.insert(0, {
                    "type": "summary",
                    "title": "E-Phy Information Summary",
                    "content": result.get("description", ""),
                    "source": "AI-generated summary from official sources"
                })
            else:
                # Regular search results
                formatted_result = {
                    "type": "search_result",
                    "title": result.get("title", ""),
                    "url": result.get("url", ""),
                    "snippet": result.get("snippet", ""),
                    "score": result.get("score", 0.0)
                }

                # Add raw content if available for more detailed information
                if "raw_content" in result:
                    formatted_result["raw_content"] = result["raw_content"]

                # Add publication date if available
                if "date_published" in result:
                    formatted_result["date_published"] = result["date_published"]

                formatted_results.append(formatted_result)

        return {
            "query": query,
            "total_results": len(formatted_results),
            "results": formatted_results,
            "_citation": {
                "source": "E-Phy Catalogue - ANSES",
                "official_urls": ["https://ephy.anses.fr/", "http://www.anses.fr/"],
                "description": "Official French database of authorized plant protection products",
                "search_engine": "Tavily",
                "search_timestamp": "real-time"
            }
        }

    except Exception as e:
        logger.error(f"Error in search_e_phy_data: {str(e)}")
        return {
            "error": "Failed to search E-Phy catalogue",
            "error_type": "search_error",
            "message": f"An error occurred while searching: {str(e)}",
            "suggestion": "Please try again with a different query or check your internet connection."
        }
