# Application Settings
PROJECT_NAME=Ekumen AI
VERSION=1.0.0
API_V1_STR=/api/v1
ENV=dev

# Database Settings (Optional - if needed)
#DB_HOST=db_host
#DB_USER=db_user
#DB_PASS=db_password
#DB_PORT=5432
#DATABASE=ekumen
#CHARSET=utf8mb4

# MinIO Settings (Optional - if needed)
#MINIO_ENDPOINT=minio:9000
#MINIO_DEFAULT_BUCKET=notes
#MINIO_ACCESS_KEY_ID=minioadmin
#MINIO_SECRET_ACCESS_KEY=minioadmin
#MINIO_SSLMODE=False

# API Keys (Required)
MISTRAL_API_KEY=your_mistral_api_key
OPENAI_API_KEY=your_openai_api_key

# Other API Keys (Optional)
WEATHER_API_KEY=your_weather_api_key

# Logging (Optional)
#LOGFIRE_TOKEN=your_logfire_token