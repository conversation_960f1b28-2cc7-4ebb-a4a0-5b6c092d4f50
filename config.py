from functools import lru_cache
from typing import Optional

from pydantic_settings import BaseSettings, SettingsConfigDict


class Config(BaseSettings):
    model_config = SettingsConfigDict(
        case_sensitive=True,
        env_file=".env",
        env_file_encoding="utf-8",
        extra="ignore"
    )

    PROJECT_NAME: str = "Ekumen AI"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    ENV: str = "dev"
    PYTHONUNBUFFERED: int = 1

    BACKEND_HOST: str = "https://rock-ha4t.onrender.com"

    DB_HOST: Optional[str] = None
    DB_USER: Optional[str] = None
    DB_PASS: Optional[str] = None
    DB_PORT: Optional[int] = None
    DATABASE: Optional[str] = None
    CHARSET: Optional[str] = "utf8mb4"
    # DB_URI: Optional[str] = None

    MINIO_ENDPOINT: Optional[str] = None
    MINIO_ACCESS_KEY_ID: Optional[str] = None
    MINIO_SECRET_ACCESS_KEY: Optional[str] = None
    MINIO_SSLMODE: Optional[str] = True

    MINIO_EPHY_BUCKET: Optional[str] = 'e-phy'

    MISTRAL_API_KEY: Optional[str] = None
    OPENAI_API_KEY: Optional[str] = None

    WEATHER_API_KEY: Optional[str] = None

    LOGFIRE_TOKEN: Optional[str] = None


@lru_cache
def get_config():
    return Config()


# Initialize the CONFIG instance at the global level
CONFIG = get_config()
