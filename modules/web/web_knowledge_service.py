import hashlib
import uuid
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional
from difflib import SequenceMatcher
from urllib.parse import urlparse

from sqlalchemy import select
from models.dbm import DatabaseManager
from models.web_knowledge import WebKnowledge
from utils.logger import logger


class WebKnowledgeService:
    """Service for collecting high-quality web data for model fine-tuning"""
    
    def __init__(self, min_score_threshold: float = 0.5, similarity_threshold: float = 0.8):
        self.db_manager = DatabaseManager()
        self.min_score_threshold = min_score_threshold
        self.similarity_threshold = similarity_threshold
    
    def _calculate_content_hash(self, results: List[Dict[str, Any]]) -> str:
        """Create content fingerprint for deduplication"""
        content_parts = []
        for result in results:
            title = result.get('title', '').lower().strip()
            snippet = result.get('snippet', '').lower().strip()
            content_parts.append(f"{title} {snippet}")
        
        combined_content = " ".join(content_parts)
        return hashlib.md5(combined_content.encode()).hexdigest()
    
    def _normalize_content(self, text: str) -> str:
        """Normalize text for similarity comparison"""
        import re
        text = text.lower().strip()
        # Remove common French stop words and normalize
        text = re.sub(r'\b(le|la|les|de|du|des|et|ou|dans|pour|avec|sur|en|un|une)\b', '', text)
        text = re.sub(r'[^\w\s]', '', text)
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    
    def _is_content_similar(self, content1: str, content2: str) -> bool:
        """Check if two content strings are similar"""
        norm1 = self._normalize_content(content1)
        norm2 = self._normalize_content(content2)
        similarity = SequenceMatcher(None, norm1, norm2).ratio()
        return similarity > self.similarity_threshold
    
    def _is_domain_duplicate(self, url1: str, url2: str) -> bool:
        """Check if URLs are likely duplicates from same domain"""
        try:
            parsed1 = urlparse(url1)
            parsed2 = urlparse(url2)
            
            if parsed1.netloc == parsed2.netloc:
                # Same domain, check path similarity
                path_similarity = SequenceMatcher(None, parsed1.path, parsed2.path).ratio()
                return path_similarity > 0.7
            
            return False
        except:
            return False
    
    def _deduplicate_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate and low-quality results"""
        
        # 1. Filter by score first
        high_quality = [
            result for result in results 
            if result.get('score', 0) >= self.min_score_threshold
        ]
        
        if len(high_quality) < 2:
            logger.info("Insufficient high-quality results for training")
            return []
        
        # 2. Deduplicate by URL and content
        unique_results = []
        seen_urls = set()
        
        for result in high_quality:
            url = result.get('url', '')
            
            # Skip if URL already seen
            if url in seen_urls:
                continue
            
            # Check for domain duplicates
            is_domain_dup = any(
                self._is_domain_duplicate(url, seen_url) 
                for seen_url in seen_urls
            )
            if is_domain_dup:
                continue
            
            # Check for content similarity
            current_content = f"{result.get('title', '')} {result.get('snippet', '')}"
            is_content_dup = any(
                self._is_content_similar(
                    current_content, 
                    f"{existing.get('title', '')} {existing.get('snippet', '')}"
                )
                for existing in unique_results
            )
            
            if not is_content_dup:
                seen_urls.add(url)
                unique_results.append(result)
        
        logger.info(f"Deduplicated: {len(results)} → {len(unique_results)} results")
        return unique_results
    
    async def store_web_training_data(
        self, 
        message_id: str, 
        query: str, 
        raw_results: List[Dict[str, Any]]
    ) -> Optional[str]:
        """
        Store high-quality, deduplicated web results for fine-tuning
        
        Args:
            message_id: ID linking to conversation system
            query: Web search query
            raw_results: Raw web search results from Tavily
            
        Returns:
            Stored record ID if successful, None otherwise
        """
        try:
            # Filter and deduplicate results
            filtered_results = self._deduplicate_results(raw_results)
            
            if len(filtered_results) < 2:
                logger.info(f"Skipping storage - insufficient quality results for query: {query}")
                return None
            
            # Calculate content hash for deduplication
            content_hash = self._calculate_content_hash(filtered_results)
            
            async with self.db_manager.session_scope() as session:
                # Check if similar content already exists
                existing = await session.execute(
                    select(WebKnowledge)
                    .where(WebKnowledge.content_hash == content_hash)
                )
                
                if existing.scalars().first():
                    logger.info(f"Similar training data already exists for content hash: {content_hash}")
                    return None
                
                # Store the training data
                training_record = WebKnowledge(
                    message_id=message_id,
                    query=query,
                    results=filtered_results,
                    content_hash=content_hash,
                    created_at=datetime.now(timezone.utc)
                )
                
                session.add(training_record)
                await session.commit()
                
                logger.info(f"Stored training data with {len(filtered_results)} results for message: {message_id}")
                return training_record.id
                
        except Exception as e:
            logger.error(f"Error storing web training data: {str(e)}")
            return None
