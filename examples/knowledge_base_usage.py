"""
Example usage of the Knowledge Base RAG system.
Demonstrates document processing, chunking, embedding, and retrieval.
"""
import asyncio
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../')))

from modules.knowledge_base import KnowledgeBaseService
from models.knowledge_base import AccessLevel
from modules.external.minio_adapter import MinIOAdapter
from utils.logger import logger


async def upload_sample_documents():
    """Upload sample agricultural documents to MinIO for testing."""
    minio_adapter = MinIOAdapter()
    
    # Sample agricultural text content
    sample_documents = {
        "wheat_pest_guide.txt": """
        Wheat Pest Management Guide
        
        Common Wheat Pests and Their Control
        
        1. Aphids
        Aphids are small insects that feed on wheat sap. They can cause significant damage during the growing season.
        Control methods:
        - Apply insecticide when threshold is reached (5-10 aphids per plant)
        - Use beneficial insects like ladybugs for biological control
        - Monitor regularly during spring and early summer
        
        2. Wheat Stem Rust
        A fungal disease that appears as reddish-brown pustules on stems and leaves.
        Prevention and control:
        - Plant resistant wheat varieties
        - Apply fungicide preventively in humid conditions
        - Remove volunteer wheat plants that can harbor the disease
        
        3. Hessian Fly
        Small flies that lay eggs on wheat leaves. Larvae feed inside the stem.
        Management strategies:
        - Plant after the "fly-free" date in your region
        - Use resistant wheat varieties when available
        - Destroy crop residue after harvest
        """,
        
        "corn_fertilizer_guide.txt": """
        Corn Fertilization Guidelines
        
        Nitrogen Requirements
        Corn requires significant nitrogen throughout its growing season.
        
        Application timing:
        - Pre-plant: Apply 30-50% of total nitrogen before planting
        - Side-dress: Apply remaining nitrogen at V6-V8 growth stage
        - Late season: Monitor plant color and apply additional if needed
        
        Phosphorus and Potassium
        Essential for root development and overall plant health.
        - Soil test recommended to determine application rates
        - Apply based on soil fertility levels and yield goals
        - Broadcast and incorporate before planting
        
        Micronutrients
        Zinc and boron are often deficient in corn production.
        - Apply zinc at 1-2 lbs/acre if soil levels are low
        - Boron application may be needed on sandy soils
        """,
        
        "organic_farming_practices.txt": """
        Sustainable Organic Farming Practices
        
        Soil Health Management
        Building healthy soil is the foundation of organic farming.
        
        Composting
        - Create compost from farm waste and organic materials
        - Turn compost pile every 2-3 weeks for proper decomposition
        - Apply finished compost at 2-4 tons per acre annually
        
        Cover Crops
        Cover crops improve soil structure and add organic matter.
        - Plant legume cover crops to add nitrogen naturally
        - Use grasses like rye to prevent soil erosion
        - Terminate cover crops 2-3 weeks before planting main crop
        
        Crop Rotation
        Rotating crops breaks pest and disease cycles.
        - Follow nitrogen-fixing legumes with heavy feeders like corn
        - Include diverse crop families in rotation
        - Plan 3-4 year rotation cycles for maximum benefit
        """
    }
    
    uploaded_files = []
    
    for filename, content in sample_documents.items():
        try:
            # Upload to MinIO
            success = await minio_adapter.upload_file(
                file_data=content.encode('utf-8'),
                object_name=f"knowledge-base/samples/{filename}",
                content_type="text/plain",
                metadata={"source": "example_usage", "type": "agricultural_guide"}
            )
            
            if success:
                uploaded_files.append(f"knowledge-base/samples/{filename}")
                logger.info(f"Uploaded sample document: {filename}")
            else:
                logger.error(f"Failed to upload: {filename}")
                
        except Exception as e:
            logger.error(f"Error uploading {filename}: {e}")
    
    return uploaded_files


async def process_documents_example():
    """Example of processing documents through the knowledge base."""
    logger.info("=== Knowledge Base RAG System Demo ===")
    
    # Initialize service
    kb_service = KnowledgeBaseService()
    
    # Upload sample documents
    logger.info("1. Uploading sample agricultural documents...")
    file_paths = await upload_sample_documents()
    
    if not file_paths:
        logger.error("No documents uploaded successfully")
        return
    
    # Process documents
    logger.info("2. Processing documents through RAG pipeline...")
    results = await kb_service.batch_process_documents(
        file_paths=file_paths,
        user_id="demo_user",
        enterprise_id="demo_enterprise",
        access_level=AccessLevel.PRIVATE,
        batch_size=2
    )
    
    # Display processing results
    logger.info("3. Processing Results:")
    for file_path, result in results.items():
        if result['success']:
            logger.info(f"✓ {file_path} -> Document ID: {result['document_id']}")
        else:
            logger.error(f"✗ {file_path} -> Error: {result['error']}")
    
    # Wait a moment for processing to complete
    await asyncio.sleep(2)
    
    return results


async def search_examples(kb_service: KnowledgeBaseService):
    """Example search queries on the processed documents."""
    logger.info("4. Running example search queries...")
    
    # Example queries for agricultural content
    example_queries = [
        "How to control aphids in wheat?",
        "When should I apply nitrogen fertilizer to corn?",
        "What are organic farming practices for soil health?",
        "How to manage wheat stem rust disease?",
        "Cover crop benefits and management"
    ]
    
    for i, query in enumerate(example_queries, 1):
        logger.info(f"\n--- Search Example {i}: '{query}' ---")
        
        try:
            search_results = await kb_service.search_documents(
                query=query,
                user_id="demo_user",
                enterprise_id="demo_enterprise",
                access_levels=[AccessLevel.PRIVATE, AccessLevel.PUBLIC],
                limit=3,
                include_chunks=True
            )
            
            logger.info(f"Found {search_results['total_results']} results in {search_results['retrieval_time_ms']:.1f}ms")
            
            for j, result in enumerate(search_results['results'][:2], 1):  # Show top 2 results
                logger.info(f"\nResult {j}:")
                logger.info(f"  Document: {result['document_title']}")
                logger.info(f"  Relevance: {result['relevance_score']:.3f}")
                logger.info(f"  Page: {result.get('page_number', 'N/A')}")
                logger.info(f"  Context: {result.get('context_summary', 'No context')}")
                logger.info(f"  Content Preview: {result['chunk_preview'][:100]}...")
            
        except Exception as e:
            logger.error(f"Search failed: {e}")


async def chunk_analysis_example(kb_service: KnowledgeBaseService, document_id: str):
    """Example of analyzing document chunks."""
    logger.info(f"\n5. Analyzing chunks for document {document_id}...")
    
    try:
        chunks = await kb_service.get_document_chunks(
            document_id=document_id,
            user_id="demo_user",
            enterprise_id="demo_enterprise"
        )
        
        if chunks:
            logger.info(f"Document has {len(chunks)} chunks:")
            
            for chunk in chunks[:3]:  # Show first 3 chunks
                logger.info(f"\nChunk {chunk['chunk_index']}:")
                logger.info(f"  Type: {chunk['chunk_type']}")
                logger.info(f"  Page: {chunk['page_number'] or 'N/A'}")
                logger.info(f"  Importance: {chunk['importance_score']:.2f}")
                logger.info(f"  Topics: {chunk.get('detected_topics', [])}")
                logger.info(f"  Context: {chunk['context_summary']}")
                logger.info(f"  Content: {chunk['content_preview']}")
        else:
            logger.warning("No chunks found or access denied")
            
    except Exception as e:
        logger.error(f"Chunk analysis failed: {e}")


async def main():
    """Main demo function."""
    try:
        # Process documents
        processing_results = await process_documents_example()
        
        if not any(r['success'] for r in processing_results.values()):
            logger.error("No documents processed successfully. Cannot proceed with search examples.")
            return
        
        # Initialize service for searches
        kb_service = KnowledgeBaseService()
        
        # Run search examples
        await search_examples(kb_service)
        
        # Analyze chunks for first successful document
        successful_doc = next(
            (result['document_id'] for result in processing_results.values() if result['success']),
            None
        )
        
        if successful_doc:
            await chunk_analysis_example(kb_service, successful_doc)
        
        logger.info("\n=== Demo completed successfully! ===")
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")


if __name__ == "__main__":
    # Run the demo
    asyncio.run(main())