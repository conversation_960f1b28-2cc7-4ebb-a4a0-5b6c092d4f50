"""
Product Details & Safety Information Tool for E-Phy Agricultural Database.
Provides comprehensive product information with safety and regulatory details.
"""
from dataclasses import dataclass
from typing import List, Optional

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from models.e_phy import (
    Product, ProductSubstance
)
from .types import HazardImportance, ConditionType


@dataclass
class BasicProductInfo:
    """Basic product information."""
    product_id: int
    product_name: str
    registration_number: str
    authorization_status: str
    is_currently_authorized: bool
    function_category: Optional[str]
    formulation_type: Optional[str]
    holder: str
    product_type: Optional[str]
    first_authorization_date: Optional[str]
    withdrawal_date: Optional[str]


@dataclass
class CompositionDetail:
    """Product composition component."""
    component_name: str
    min_value: Optional[float]
    max_value: Optional[float]
    unit: Optional[str]
    component_category: Optional[str]
    is_active_substance: bool
    concentration_range: Optional[str]


@dataclass
class SafetyHazard:
    """Safety hazard information."""
    hazard_code: str
    hazard_description: str
    hazard_category: Optional[str]
    severity_level: int
    requires_ppe: bool
    safety_priority: HazardImportance


@dataclass
class UsageRestriction:
    """Usage condition or restriction."""
    condition_category: str
    condition_description: str
    condition_type: ConditionType
    importance_level: HazardImportance
    compliance_required: bool


@dataclass
class ActiveSubstanceInfo:
    """Active substance details."""
    substance_name: str
    cas_number: Optional[str]
    concentration: Optional[float]
    concentration_unit: Optional[str]
    authorization_status: Optional[str]
    is_primary: bool
    variants: List[str]


@dataclass
class DataCompletenessScore:
    """Data completeness assessment."""
    basic_info: float
    composition: float
    hazards: float
    conditions: float
    substances: float
    overall_score: float
    missing_elements: List[str]


@dataclass
class ProductDetailResponse:
    """Complete product detail response."""
    basic_info: BasicProductInfo
    active_substances: List[ActiveSubstanceInfo]
    composition: List[CompositionDetail]
    safety_hazards: List[SafetyHazard]
    usage_conditions: List[UsageRestriction]
    regulatory_notes: List[str]
    safety_summary: str
    data_completeness: DataCompletenessScore
    alternative_names: List[str]
    related_products: List[str]
    expert_recommendations: List[str]


async def get_product_comprehensive_info(
        session: AsyncSession,
        product_identifier: str,
        include_composition: bool = True,
        include_hazards: bool = True,
        include_conditions: bool = True,
) -> Optional[ProductDetailResponse]:
    """
    Complete product information with safety and regulatory details.
    
    Args:
        product_identifier: Product ID, name, or registration number
        include_composition: Include chemical composition details
        include_hazards: Include safety hazard information
        include_conditions: Include usage conditions and restrictions
        language_preference: Output language preference
    
    Returns:
        ProductDetailResponse with comprehensive information or None if not found
    """

    # Find the product
    product = await _find_product(session, product_identifier)
    if not product:
        return None

    # Load product with all related data
    product_query = select(Product).options(
        selectinload(Product.substances).selectinload(ProductSubstance.substance),
        selectinload(Product.compositions) if include_composition else None,
        selectinload(Product.hazards) if include_hazards else None,
        selectinload(Product.conditions) if include_conditions else None
    ).where(Product.id == product.id)

    result = await session.execute(product_query)
    product = result.scalar_one()

    # Build basic info
    basic_info = BasicProductInfo(
        product_id=product.id,
        product_name=product.product_name,
        registration_number=product.registration_number,
        authorization_status=product.authorization_status or "Unknown",
        is_currently_authorized=product.is_currently_authorized or False,
        function_category=product.function_category,
        formulation_type=product.formulation_type,
        holder=product.holder or "Unknown",
        product_type=product.product_type,
        first_authorization_date=product.first_authorization_date.isoformat() if product.first_authorization_date else None,
        withdrawal_date=product.withdrawal_date.isoformat() if product.withdrawal_date else None
    )

    # Process active substances
    active_substances = []
    if product.substances:
        for ps in product.substances:
            if ps.substance:
                variants = []
                if ps.substance.variants:
                    variants = [v.strip() for v in ps.substance.variants.split('|') if v.strip()]

                substance_info = ActiveSubstanceInfo(
                    substance_name=ps.substance.name,
                    cas_number=ps.substance.cas_number,
                    concentration=ps.concentration,
                    concentration_unit=ps.concentration_unit,
                    authorization_status=ps.substance.authorization_status,
                    is_primary=ps.primary_substance or False,
                    variants=variants
                )
                active_substances.append(substance_info)

    # Process composition
    composition = []
    if include_composition and product.compositions:
        for comp in product.compositions:
            # Check if this component is an active substance
            is_active = any(
                comp.component_name.lower() in substance.substance_name.lower()
                for substance in product.substances
                if substance.substance
            )

            # Format concentration range
            conc_range = None
            if comp.min_value is not None and comp.max_value is not None:
                if comp.min_value == comp.max_value:
                    conc_range = f"{comp.min_value}{comp.unit or ''}"
                else:
                    conc_range = f"{comp.min_value}-{comp.max_value}{comp.unit or ''}"

            composition_detail = CompositionDetail(
                component_name=comp.component_name,
                min_value=comp.min_value,
                max_value=comp.max_value,
                unit=comp.unit,
                component_category=comp.component_category,
                is_active_substance=is_active,
                concentration_range=conc_range
            )
            composition.append(composition_detail)

    # Process safety hazards
    safety_hazards = []
    if include_hazards and product.hazards:
        for hazard in product.hazards:
            # Determine safety priority
            priority = _determine_hazard_priority(hazard.hazard_severity or 1)

            safety_hazard = SafetyHazard(
                hazard_code=hazard.hazard_code or "Unknown",
                hazard_description=hazard.hazard_description or "No description available",
                hazard_category=hazard.hazard_category,
                severity_level=hazard.hazard_severity or 1,
                requires_ppe=hazard.requires_special_equipment or False,
                safety_priority=priority
            )
            safety_hazards.append(safety_hazard)

        # Sort by severity (highest first)
        safety_hazards.sort(key=lambda x: x.severity_level, reverse=True)

    # Process usage conditions
    usage_conditions = []
    if include_conditions and product.conditions:
        for condition in product.conditions:
            # Determine condition type and importance
            cond_type = _categorize_condition_type(condition.condition_category or "")
            importance = _map_condition_importance(condition.condition_importance or "standard")

            usage_restriction = UsageRestriction(
                condition_category=condition.condition_category or "General",
                condition_description=condition.condition_description or "No description",
                condition_type=cond_type,
                importance_level=importance,
                compliance_required=importance in ["high", "critical"]
            )
            usage_conditions.append(usage_restriction)

        # Sort by importance
        importance_order = {"critical": 4, "high": 3, "standard": 2, "low": 1}
        usage_conditions.sort(
            key=lambda x: importance_order.get(x.importance_level, 0),
            reverse=True
        )

    # Generate derived information
    regulatory_notes = _generate_regulatory_notes(basic_info, usage_conditions)
    safety_summary = _generate_safety_summary(safety_hazards, usage_conditions)
    data_completeness = _assess_data_completeness(
        basic_info, active_substances, composition, safety_hazards, usage_conditions
    )

    # Get alternative names
    alternative_names = []
    if product.alternative_names:
        alternative_names = [name.strip() for name in product.alternative_names.split('|') if name.strip()]

    # Generate expert recommendations
    expert_recommendations = _generate_expert_recommendations(
        basic_info, safety_hazards, usage_conditions, data_completeness
    )

    # Find related products (simplified - could be enhanced)
    related_products = []
    if product.reference_product_name:
        related_products.append(product.reference_product_name)

    return ProductDetailResponse(
        basic_info=basic_info,
        active_substances=active_substances,
        composition=composition,
        safety_hazards=safety_hazards,
        usage_conditions=usage_conditions,
        regulatory_notes=regulatory_notes,
        safety_summary=safety_summary,
        data_completeness=data_completeness,
        alternative_names=alternative_names,
        related_products=related_products,
        expert_recommendations=expert_recommendations
    )


async def _find_product(session: AsyncSession, identifier: str) -> Optional[Product]:
    """Find product by ID, name, or registration number."""

    # Try as ID first
    try:
        product_id = int(identifier)
        query = select(Product).where(Product.id == product_id)
        result = await session.execute(query)
        product = result.scalar_one_or_none()
        if product:
            return product
    except ValueError:
        pass

    # Try as registration number
    query = select(Product).where(Product.registration_number == identifier)
    result = await session.execute(query)
    product = result.scalar_one_or_none()
    if product:
        return product

    # Try as product name (exact match)
    query = select(Product).where(Product.product_name.ilike(identifier))
    result = await session.execute(query)
    product = result.scalar_one_or_none()
    if product:
        return product

    # Try partial name match
    query = select(Product).where(Product.product_name.ilike(f"%{identifier}%"))
    result = await session.execute(query)
    products = result.scalars().all()

    if products:
        # Return first match (could be improved with similarity scoring)
        return products[0]

    return None


def _determine_hazard_priority(severity: int) -> HazardImportance:
    """Determine hazard priority based on severity level."""
    if severity >= 4:
        return "critical"
    elif severity >= 3:
        return "high"
    elif severity >= 2:
        return "standard"
    else:
        return "low"


def _categorize_condition_type(category: str) -> ConditionType:
    """Categorize condition based on category string."""
    category_lower = category.lower()

    if "protection" in category_lower or "opérateur" in category_lower:
        return "safety"
    elif "environnement" in category_lower or "zone" in category_lower:
        return "environmental"
    elif "étiquetage" in category_lower or "mention" in category_lower:
        return "regulatory"
    else:
        return "application"


def _map_condition_importance(importance: str) -> HazardImportance:
    """Map condition importance string to enum."""
    importance_lower = importance.lower()

    if importance_lower == "high":
        return "high"
    elif importance_lower == "low":
        return "low"
    else:
        return "standard"


def _generate_regulatory_notes(
        basic_info: BasicProductInfo,
        conditions: List[UsageRestriction]
) -> List[str]:
    """Generate regulatory compliance notes."""
    notes = []

    # Authorization status
    if not basic_info.is_currently_authorized:
        notes.append("⚠️ Product is not currently authorized for use")
        if basic_info.withdrawal_date:
            notes.append(f"Withdrawn on: {basic_info.withdrawal_date}")

    # High-priority conditions
    critical_conditions = [c for c in conditions if c.importance_level == "critical"]
    if critical_conditions:
        notes.append(f"Critical compliance requirements: {len(critical_conditions)} conditions")

    # PPE requirements
    safety_conditions = [c for c in conditions if c.condition_type == "safety"]
    if safety_conditions:
        notes.append("Personal protective equipment required - see safety conditions")

    # Environmental restrictions
    env_conditions = [c for c in conditions if c.condition_type == "environmental"]
    if env_conditions:
        notes.append("Environmental restrictions apply - check buffer zones")

    if not notes:
        notes.append("Follow standard regulatory requirements")

    return notes


def _generate_safety_summary(
        hazards: List[SafetyHazard],
        conditions: List[UsageRestriction]
) -> str:
    """Generate overall safety summary."""
    if not hazards and not conditions:
        return "Limited safety information available. Follow label instructions."

    risk_level = "Low"
    key_concerns = []

    # Analyze hazards
    if hazards:
        max_severity = max(h.severity_level for h in hazards)
        if max_severity >= 4:
            risk_level = "Very High"
        elif max_severity >= 3:
            risk_level = "High"
        elif max_severity >= 2:
            risk_level = "Moderate"

        # Identify key concerns
        ppe_required = any(h.requires_ppe for h in hazards)
        if ppe_required:
            key_concerns.append("PPE required")

        critical_hazards = [h for h in hazards if h.safety_priority == "critical"]
        if critical_hazards:
            key_concerns.append(f"{len(critical_hazards)} critical hazards")

    # Analyze conditions
    safety_conditions = [c for c in conditions if c.condition_type == "safety"]
    if safety_conditions:
        key_concerns.append("Special safety conditions")

    summary = f"Safety Risk Level: {risk_level}"
    if key_concerns:
        summary += f" | Key Concerns: {', '.join(key_concerns)}"

    return summary


def _assess_data_completeness(
        basic_info: BasicProductInfo,
        substances: List[ActiveSubstanceInfo],
        composition: List[CompositionDetail],
        hazards: List[SafetyHazard],
        conditions: List[UsageRestriction]
) -> DataCompletenessScore:
    """Assess overall data completeness."""

    # Basic info completeness
    basic_fields = [
        basic_info.authorization_status != "Unknown",
        basic_info.function_category is not None,
        basic_info.holder != "Unknown",
        basic_info.product_type is not None,
        basic_info.first_authorization_date is not None
    ]
    basic_score = sum(basic_fields) / len(basic_fields)

    # Composition completeness
    composition_score = 1.0 if composition else 0.0

    # Hazards completeness
    hazards_score = 1.0 if hazards else 0.0

    # Conditions completeness
    conditions_score = 1.0 if conditions else 0.0

    # Substances completeness
    substances_score = 1.0 if substances else 0.0

    # Overall score
    overall_score = (
            basic_score * 0.3 +
            composition_score * 0.2 +
            hazards_score * 0.2 +
            conditions_score * 0.2 +
            substances_score * 0.1
    )

    # Identify missing elements
    missing_elements = []
    if composition_score == 0.0:
        missing_elements.append("Product composition")
    if hazards_score == 0.0:
        missing_elements.append("Safety hazard information")
    if conditions_score == 0.0:
        missing_elements.append("Usage conditions")
    if substances_score == 0.0:
        missing_elements.append("Active substance details")
    if not basic_info.function_category:
        missing_elements.append("Function category")

    return DataCompletenessScore(
        basic_info=basic_score,
        composition=composition_score,
        hazards=hazards_score,
        conditions=conditions_score,
        substances=substances_score,
        overall_score=overall_score,
        missing_elements=missing_elements
    )


def _generate_expert_recommendations(
        basic_info: BasicProductInfo,
        hazards: List[SafetyHazard],
        conditions: List[UsageRestriction],
        data_completeness: DataCompletenessScore
) -> List[str]:
    """Generate expert recommendations based on product profile."""
    recommendations = []

    # Authorization status
    if not basic_info.is_currently_authorized:
        recommendations.append("❌ Do not use - product authorization withdrawn")
        return recommendations

    # Data quality warnings
    if data_completeness.overall_score < 0.5:
        recommendations.append("⚠️ Limited product data available - verify with manufacturer")

    # Safety recommendations
    high_risk_hazards = [h for h in hazards if h.severity_level >= 3]
    if high_risk_hazards:
        recommendations.append("🔒 High-risk product - ensure proper training before use")
        recommendations.append("🥽 Mandatory protective equipment required")

    # Usage recommendations
    critical_conditions = [c for c in conditions if c.importance_level == "critical"]
    if critical_conditions:
        recommendations.append("📋 Critical usage conditions apply - review carefully")

    # Environmental considerations
    env_conditions = [c for c in conditions if c.condition_type == "environmental"]
    if env_conditions:
        recommendations.append("🌿 Environmental restrictions apply - check buffer zones")

    # General recommendations
    if not recommendations:
        recommendations.append("✅ Standard safety practices apply")
        recommendations.append("📖 Follow all label instructions")

    return recommendations
