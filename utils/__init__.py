# async def is_advisor(
#         ctx: RunContext[SChatRequest], tool_def: ToolDefinition
# ) -> Union[ToolDefinition, None]:
#     if ctx.deps.personal_context.email == '<EMAIL>':
#         logger.debug(f"User is advisor: {ctx.deps.personal_context.email}")
#         return tool_def
#
#
# async def is_farmer(
#         ctx: RunContext[SChatRequest], tool_def: ToolDefinition
# ) -> Union[ToolDefinition, None]:
#     if ctx.deps.personal_context.email == '<EMAIL>':
#         logger.debug(f"User is farmer: {ctx.deps.personal_context.email}")
#         return tool_def
#
#
# @agent.tool(prepare=is_advisor)
# async def get_purchase_orders_history(ctx: RunContext[SChatRequest]):
#     """
#     Returns overview of purchase orders history.
#     """
#     logger.debug(f"Fetching purchase orders for user: {ctx.deps.personal_context.id}")
#     user_id = ctx.deps.personal_context.id
#     raw_data = await fetch_purchase_order(user_id)
#     return raw_data
#
#
# @agent.tool(prepare=is_farmer)
# async def get_farm_insights(ctx: RunContext[SChatRequest]) -> str:
#     """
#     Returns overview of my farm's current state and recent activities.
#     """
#     logger.debug(f"Fetching farm insights for user: {ctx.deps.personal_context.id}")
#     user_id = ctx.deps.personal_context.id
#     raw_data = await fetch_farm_data(user_id)
#
#     # Handle API errors
#     if "error" in raw_data:
#         return f"Error accessing masParselle data: {raw_data['error']}"
#
#     # Initialize result sections
#     farm_info = []
#     parcel_info = []
#     intervention_info = []
#     warnings = []
#
#     # Process farm data
#     if "farms" in raw_data and raw_data["farms"]:
#         for farm in raw_data["farms"]:
#             name = farm.get("name", "Unnamed farm")
#             farm_id = farm.get("id", "Unknown ID")
#             farm_info.append(f"Farm: {name} (ID: {farm_id})")
#
#     # Process parcel data
#     if "parcels" in raw_data and raw_data["parcels"]:
#         for parcel in raw_data["parcels"]:
#             p_info = []
#             p_id = parcel.get("uuid", "Unknown")
#             area = parcel.get("measured_area_ha", "Unknown")
#             p_info.append(f"Parcel {p_id}: {area} hectares")
#
#             if "crop_rotation_history" in parcel and parcel["crop_rotation_history"]:
#                 rotation = " → ".join(parcel["crop_rotation_history"])
#                 p_info.append(f"  Crop rotation history: {rotation}")
#
#             parcel_info.append("\n".join(p_info))
#
#     # Process intervention data
#     if "interventions" in raw_data and raw_data["interventions"]:
#         for intervention in raw_data["interventions"]:
#             i_info = []
#             i_type = intervention.get("type", "Unknown activity")
#             crop = intervention.get("crop_name", "Unknown crop")
#             parcel_id = intervention.get("parcel_uuid", "Unknown parcel")
#
#             # Format dates
#             start_date = intervention.get("start_date", "")
#             if start_date:
#                 try:
#                     start_date = start_date.split("T")[0]
#                 except (AttributeError, IndexError):
#                     pass
#
#             i_info.append(f"{i_type} of {crop} on parcel {parcel_id} ({start_date})")
#
#             # Add input information if available
#             if "inputs" in intervention and intervention["inputs"]:
#                 input_details = []
#                 for input_item in intervention["inputs"]:
#                     input_name = input_item.get("name", "Unknown input")
#                     input_type = input_item.get("type", "")
#                     quantity = input_item.get("quantity", "")
#                     unit = input_item.get("unit", "")
#
#                     input_text = f"{input_name}"
#                     if input_type:
#                         input_text += f" ({input_type})"
#                     if quantity and unit:
#                         input_text += f": {quantity} {unit}"
#
#                     input_details.append(f"  - {input_text}")
#
#                 if input_details:
#                     i_info.append("  Inputs used:")
#                     i_info.extend(input_details)
#
#             intervention_info.append("\n".join(i_info))
#
#     # Process errors/warnings
#     if "errors" in raw_data and raw_data["errors"]:
#         for error in raw_data["errors"]:
#             message = error.get("message", "Unknown issue")
#             farm_id = error.get("farm_id", "")
#             if farm_id:
#                 warnings.append(f"Warning for farm {farm_id}: {message}")
#             else:
#                 warnings.append(f"Warning: {message}")
#
#     # Combine all sections
#     sections = []
#
#     if farm_info:
#         sections.append("FARM INFORMATION:")
#         sections.extend(farm_info)
#         sections.append("")
#
#     if parcel_info:
#         sections.append("PARCEL INFORMATION:")
#         sections.extend(parcel_info)
#         sections.append("")
#
#     if intervention_info:
#         sections.append("RECENT AGRICULTURAL ACTIVITIES:")
#         sections.extend(intervention_info)
#         sections.append("")
#
#     if warnings:
#         sections.append("ATTENTION REQUIRED:")
#         sections.extend(warnings)
#         sections.append("")
#
#     # Add citation
#     sections.append("---")
#     sections.append("Source: masParselle agricultural management system")
#
#     if not sections:
#         return "No relevant farm data found in masParselle."
#     txt = '\n'.join(sections)
#     return f"<FarmDataContext>\n{txt}\n</FarmDataContext>"
#
#
# return agent
