import logging
from typing import List, Dict, Any, Optional, Callable, AsyncGenerator

from crawl4ai import (
    BrowserConfig,
    CrawlerRunConfig,
    CacheMode,
    AsyncWebCrawler,
    DefaultMarkdownGenerator, PruningContentFilter
)
from crawl4ai.async_dispatcher import MemoryAdaptiveDispatcher
from crawl4ai.types import CrawlResult


class WebScraper:
    def __init__(self,
                 browser_config: Optional[BrowserConfig] = None,
                 max_concurrent: int = 5,
                 memory_threshold: float = 70.0,
                 check_robots_txt: bool = True,
                 cache_mode: CacheMode = CacheMode.BYPASS,
                 content_threshold: float = 0.4,
                 exclude_links: bool = True,
                 verbose: bool = False):
        """Initialize the WebScraper with configurable parameters.

        Args:
            browser_config: Optional custom browser configuration
            max_concurrent: Maximum number of concurrent crawling tasks
            memory_threshold: Memory threshold percentage to pause crawling
            check_robots_txt: Whether to respect robots.txt rules
            cache_mode: Cache mode for the crawler
            content_threshold: Threshold for content pruning (0.0-1.0)
            verbose: Whether to show detailed logs
        """
        self.max_concurrent = max_concurrent
        self.memory_threshold = memory_threshold
        self.check_robots_txt = check_robots_txt
        self.cache_mode = cache_mode
        self.content_threshold = content_threshold
        self.exclude_links = exclude_links
        self.verbose = verbose

        # Initialize with default browser config if none provided
        self.browser_config = browser_config or BrowserConfig(
            headless=True,
            verbose=verbose,
            extra_args=["--disable-gpu", "--disable-dev-shm-usage", "--no-sandbox"],
        )

        # Setup logging
        self.logger = logging.getLogger(__name__)
        level = logging.INFO if verbose else logging.WARNING
        logging.basicConfig(level=level,
                            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    async def run(self,
                  urls: List[str],
                  on_result_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """Crawl multiple URLs and return all results after completion.

        Args:
            urls: List of URLs to crawl
            on_result_callback: Optional callback for processing each result

        Returns:
            List of crawl results
        """
        results = []
        async for result in self._crawl_streaming(urls):
            if on_result_callback and result.get('success'):
                await on_result_callback(result)
            results.append(result)
        return results

    async def _crawl_streaming(self, urls: List[str]) -> AsyncGenerator[Dict[str, Any], None]:
        """Crawl multiple URLs and yield results as they become available.

        Args:
            urls: List of URLs to crawl

        Yields:
            Dictionary with crawl results for each URL
        """
        # BM25 - Selection of relevant chunks from the whole data
        # content_filter = BM25ContentFilter(
        #     user_query="health benefits fruit",
        #     bm25_threshold=1.2
        # )
        # Configure crawling
        crawl_config = CrawlerRunConfig(
            cache_mode=self.cache_mode,
            check_robots_txt=self.check_robots_txt,
            markdown_generator=DefaultMarkdownGenerator(
                content_filter=PruningContentFilter(
                    threshold=self.content_threshold,
                    threshold_type="fixed"
                ),
                options={
                    "ignore_links": False,
                    "escape_html": False,
                    "body_width": None,
                    "skip_internal_links": False,
                    "citations": False
                }),
            stream=True,  # Enable streaming mode for immediate processing
            word_count_threshold=1,  # Ensure we get content even for small pages
            excluded_tags=['header', 'footer'],  # Exclude header and footer elements
            exclude_external_links=self.exclude_links,
            exclude_internal_links=self.exclude_links,
            exclude_social_media_links=True,
        )

        # Configure dispatcher with memory-adaptive concurrency and monitoring
        dispatcher = MemoryAdaptiveDispatcher(
            memory_threshold_percent=self.memory_threshold,
            check_interval=1.0,
            max_session_permit=self.max_concurrent,
            # rate_limiter=RateLimiter(
            #     base_delay=(1.0, 3.0),  # Random delay between 1-3 seconds
            #     max_delay=30.0,         # Maximum delay of 30 seconds
            #     max_retries=3,          # Retry up to 3 times
            #     rate_limit_codes=[429, 503, 504]  # Status codes that trigger backoff
            # ),
            # monitor=CrawlerMonitor(
            #     enable_ui=True
            # ) if self.verbose else None
        )

        # Create and start crawler
        self.logger.info(f"Starting crawler for {len(urls)} URLs with max concurrency {self.max_concurrent}")
        crawler = AsyncWebCrawler(config=self.browser_config)
        await crawler.start()

        try:
            # Process URLs in streaming mode
            async for result in await crawler.arun_many(
                    urls=urls,
                    config=crawl_config,
                    dispatcher=dispatcher
            ):
                await self._handle_result(result)
                processed_result = {
                    'url': result.url,
                    'success': result.success,
                    # 'content': result.markdown.raw_markdown,
                    'content': result.markdown.fit_markdown,
                    # 'html': result.html if result.success else None,
                    'title': result.metadata.get("title"),
                    'error': result.error_message
                }

                # Add dispatch statistics if available
                if result.success and result.dispatch_result:
                    dr = result.dispatch_result
                    print(f"URL: {result.url}, Task ID: {dr.task_id}")
                    print(f"Memory: {dr.memory_usage:.1f} MB (Peak: {dr.peak_memory:.1f} MB)")
                    print(f"Duration: {dr.end_time - dr.start_time}")

                if result.success:
                    self.logger.info(f"Successfully crawled: {result.url}")
                else:
                    self.logger.warning(f"Failed to crawl {result.url}: {result.error_message}")

                yield processed_result

        finally:
            # Always close the crawler
            await crawler.close()
            self.logger.info("Crawler closed")

    async def _handle_result(self, result: CrawlResult):
        if not result.success:
            print("Crawl error:", result.error_message)
            return

        # Basic info
        print("Crawled URL:", result.url)
        print("Status code:", result.status_code)

        # HTML
        print("Original HTML size:", len(result.html))
        print("Cleaned HTML size:", len(result.cleaned_html or ""))

        # Markdown output
        if result.markdown:
            # print("Raw Markdown:", result.markdown.raw_markdown[:300])
            # print("Citations Markdown:", result.markdown.markdown_with_citations[:300])
            if result.markdown.fit_markdown:
                print("Fit Markdown:", result.markdown.fit_markdown[:200])
        if result.metadata:
            print("Title:", result.metadata.get("title"))
            print("Author:", result.metadata.get("author"))
        # Media & Links
        if "images" in result.media:
            print("Image count:", len(result.media["images"]))
        if "internal" in result.links:
            print("Internal link count:", len(result.links["internal"]))

        # Extraction strategy result
        if result.extracted_content:
            print("Structured data:", result.extracted_content)

        # Screenshot/PDF
        if result.screenshot:
            print("Screenshot length:", len(result.screenshot))
        if result.pdf:
            print("PDF bytes length:", len(result.pdf))


# Example usage
if __name__ == "__main__":
    async def main():

        # Initialize scraper and summarizer
        scraper = WebScraper(
            max_concurrent=5,
            memory_threshold=70.0,
            check_robots_txt=True,
            verbose=True
        )

        async def test(result):
            # print(f"\n--- Result {i + 1} ---")
            print(f"URL: {result['url']}")
            if result['success']:
                print(f"Title: {result['title']}")
                print(f"< <--\n {result['content']} > >")
            else:
                print(f"Error: {result['error']}")

        # Example URLs
        urls = [
            "https://www.lafranceagricole.fr/actualites/"
        ]

        # Process with immediate summarization
        results = await scraper.run(urls, test)


    # For local testing
    import asyncio

    asyncio.run(main())
