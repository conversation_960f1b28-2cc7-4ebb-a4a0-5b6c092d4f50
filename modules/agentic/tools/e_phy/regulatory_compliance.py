"""
Enhanced Regulatory Compliance Checker Tool for E-Phy Agricultural Database.

This tool provides comprehensive compliance verification with AI-optimized decision support
for agricultural agents. Features include:

- Fuzzy search with confidence scoring for crops and targets
- Enhanced user type authorization logic with better amateur/professional distinction  
- AI-friendly decision recommendations (approved/conditional/rejected)
- Alternative product suggestions when compliance issues are found
- Rich data quality assessment and missing data warnings
- Detailed safety scoring and risk factor identification
- Automation-friendly restriction categorization
- Context-aware regulatory guidance

Designed specifically for AI agents to make informed compliance decisions
with clear actionable recommendations and confidence levels.
"""
import re
from dataclasses import dataclass
from datetime import date
from difflib import SequenceMatcher
from typing import List, Optional, Dict, Any

from sqlalchemy import select, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from models.e_phy import Product, ProductUse, Crop, Target
from .types import UserType, HazardImportance


@dataclass
class ComplianceViolation:
    """Compliance violation or warning."""
    violation_type: str
    severity: str  # "critical", "high", "medium", "low"
    description: str
    legal_reference: Optional[str]
    remediation_steps: List[str]
    impact_score: float  # 0-1 scale for AI decision making
    ai_recommendation: str  # Clear action for AI agent


@dataclass
class RestrictionInfo:
    """Usage restriction information."""
    restriction_type: str
    description: str
    compliance_level: str  # "mandatory", "recommended", "advisory"
    applies_to_user_type: bool
    penalty_risk: str
    ai_priority: str  # "must_follow", "should_follow", "consider"
    automation_friendly: bool  # Can this be automated/checked?


@dataclass
class SafetyWarning:
    """Safety warning information."""
    warning_type: str
    message: str
    severity: HazardImportance
    ppe_required: bool
    training_required: bool
    hazard_codes: List[str]  # H-codes for reference
    safety_score: float  # 0-1 risk assessment
    mitigation_required: bool  # Immediate action needed


@dataclass
class ProductCompliance:
    """Individual product compliance assessment."""
    product_id: int
    product_name: str
    registration_number: str
    authorization_status: str
    is_currently_authorized: bool
    intended_use_authorized: bool
    user_type_authorized: bool
    restrictions: List[RestrictionInfo]
    violations: List[ComplianceViolation]
    warnings: List[SafetyWarning]
    compliance_score: float
    regulatory_notes: List[str]
    # AI-friendly additions
    ai_decision: str  # "approved", "conditional", "rejected"
    confidence_level: float  # 0-1 confidence in assessment
    usage_context: Dict[str, Any]  # Matched crop/target context
    alternative_suggestions: List[str]  # If rejected, suggest alternatives
    risk_factors: List[str]  # Key risks for AI to consider


@dataclass
class ComplianceSummary:
    """Overall compliance summary."""
    overall_status: str  # "compliant", "non_compliant", "conditional", "requires_review"
    products_checked: int
    violations_found: int
    warnings_issued: int
    critical_issues: int
    compliance_percentage: float
    # AI decision support
    recommended_action: str  # Clear action recommendation
    risk_level: str  # "low", "medium", "high", "critical"
    can_proceed: bool  # Simple boolean for AI decisions
    blocking_issues: List[str]  # What prevents approval
    data_quality_score: float  # How reliable is this assessment


@dataclass
class ComplianceResponse:
    """Complete compliance check response."""
    compliance_summary: ComplianceSummary
    product_compliance: List[ProductCompliance]
    general_recommendations: List[str]
    regulatory_updates: List[str]
    consultation_advice: List[str]
    legal_disclaimers: List[str]
    # AI agent enhancements
    search_quality: Dict[str, float]  # How well crops/targets were matched
    missing_data_warnings: List[str]  # What data was incomplete
    alternative_products: List[Dict[str, Any]]  # Better options if available
    regulatory_context: Dict[str, Any]  # Additional context for decisions


async def check_regulatory_compliance(
        session: AsyncSession,
        product_ids: List[int],
        intended_use: Dict[str, Any],
        region: str = "france",
        user_type: UserType = "professional",
        check_date: Optional[str] = None,
        include_warnings: bool = True,
        include_alternatives: bool = True,
        strict_matching: bool = False,
        ai_context: Optional[Dict[str, Any]] = None
) -> ComplianceResponse:
    """
    Verify authorization status, restrictions, and legal compliance.
    Enhanced for AI agent decision making with fuzzy matching and context.
    
    Args:
        product_ids: List of product IDs to check
        intended_use: Dict with crop, target, application_method, dosage, timing
        region: Regulatory jurisdiction (currently only "france")
        user_type: User type for specific restrictions
        check_date: Specific date for compliance (default: today)
        include_warnings: Include safety and restriction warnings
        include_alternatives: Find alternative products if issues found
        strict_matching: Use exact matching vs fuzzy for crops/targets
        ai_context: Additional context for AI decision making
    
    Returns:
        ComplianceResponse with detailed compliance assessment and AI guidance
    """

    if not product_ids:
        return _create_empty_response()

    # Parse intended use with enhanced context
    crop_name = intended_use.get("crop")
    target_name = intended_use.get("target")

    # Initialize search quality tracking
    search_quality = {"crop_match": 0.0, "target_match": 0.0}
    missing_data_warnings = []

    # Find crop and target with enhanced search
    crop = None
    target = None
    if crop_name:
        crop, search_quality["crop_match"] = await _find_crop_enhanced(session, crop_name, strict_matching)
        if crop is None:
            missing_data_warnings.append(f"Crop '{crop_name}' not found - compliance may be incomplete")
    if target_name:
        target, search_quality["target_match"] = await _find_target_enhanced(session, target_name, strict_matching)
        if target is None:
            missing_data_warnings.append(f"Target '{target_name}' not found - compliance may be incomplete")

    # Check each product with enhanced analysis
    product_compliance_results = []

    for product_id in product_ids:
        product_compliance = await _check_product_compliance_enhanced(
            session, product_id, crop, target, user_type, include_warnings,
            intended_use, ai_context
        )
        if product_compliance:
            product_compliance_results.append(product_compliance)

    # Find alternatives if requested and issues found
    alternative_products = []
    if include_alternatives and crop and target:
        alternative_products = await _find_alternative_products(
            session, crop, target, user_type, product_ids
        )

    # Generate enhanced summary with AI decision support
    compliance_summary = _generate_compliance_summary_enhanced(
        product_compliance_results, search_quality, missing_data_warnings
    )

    # Generate AI-optimized recommendations
    general_recommendations = _generate_ai_recommendations(
        product_compliance_results, user_type, intended_use
    )

    regulatory_updates = _generate_regulatory_updates(product_compliance_results)
    consultation_advice = _generate_consultation_advice(compliance_summary, user_type)
    legal_disclaimers = _generate_legal_disclaimers(region)

    # Build regulatory context for AI
    regulatory_context = {
        "region": region,
        "check_date": check_date or date.today().isoformat(),
        "authorization_rate": sum(1 for pc in product_compliance_results if pc.is_currently_authorized) / len(
            product_compliance_results) if product_compliance_results else 0,
        "avg_compliance_score": sum(pc.compliance_score for pc in product_compliance_results) / len(
            product_compliance_results) if product_compliance_results else 0,
        "data_completeness": 1.0 - len(missing_data_warnings) * 0.2
    }

    return ComplianceResponse(
        compliance_summary=compliance_summary,
        product_compliance=product_compliance_results,
        general_recommendations=general_recommendations,
        regulatory_updates=regulatory_updates,
        consultation_advice=consultation_advice,
        legal_disclaimers=legal_disclaimers,
        search_quality=search_quality,
        missing_data_warnings=missing_data_warnings,
        alternative_products=alternative_products,
        regulatory_context=regulatory_context
    )


async def _find_crop(session: AsyncSession, crop_name: str) -> Optional[Crop]:
    """Find crop by name - legacy function."""
    crop, _ = await _find_crop_enhanced(session, crop_name, False)
    return crop


async def _find_crop_enhanced(
        session: AsyncSession,
        crop_name: str,
        strict_matching: bool = False
) -> tuple[Optional[Crop], float]:
    """Enhanced crop search with fuzzy matching and confidence scoring."""
    crop_name_clean = crop_name.strip().lower()

    # Try exact matches first
    exact_query = select(Crop).where(
        or_(
            func.lower(Crop.crop_name) == crop_name_clean,
            func.lower(Crop.normalized_name) == crop_name_clean
        )
    )
    result = await session.execute(exact_query)
    exact_match = result.scalar_one_or_none()
    if exact_match:
        return exact_match, 1.0

    if strict_matching:
        return None, 0.0

    # Try partial matches
    partial_query = select(Crop).where(
        or_(
            Crop.crop_name.ilike(f"%{crop_name_clean}%"),
            Crop.normalized_name.ilike(f"%{crop_name_clean}%"),
            Crop.common_synonyms.ilike(f"%{crop_name_clean}%")
        )
    )
    result = await session.execute(partial_query)
    candidates = result.scalars().all()

    if not candidates:
        return None, 0.0

    # Score candidates by similarity
    best_match = None
    best_score = 0.0

    for candidate in candidates:
        # Check all possible name matches
        names_to_check = [candidate.crop_name, candidate.normalized_name]
        if candidate.common_synonyms:
            names_to_check.extend(candidate.common_synonyms.split(","))

        for name in names_to_check:
            if name:
                name_clean = name.strip().lower()
                similarity = SequenceMatcher(None, crop_name_clean, name_clean).ratio()
                if similarity > best_score:
                    best_score = similarity
                    best_match = candidate

    # Only return if confidence is reasonable
    return (best_match, best_score) if best_score >= 0.6 else (None, 0.0)


async def _find_target(session: AsyncSession, target_name: str) -> Optional[Target]:
    """Find target by name - legacy function."""
    target, _ = await _find_target_enhanced(session, target_name, False)
    return target


async def _find_target_enhanced(
        session: AsyncSession,
        target_name: str,
        strict_matching: bool = False
) -> tuple[Optional[Target], float]:
    """Enhanced target search with fuzzy matching and confidence scoring."""
    target_name_clean = target_name.strip().lower()

    # Try exact matches first
    exact_query = select(Target).where(
        or_(
            func.lower(Target.target_name) == target_name_clean,
            func.lower(Target.normalized_name) == target_name_clean
        )
    )
    result = await session.execute(exact_query)
    exact_match = result.scalar_one_or_none()
    if exact_match:
        return exact_match, 1.0

    if strict_matching:
        return None, 0.0

    # Try partial matches with pattern cleaning
    # Remove common patterns like "(1)", "(2)" etc.
    clean_target = re.sub(r'\(\d+\)', '', target_name_clean).strip()

    partial_query = select(Target).where(
        or_(
            Target.target_name.ilike(f"%{clean_target}%"),
            Target.normalized_name.ilike(f"%{clean_target}%"),
            Target.target_name.ilike(f"%{target_name_clean}%"),
            Target.normalized_name.ilike(f"%{target_name_clean}%")
        )
    )
    result = await session.execute(partial_query)
    candidates = result.scalars().all()

    if not candidates:
        return None, 0.0

    # Score candidates by similarity
    best_match = None
    best_score = 0.0

    for candidate in candidates:
        names_to_check = [candidate.target_name, candidate.normalized_name]

        for name in names_to_check:
            if name:
                name_clean = name.strip().lower()
                # Try both original and cleaned versions
                similarity1 = SequenceMatcher(None, target_name_clean, name_clean).ratio()
                similarity2 = SequenceMatcher(None, clean_target, name_clean).ratio()
                similarity = max(similarity1, similarity2)

                if similarity > best_score:
                    best_score = similarity
                    best_match = candidate

    return (best_match, best_score) if best_score >= 0.6 else (None, 0.0)


async def _check_product_compliance(
        session: AsyncSession,
        product_id: int,
        crop: Optional[Crop],
        target: Optional[Target],
        user_type: UserType,
        include_warnings: bool
) -> Optional[ProductCompliance]:
    """Legacy compliance check - use enhanced version."""
    return await _check_product_compliance_enhanced(
        session, product_id, crop, target, user_type, include_warnings, {}, None
    )


async def _check_product_compliance_enhanced(
        session: AsyncSession,
        product_id: int,
        crop: Optional[Crop],
        target: Optional[Target],
        user_type: UserType,
        include_warnings: bool,
        intended_use: Dict[str, Any],
        ai_context: Optional[Dict[str, Any]]
) -> Optional[ProductCompliance]:
    """Check compliance for a single product."""

    # Get product with related data
    product_query = select(Product).options(
        selectinload(Product.conditions),
        selectinload(Product.hazards),
        selectinload(Product.uses)
    ).where(Product.id == product_id)

    result = await session.execute(product_query)
    product = result.scalar_one_or_none()

    if not product:
        return None

    # Check basic authorization
    violations = []
    warnings = []

    # Critical: Product authorization status
    if not product.is_currently_authorized:
        violations.append(ComplianceViolation(
            violation_type="authorization",
            severity="critical",
            description="Product is not currently authorized for use",
            legal_reference="EU Regulation 1107/2009",
            remediation_steps=[
                "Do not use this product",
                "Find authorized alternative products",
                "Consult local regulatory authority"
            ],
            impact_score=1.0,
            ai_recommendation="REJECT - Product authorization withdrawn or expired"
        ))

    # Check intended use authorization
    intended_use_authorized = True
    if crop and target:
        intended_use_authorized = await _check_intended_use_authorization(
            session, product_id, crop.id, target.id
        )

        if not intended_use_authorized:
            violations.append(ComplianceViolation(
                violation_type="intended_use",
                severity="high",
                description=f"Product not authorized for use on {crop.crop_name} against {target.target_name}",
                legal_reference="Product registration conditions",
                remediation_steps=[
                    "Verify crop and target combination",
                    "Check product label for authorized uses",
                    "Consider alternative products"
                ],
                impact_score=0.8,
                ai_recommendation="CONDITIONAL - Check for authorized crop/target combinations"
            ))

    # Check user type restrictions
    user_type_authorized = _check_user_type_authorization(product, user_type)
    if not user_type_authorized:
        violations.append(ComplianceViolation(
            violation_type="user_authorization",
            severity="high",
            description=f"Product restricted for {user_type} use",
            legal_reference="Product authorization conditions",
            remediation_steps=[
                "Obtain required certification",
                "Use only authorized applicators",
                "Consult product distributor"
            ],
            impact_score=0.7,
            ai_recommendation="CONDITIONAL - Check user certification requirements"
        ))

    # Process restrictions with AI-friendly enhancements
    restrictions = []
    if product.conditions:
        for condition in product.conditions:
            restriction = RestrictionInfo(
                restriction_type=condition.condition_category or "General",
                description=condition.condition_description or "No description",
                compliance_level=_map_compliance_level(condition.condition_importance),
                applies_to_user_type=True,  # Simplified - could be more specific
                penalty_risk=_assess_penalty_risk(condition.condition_importance),
                ai_priority=_map_ai_priority(condition.condition_importance),
                automation_friendly=_is_automation_friendly(condition.condition_category)
            )
            restrictions.append(restriction)

    # Process safety warnings with enhanced AI context
    if include_warnings and product.hazards:
        for hazard in product.hazards:
            warning = SafetyWarning(
                warning_type="safety_hazard",
                message=hazard.hazard_description or f"Hazard code: {hazard.hazard_code}",
                severity=_map_hazard_severity(hazard.hazard_severity),
                ppe_required=hazard.requires_special_equipment or False,
                training_required=hazard.hazard_severity and hazard.hazard_severity >= 3,
                hazard_codes=[hazard.hazard_code] if hazard.hazard_code else [],
                safety_score=_calculate_safety_score(hazard.hazard_severity),
                mitigation_required=hazard.hazard_severity and hazard.hazard_severity >= 3
            )
            warnings.append(warning)

    # Check withdrawal dates
    if product.withdrawal_date and product.withdrawal_date <= date.today():
        violations.append(ComplianceViolation(
            violation_type="withdrawal",
            severity="critical",
            description=f"Product withdrawn on {product.withdrawal_date}",
            legal_reference="Product withdrawal notice",
            remediation_steps=[
                "Stop using this product immediately",
                "Dispose of remaining product properly",
                "Find authorized alternatives"
            ],
            impact_score=1.0,
            ai_recommendation="REJECT - Product officially withdrawn from market"
        ))

    # Calculate compliance score and AI decision
    compliance_score = _calculate_compliance_score(violations, warnings)
    ai_decision, confidence_level = _calculate_ai_decision(violations, warnings, compliance_score)

    # Generate context for AI decision making
    usage_context = {
        "crop": crop.crop_name if crop else None,
        "target": target.target_name if target else None,
        "product_function": product.function_category
    }

    # Generate alternative suggestions if needed
    alternative_suggestions = []
    if ai_decision == "rejected" and violations:
        alternative_suggestions = _generate_alternative_suggestions(violations)

    # Identify risk factors for AI consideration
    risk_factors = _identify_risk_factors(product, violations, warnings)

    # Generate regulatory notes
    regulatory_notes = _generate_regulatory_notes(product, violations, warnings)

    return ProductCompliance(
        product_id=product.id,
        product_name=product.product_name,
        registration_number=product.registration_number,
        authorization_status=product.authorization_status or "Unknown",
        is_currently_authorized=product.is_currently_authorized or False,
        intended_use_authorized=intended_use_authorized,
        user_type_authorized=user_type_authorized,
        restrictions=restrictions,
        violations=violations,
        warnings=warnings,
        compliance_score=compliance_score,
        regulatory_notes=regulatory_notes,
        ai_decision=ai_decision,
        confidence_level=confidence_level,
        usage_context=usage_context,
        alternative_suggestions=alternative_suggestions,
        risk_factors=risk_factors
    )


async def _check_intended_use_authorization(
        session: AsyncSession,
        product_id: int,
        crop_id: int,
        target_id: int
) -> bool:
    """Check if specific crop-target use is authorized."""
    query = select(ProductUse).where(
        and_(
            ProductUse.product_id == product_id,
            ProductUse.crop_id == crop_id,
            ProductUse.target_id == target_id,
            ProductUse.is_currently_authorized == True
        )
    )

    result = await session.execute(query)
    use = result.scalar_one_or_none()
    return use is not None


def _check_user_type_authorization(product: Product, user_type: UserType) -> bool:
    """Check if user type is authorized to use product with improved logic."""

    # Check authorized mentions for user restrictions
    if product.authorized_mentions:
        mentions_lower = product.authorized_mentions.lower()

        # Amateur/garden use
        has_amateur_auth = "jardin" in mentions_lower or "amateur" in mentions_lower
        has_professional_restriction = "professionnel" in mentions_lower

        if user_type == "amateur":
            # Amateurs can only use products explicitly authorized for gardens/amateur use
            return has_amateur_auth

        elif user_type == "professional":
            # Professionals can use:
            # 1. Products with no specific restrictions
            # 2. Products explicitly marked for professional use
            # 3. Products marked for both amateur and professional use
            if has_professional_restriction:
                return True  # Explicitly for professionals
            elif has_amateur_auth and "professionnel" not in mentions_lower:
                return False  # Amateur only, professionals restricted
            else:
                return True  # General use or no specific restrictions

        elif user_type == "certified":
            return True  # Certified applicators can use any authorized product

    # If no authorized mentions, default based on user type
    # Amateurs need explicit authorization, others can proceed
    return user_type in ["professional", "certified"]


def _map_compliance_level(importance: Optional[str]) -> str:
    """Map condition importance to compliance level."""
    if not importance:
        return "recommended"

    importance_lower = importance.lower()
    if importance_lower == "high":
        return "mandatory"
    elif importance_lower == "medium":
        return "recommended"
    elif importance_lower == "low":
        return "advisory"
    else:
        return "recommended"  # standard and other values


def _assess_penalty_risk(importance: Optional[str]) -> str:
    """Assess penalty risk for non-compliance."""
    if not importance:
        return "low"

    importance_lower = importance.lower()
    if importance_lower == "high":
        return "high"
    elif importance_lower == "medium":
        return "moderate"
    elif importance_lower == "low":
        return "minimal"
    else:
        return "low"  # standard and other values


def _map_hazard_severity(severity: Optional[int]) -> HazardImportance:
    """Map numeric hazard severity to importance level."""
    if not severity:
        return "low"

    if severity >= 4:
        return "critical"
    elif severity >= 3:
        return "high"
    elif severity >= 2:
        return "standard"
    else:
        return "low"


def _map_ai_priority(importance: Optional[str]) -> str:
    """Map condition importance to AI priority level."""
    if not importance:
        return "consider"

    importance_lower = importance.lower()
    if importance_lower == "high":
        return "must_follow"
    elif importance_lower in ["medium", "standard"]:
        return "should_follow"
    else:
        return "consider"


def _is_automation_friendly(category: Optional[str]) -> bool:
    """Determine if a condition can be automated or easily checked."""
    if not category:
        return False

    category_lower = category.lower()
    automation_friendly_categories = [
        "délai de rentrée",
        "respect lmr",
        "étiquetage",
        "stockage"
    ]

    return any(cat in category_lower for cat in automation_friendly_categories)


def _calculate_safety_score(severity: Optional[int]) -> float:
    """Calculate a 0-1 safety score where 0 is safest."""
    if not severity:
        return 0.1

    # Invert severity to safety score
    safety_map = {1: 0.2, 2: 0.5, 3: 0.8, 4: 1.0, 5: 1.0}
    return safety_map.get(severity, 0.1)


def _calculate_ai_decision(violations: List[ComplianceViolation], warnings: List[SafetyWarning],
                           compliance_score: float) -> tuple[str, float]:
    """Calculate AI decision and confidence level."""
    critical_violations = [v for v in violations if v.severity == "critical"]
    high_violations = [v for v in violations if v.severity == "high"]
    critical_warnings = [w for w in warnings if w.severity == "critical"]

    # Decision logic
    if critical_violations:
        return "rejected", 0.95
    elif len(high_violations) >= 2 or critical_warnings:
        return "conditional", 0.8
    elif compliance_score >= 0.8:
        return "approved", 0.9
    elif compliance_score >= 0.6:
        return "conditional", 0.7
    else:
        return "rejected", 0.8


def _generate_alternative_suggestions(violations: List[ComplianceViolation]) -> List[str]:
    """Generate alternative suggestions based on violations."""
    suggestions = []

    for violation in violations:
        if violation.violation_type == "authorization":
            suggestions.append("Find currently authorized alternative products")
        elif violation.violation_type == "intended_use":
            suggestions.append("Check product label for other authorized crop/target combinations")
        elif violation.violation_type == "user_authorization":
            suggestions.append("Consider professional application or certification")

    return list(set(suggestions))  # Remove duplicates


def _identify_risk_factors(product: Product, violations: List[ComplianceViolation], warnings: List[SafetyWarning]) -> \
List[str]:
    """Identify key risk factors for AI consideration."""
    risks = []

    if not product.is_currently_authorized:
        risks.append("Product not currently authorized")

    if product.withdrawal_date and product.withdrawal_date <= date.today():
        risks.append("Product officially withdrawn")

    high_severity_warnings = [w for w in warnings if w.severity in ["critical", "high"]]
    if high_severity_warnings:
        risks.append(f"{len(high_severity_warnings)} high-risk safety concerns")

    critical_violations = [v for v in violations if v.severity == "critical"]
    if critical_violations:
        risks.append(f"{len(critical_violations)} critical compliance violations")

    return risks


def _calculate_compliance_score(
        violations: List[ComplianceViolation],
        warnings: List[SafetyWarning]
) -> float:
    """Calculate overall compliance score (0-1)."""

    if not violations and not warnings:
        return 1.0

    # Deduct points for violations
    score = 1.0

    for violation in violations:
        if violation.severity == "critical":
            score -= 0.4
        elif violation.severity == "high":
            score -= 0.3
        elif violation.severity == "medium":
            score -= 0.2
        else:
            score -= 0.1

    # Deduct smaller amounts for warnings
    for warning in warnings:
        if warning.severity == "critical":
            score -= 0.1
        elif warning.severity == "high":
            score -= 0.05
        else:
            score -= 0.02

    return max(score, 0.0)


def _generate_regulatory_notes(
        product: Product,
        violations: List[ComplianceViolation],
        warnings: List[SafetyWarning]
) -> List[str]:
    """Generate regulatory notes for the product."""
    notes = []

    # Authorization notes
    if product.is_currently_authorized:
        notes.append("✅ Product currently authorized")
    else:
        notes.append("❌ Product authorization withdrawn or expired")

    # Violation summary
    if violations:
        critical_violations = [v for v in violations if v.severity == "critical"]
        if critical_violations:
            notes.append(f"⛔ {len(critical_violations)} critical compliance issues")

    # Safety notes
    if warnings:
        high_risk_warnings = [w for w in warnings if w.severity in ["critical", "high"]]
        if high_risk_warnings:
            notes.append(f"⚠️ {len(high_risk_warnings)} high-risk safety concerns")

    # Registration info
    if product.first_authorization_date:
        notes.append(f"First authorized: {product.first_authorization_date}")

    if product.withdrawal_date:
        notes.append(f"Withdrawn: {product.withdrawal_date}")

    return notes


async def _find_alternative_products(
        session: AsyncSession,
        crop: Crop,
        target: Target,
        user_type: UserType,
        exclude_product_ids: List[int]
) -> List[Dict[str, Any]]:
    """Find alternative products for the same crop/target combination."""

    # Query for authorized alternatives
    query = select(Product).join(ProductUse).where(
        and_(
            ProductUse.crop_id == crop.id,
            ProductUse.target_id == target.id,
            ProductUse.is_currently_authorized == True,
            Product.is_currently_authorized == True,
            ~Product.id.in_(exclude_product_ids)
        )
    ).limit(5)

    result = await session.execute(query)
    alternatives = result.scalars().all()

    alternative_list = []
    for alt in alternatives:
        # Quick user type check
        user_authorized = _check_user_type_authorization(alt, user_type)

        alternative_list.append({
            "product_id": alt.id,
            "product_name": alt.product_name,
            "registration_number": alt.registration_number,
            "function_category": alt.function_category,
            "user_type_compatible": user_authorized,
            "recommendation_score": 0.8 if user_authorized else 0.4
        })

    # Sort by recommendation score
    alternative_list.sort(key=lambda x: x["recommendation_score"], reverse=True)
    return alternative_list


def _generate_compliance_summary(
        product_compliance_results: List[ProductCompliance]
) -> ComplianceSummary:
    """Legacy summary generation - use enhanced version."""
    return _generate_compliance_summary_enhanced(
        product_compliance_results, {"crop_match": 1.0, "target_match": 1.0}, []
    )


def _generate_compliance_summary_enhanced(
        product_compliance_results: List[ProductCompliance],
        search_quality: Dict[str, float],
        missing_data_warnings: List[str]
) -> ComplianceSummary:
    """Generate overall compliance summary."""

    if not product_compliance_results:
        return ComplianceSummary(
            overall_status="no_products",
            products_checked=0,
            violations_found=0,
            warnings_issued=0,
            critical_issues=0,
            compliance_percentage=0.0,
            recommended_action="Provide valid product IDs for compliance checking",
            risk_level="unknown",
            can_proceed=False,
            blocking_issues=["No products provided for analysis"],
            data_quality_score=0.0
        )

    total_products = len(product_compliance_results)
    total_violations = sum(len(pc.violations) for pc in product_compliance_results)
    total_warnings = sum(len(pc.warnings) for pc in product_compliance_results)

    critical_issues = sum(
        len([v for v in pc.violations if v.severity == "critical"])
        for pc in product_compliance_results
    )

    # Calculate compliance percentage
    compliant_products = sum(1 for pc in product_compliance_results if pc.compliance_score >= 0.8)
    compliance_percentage = (compliant_products / total_products) * 100

    # AI decision analysis
    approved_products = sum(1 for pc in product_compliance_results if pc.ai_decision == "approved")
    rejected_products = sum(1 for pc in product_compliance_results if pc.ai_decision == "rejected")

    # Calculate data quality score
    avg_search_quality = sum(search_quality.values()) / len(search_quality) if search_quality else 1.0
    data_completeness = 1.0 - (len(missing_data_warnings) * 0.1)
    data_quality_score = (avg_search_quality + data_completeness) / 2

    # Determine overall status with enhanced logic
    if critical_issues > 0:
        overall_status = "non_compliant"
        recommended_action = "Critical issues must be resolved before proceeding"
        risk_level = "critical"
        can_proceed = False
    elif rejected_products == total_products:
        overall_status = "non_compliant"
        recommended_action = "All products rejected - find alternatives"
        risk_level = "high"
        can_proceed = False
    elif approved_products >= total_products * 0.8:
        overall_status = "compliant"
        recommended_action = "Proceed with approved products and restrictions"
        risk_level = "low"
        can_proceed = True
    elif compliance_percentage >= 60:
        overall_status = "conditional"
        recommended_action = "Review conditional approvals and restrictions"
        risk_level = "medium"
        can_proceed = True
    else:
        overall_status = "requires_review"
        recommended_action = "Manual review required before proceeding"
        risk_level = "high"
        can_proceed = False

    # Identify blocking issues
    blocking_issues = []
    if critical_issues > 0:
        blocking_issues.append(f"{critical_issues} critical compliance violations")
    if data_quality_score < 0.5:
        blocking_issues.append("Poor data quality - search results unreliable")
    if rejected_products > 0:
        blocking_issues.append(f"{rejected_products} products rejected for use")

    return ComplianceSummary(
        overall_status=overall_status,
        products_checked=total_products,
        violations_found=total_violations,
        warnings_issued=total_warnings,
        critical_issues=critical_issues,
        compliance_percentage=compliance_percentage,
        recommended_action=recommended_action,
        risk_level=risk_level,
        can_proceed=can_proceed,
        blocking_issues=blocking_issues,
        data_quality_score=data_quality_score
    )


def _generate_ai_recommendations(
        product_compliance_results: List[ProductCompliance],
        user_type: UserType,
        intended_use: Dict[str, Any]
) -> List[str]:
    """Generate AI-optimized recommendations for decision making."""
    recommendations = []

    # Quick decision summary
    approved = [pc for pc in product_compliance_results if pc.ai_decision == "approved"]
    conditional = [pc for pc in product_compliance_results if pc.ai_decision == "conditional"]
    rejected = [pc for pc in product_compliance_results if pc.ai_decision == "rejected"]

    if approved:
        recommendations.append(f"✅ {len(approved)} products approved for immediate use")

    if conditional:
        recommendations.append(f"⚠️ {len(conditional)} products require additional conditions")

    if rejected:
        recommendations.append(f"❌ {len(rejected)} products rejected - alternatives needed")

    # Context-specific recommendations
    if intended_use.get("crop") and intended_use.get("target"):
        recommendations.append(f"🎯 Compliance checked for {intended_use['crop']} against {intended_use['target']}")

    # Critical safety alerts
    high_risk_products = [
        pc for pc in product_compliance_results
        if any(w.severity in ["critical", "high"] for w in pc.warnings)
    ]

    if high_risk_products:
        recommendations.append(f"⚠️ {len(high_risk_products)} products have high safety risks - PPE required")

    # User-specific guidance
    if user_type == "amateur":
        amateur_safe = [pc for pc in product_compliance_results if pc.user_type_authorized]
        recommendations.append(f"🏠 {len(amateur_safe)} products suitable for amateur use")

    return recommendations


def _generate_regulatory_updates(
        product_compliance_results: List[ProductCompliance]
) -> List[str]:
    """Generate information about regulatory updates."""
    updates = []

    # Withdrawal notices
    withdrawn_products = [
        pc for pc in product_compliance_results
        if not pc.is_currently_authorized
    ]

    if withdrawn_products:
        updates.append(f"📢 {len(withdrawn_products)} products have authorization changes")

    # General regulatory info
    updates.extend([
        "🇪🇺 EU pesticide regulations continue to evolve",
        "🔍 Regular authorization reviews may affect product availability",
        "📱 Monitor official regulatory announcements"
    ])

    return updates


def _generate_consultation_advice(
        compliance_summary: ComplianceSummary,
        user_type: UserType
) -> List[str]:
    """Generate advice on when to consult experts."""
    advice = []

    if compliance_summary.overall_status == "non_compliant":
        advice.extend([
            "🚨 Immediate consultation with regulatory expert recommended",
            "⚖️ Legal advice may be necessary for critical violations"
        ])

    elif compliance_summary.overall_status == "conditional":
        advice.extend([
            "👨‍🌾 Consult agricultural advisor for compliance optimization",
            "📞 Contact product manufacturers for clarification"
        ])

    # User type specific advice
    if user_type == "amateur":
        advice.append("🏪 Consult local garden center for amateur-appropriate products")
    elif user_type == "professional":
        advice.append("🏢 Consider professional compliance training")

    return advice


def _generate_legal_disclaimers(region: str) -> List[str]:
    """Generate legal disclaimers for compliance checking."""
    disclaimers = [
        "⚖️ This compliance check is advisory only and not legal advice",
        "📅 Regulatory status may change - verify current authorization",
        "🏛️ Final compliance responsibility rests with the user",
        "📞 Consult official regulatory authorities for definitive guidance"
    ]

    if region == "france":
        disclaimers.extend([
            "🇫🇷 Based on French E-Phy database information",
            "🏛️ ANSES is the authoritative source for product authorization"
        ])

    return disclaimers


def _create_empty_response() -> ComplianceResponse:
    """Create response when no products provided."""
    return ComplianceResponse(
        compliance_summary=ComplianceSummary(
            overall_status="no_products",
            products_checked=0,
            violations_found=0,
            warnings_issued=0,
            critical_issues=0,
            compliance_percentage=0.0,
            recommended_action="Provide product IDs to check compliance",
            risk_level="unknown",
            can_proceed=False,
            blocking_issues=["No products provided for analysis"],
            data_quality_score=0.0
        ),
        product_compliance=[],
        general_recommendations=["Provide product IDs to check compliance"],
        regulatory_updates=[],
        consultation_advice=[],
        legal_disclaimers=[],
        search_quality={"crop_match": 0.0, "target_match": 0.0},
        missing_data_warnings=["No products provided"],
        alternative_products=[],
        regulatory_context={"region": "france", "check_date": date.today().isoformat()}
    )
