import asyncio
import json
from datetime import datetime, timezone
from typing import TypedDict, Literal

from fastapi import APIRouter
from pydantic_ai import Agent, UnexpectedModelBehavior
from pydantic_ai.messages import ModelResponse, ModelMessage, ModelRequest, UserPromptPart, TextPart
from sse_starlette import EventSourceResponse
from starlette.responses import StreamingResponse

from modules.agentic.agent_service import AgentService
from schemas.assistant import SChatRequest

router = APIRouter(tags=["AI Assistant"])


# SSE endpoint for streaming responses
@router.post("/chat/stream")
async def stream_chat(
        data: SChatRequest,
):
    agent_service = AgentService()
    return EventSourceResponse(agent_service.process_chat(data), ping=20)


@router.post("/chat")
async def chat(
        data: SChatRequest,
):
    agent_service = AgentService()
    return await agent_service.process_chat(data)


class ChatMessage(TypedDict):
    """Format of messages sent to the browser."""

    role: Literal['user', 'model']
    timestamp: str
    content: str


def to_chat_message(m: ModelMessage) -> ChatMessage:
    first_part = m.parts[0]
    if isinstance(m, ModelRequest):
        if isinstance(first_part, UserPromptPart):
            assert isinstance(first_part.content, str)
            return {
                'role': 'user',
                'timestamp': first_part.timestamp.isoformat(),
                'content': first_part.content,
            }
    elif isinstance(m, ModelResponse):
        if isinstance(first_part, TextPart):
            return {
                'role': 'model',
                'timestamp': m.timestamp.isoformat(),
                'content': first_part.content,
            }
    raise UnexpectedModelBehavior(f'Unexpected message type for chat app: {m}')


agent = Agent("mistral:mistral-small-latest")
messages = []


@router.post('/chat/test')
async def post_chat(
        prompt: str
) -> StreamingResponse:
    async def stream_messages():
        """Streams new line delimited JSON `Message`s to the client."""
        # stream the user prompt so that can be displayed straight away
        yield (
                json.dumps(
                    {
                        'role': 'user',
                        'timestamp': datetime.now(tz=timezone.utc).isoformat(),
                        'content': prompt,
                    }
                ).encode('utf-8')
                + b'\n'
        )
        # get the chat history so far to pass as context to the agent

        # run the agent with the user prompt and the chat history
        retry_count = 0
        max_retries = 3
        while True:
            try:
                async with agent.run_stream(prompt, message_history=messages) as result:
                    async for text in result.stream(debounce_by=0.01):
                        # text here is a `str` and the frontend wants
                        # JSON encoded ModelResponse, so we create one
                        m = ModelResponse(parts=[TextPart(text)], timestamp=result.timestamp())
                        yield json.dumps(to_chat_message(m)).encode('utf-8') + b'\n'
                break  # Success, exit the retry loop
            except RuntimeError as e:
                print(e)
                if "Cannot send a request, as the client has been closed." in str(e) and retry_count < max_retries:
                    retry_count += 1
                    print(f"Client closed error detected. Retrying attempt {retry_count}/{max_retries}...")
                    await asyncio.sleep(1)  # Wait a bit before retrying
                else:
                    # Either different error or max retries reached
                    print(f"Error after {retry_count} retries: {e}")
                    break
        # add new messages (e.g. the user prompt and the agent response in this case) to the database
        # await database.add_messages(result.new_messages_json())

    return StreamingResponse(stream_messages(), media_type='text/plain')
