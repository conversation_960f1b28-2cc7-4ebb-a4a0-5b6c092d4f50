"""
Knowledge Base Search Tool for AI Agents.
Provides RAG (Retrieval Augmented Generation) capabilities to agents.
"""
from pydantic_ai.tools import RunContext

from modules.knowledge_base.services.knowledge_base_service import KnowledgeBaseService
from schemas.agent import SAgentDeps
from schemas.assistant import <PERSON><PERSON><PERSON><PERSON>quest
from utils.logger import logger


async def search_knowledge_base(
        ctx: RunContext[SAgentDeps[SChatRequest]],
        query: str,
        limit: int = 5,
        search_method: str = "hybrid"
) -> str:
    """
    Search the knowledge base for relevant information using RAG.
    
    This tool allows you to search through uploaded documents when the automatic search results
    are insufficient or you need different search parameters. Use this when:
    - The automatic search didn't find what you need
    - You need more results (higher limit)
    - You want to try a different search method
    - You need to search for more specific terms
    
    Args:
        query: Search query to find relevant documents/chunks
        limit: Maximum number of results to return (default: 5, max: 20)
        search_method: Search method - "hybrid" (recommended), "semantic", or "bm25"
    
    Returns:
        Formatted search results with document chunks and context
    """
    try:
        # Check if we already searched this exact query with same parameters
        search_key = f"{query}:{search_method}:{limit}"

        if search_key in ctx.deps.data.search_cache:
            logger.info(f"Reusing cached results for: {query}")
            cached_results = ctx.deps.data.search_cache[search_key]
            search_source = "cached"
        else:
            logger.info(f"Performing new knowledge base search: {query}")

            # Initialize knowledge base service
            kb_service = KnowledgeBaseService()

            # Get user context for access control
            user_id = None
            enterprise_id = None
            if ctx.deps.data.personal_context:
                user_id = str(ctx.deps.data.personal_context.id)

            # Perform search
            cached_results = await kb_service.search_documents(
                query=query,
                user_id=user_id,
                enterprise_id=enterprise_id,
                limit=min(limit, 20),  # Cap at 20 to prevent excessive results
                include_chunks=True,
                search_method=search_method
            )

            # Cache the results
            ctx.deps.data.search_cache[search_key] = cached_results
            search_source = "new"

        # Format results for agent consumption
        if not cached_results.get('results'):
            return f"📚 **Knowledge Base Tool Search** ({search_source})\nQuery: '{query}'\nMethod: {search_method}\nNo relevant documents found."

        formatted_results = [
            f"📚 **Knowledge Base Tool Search Results** ({search_source})",
            f"Query: '{query}'",
            f"Method: {search_method}, Limit: {limit}",
            f"Found: {len(cached_results['results'])} relevant document(s)",
            ""
        ]

        for i, result in enumerate(cached_results['results'], 1):
            chunk_content = result.get('chunk_content', 'No content available')
            document_title = result.get('document_title', 'Untitled Document')
            relevance_score = result.get('relevance_score', 0.0)
            page_number = result.get('page_number')
            document_id = result.get('document_id', 'unknown')
            
            # Create citation info
            citation_parts = [f"📄 {document_title}"]
            if page_number:
                citation_parts.append(f"📖 Page {page_number}")
            citation = " | ".join(citation_parts)

            formatted_results.extend([
                f"**Result {i}** (Relevance: {relevance_score:.2f})",
                f"Source: {citation}",
                f"Content: {chunk_content}",
                f"Document ID: {document_id}",
                ""
            ])

        search_summary = "\n".join(filter(None, formatted_results))  # Remove empty strings
        logger.info(f"Knowledge base tool search completed: {len(cached_results['results'])} results ({search_source})")

        return search_summary

    except Exception as e:
        error_msg = f"Error searching knowledge base: {str(e)}"
        logger.error(error_msg)
        return f"⚠️ {error_msg}"


async def get_document_chunks(
        ctx: RunContext[SAgentDeps[SChatRequest]],
        document_id: str
) -> str:
    """
    Get all chunks from a specific document.
    
    Use this when you need to access the full content of a specific document
    that was referenced in search results.
    
    Args:
        document_id: ID of the document to retrieve chunks from
    
    Returns:
        Formatted document chunks with metadata
    """
    try:
        logger.info(f"Getting chunks for document: {document_id}")

        # Initialize knowledge base service
        kb_service = KnowledgeBaseService()

        # Get user context for access control
        user_id = None
        enterprise_id = None
        if ctx.deps.data.personal_context:
            user_id = str(ctx.deps.data.personal_context.id)

        # Get document chunks
        from uuid import UUID
        chunks = await kb_service.get_document_chunks(
            document_id=UUID(document_id),
            user_id=user_id,
            enterprise_id=enterprise_id
        )

        if not chunks:
            return f"Document not found or access denied for document ID: {document_id}"

        formatted_chunks = []
        formatted_chunks.append(f"📄 **Document Chunks** (Total: {len(chunks)})\n")

        for chunk in chunks:
            chunk_index = chunk.get('chunk_index', 0)
            content = chunk.get('content', 'No content available')
            page_number = chunk.get('page_number')
            context_summary = chunk.get('context_summary')

            formatted_chunks.append(f"**Chunk {chunk_index + 1}**")
            if page_number:
                formatted_chunks.append(f"📖 **Page**: {page_number}")
            if context_summary:
                formatted_chunks.append(f"📝 **Context**: {context_summary}")
            formatted_chunks.append(f"**Content**:\n{content}\n")
            formatted_chunks.append("---")

        result = "\n".join(formatted_chunks)
        logger.info(f"Retrieved {len(chunks)} chunks for document {document_id}")

        return result

    except Exception as e:
        error_msg = f"Error getting document chunks: {str(e)}"
        logger.error(error_msg)
        return f"⚠️ {error_msg}"
