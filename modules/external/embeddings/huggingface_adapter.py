import asyncio
from typing import List, Optional, Dict, Any

try:
    import torch
    from transformers import <PERSON>Tokenizer, AutoModel
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

from .abc_adapter import EmbeddingAdapter
from .types import Embedding<PERSON><PERSON>ult, EmbeddingConfig


class HuggingFaceEmbeddingAdapter(EmbeddingAdapter):
    """HuggingFace embedding adapter implementation."""
    
    def __init__(self, config: EmbeddingConfig):
        super().__init__(config)
        if not TORCH_AVAILABLE:
            raise ImportError("PyTorch and transformers are required for HuggingFace adapter. Install with: pip install torch transformers")
        
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.tokenizer = None
        self.model = None
        self._dimensions = None
        self._max_batch_size = 32  # Conservative default
        self._initialized = False
    
    async def _initialize(self):
        """Lazy initialization of model and tokenizer."""
        if self._initialized:
            return
        
        loop = asyncio.get_event_loop()
        
        # Load tokenizer and model in thread pool to avoid blocking
        self.tokenizer, self.model = await loop.run_in_executor(
            None, self._load_model_and_tokenizer
        )
        
        # Get dimensions from model
        self._dimensions = self.model.config.hidden_size
        self._initialized = True
    
    def _load_model_and_tokenizer(self):
        """Load model and tokenizer (runs in thread pool)."""
        tokenizer = AutoTokenizer.from_pretrained(self.config.model)
        model = AutoModel.from_pretrained(self.config.model)
        model.to(self.device)
        model.eval()
        return tokenizer, model
    
    @property
    def max_batch_size(self) -> int:
        return self._max_batch_size
    
    @property
    def dimensions(self) -> int:
        if self._dimensions is None:
            # Default for sentence transformers
            return 384
        return self._dimensions
    
    def _mean_pooling(self, model_output, attention_mask):
        """Apply mean pooling to get sentence embeddings."""
        token_embeddings = model_output[0]
        input_mask_expanded = attention_mask.unsqueeze(-1).expand(token_embeddings.size()).float()
        return torch.sum(token_embeddings * input_mask_expanded, 1) / torch.clamp(input_mask_expanded.sum(1), min=1e-9)
    
    def _embed_batch_sync(self, texts: List[str]) -> List[List[float]]:
        """Synchronous embedding computation."""
        encoded_input = self.tokenizer(
            texts, 
            padding=True, 
            truncation=True, 
            return_tensors='pt',
            max_length=512
        )
        
        # Move to device
        encoded_input = {k: v.to(self.device) for k, v in encoded_input.items()}
        
        with torch.no_grad():
            model_output = self.model(**encoded_input)
            sentence_embeddings = self._mean_pooling(model_output, encoded_input['attention_mask'])
            
            # Normalize embeddings
            sentence_embeddings = torch.nn.functional.normalize(sentence_embeddings, p=2, dim=1)
            
            return sentence_embeddings.cpu().numpy().tolist()
    
    async def _embed_batch(self, texts: List[str], **kwargs) -> EmbeddingResult:
        """Embed a batch of texts using HuggingFace model."""
        await self._initialize()
        
        try:
            loop = asyncio.get_event_loop()
            embeddings = await loop.run_in_executor(
                None, 
                self._embed_batch_sync, texts
            )
            
            return EmbeddingResult(
                embeddings=embeddings,
                model=self.config.model,
                dimensions=len(embeddings[0]) if embeddings else self.dimensions,
                usage={"prompt_tokens": len(texts), "total_tokens": len(texts)}
            )
        except Exception as e:
            raise Exception(f"HuggingFace embedding failed: {str(e)}")
    
    async def embed_texts(self, texts: List[str], **kwargs) -> EmbeddingResult:
        """Embed multiple texts."""
        await self._initialize()
        
        if len(texts) <= self.max_batch_size:
            return await self._embed_batch(texts, **kwargs)
        
        # Handle batching for large text lists
        all_embeddings = []
        total_tokens = 0
        
        for i in range(0, len(texts), self.max_batch_size):
            batch = texts[i:i + self.max_batch_size]
            result = await self._embed_batch(batch, **kwargs)
            all_embeddings.extend(result.embeddings)
            total_tokens += len(batch)
        
        return EmbeddingResult(
            embeddings=all_embeddings,
            model=self.config.model,
            dimensions=self.dimensions,
            usage={"prompt_tokens": total_tokens, "total_tokens": total_tokens}
        )
    
    async def embed_query(self, query: str, **kwargs) -> List[float]:
        """Embed a single query."""
        result = await self._embed_batch([query], **kwargs)
        return result.embeddings[0]