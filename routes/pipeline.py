
from fastapi import APIRouter, BackgroundTasks
from minio import Minio
import tempfile
import os
import zipfile
from io import BytesIO

from config import CONFIG
from modules.pipelines.e_phy import load_e_phy_data, DataEnhancer
from schemas import BaseModel
from utils.logger import logger

router = APIRouter(prefix="/pipelines", tags=["Pipelines"])


class EPhyDataRequest(BaseModel):
    file_name: str = None
    use_local_files: bool = True


@router.post("/e-phy")
async def load_ephy_data(data: EPhyDataRequest, background_tasks: BackgroundTasks):
    if data.file_name:
        # Handle file from MinIO
        client = Minio(
            CONFIG.MINIO_ENDPOINT,
            access_key=CONFIG.MINIO_ACCESS_KEY_ID,
            secret_key=CONFIG.MINIO_SECRET_ACCESS_KEY,
        )

        response = None
        temp_dir = None
        try:
            # Get data from MinIO
            response = client.get_object(CONFIG.MINIO_EPHY_BUCKET, data.file_name)
            raw_data = response.data

            # Create temporary directory for extracted files
            temp_dir = tempfile.mkdtemp(prefix="ephy_data_")
            
            # Check if the file is a ZIP archive
            if data.file_name.lower().endswith('.zip'):
                # Extract ZIP file
                with zipfile.ZipFile(BytesIO(raw_data), 'r') as zip_ref:
                    zip_ref.extractall(temp_dir)
                logger.info(f"Extracted ZIP file to {temp_dir}")
            else:
                # Single file - save it directly
                file_path = os.path.join(temp_dir, data.file_name)
                with open(file_path, 'wb') as f:
                    f.write(raw_data)
                logger.info(f"Saved single file to {file_path}")

            # Load data using the temporary directory
            background_tasks.add_task(load_e_phy_data_from_files, temp_dir)
            return {"message": f"Loading e-Phy data from uploaded files in the background", "temp_dir": temp_dir}

        except Exception as e:
            logger.error(f"Error processing uploaded file: {e}")
            # Clean up temp directory if created
            if temp_dir and os.path.exists(temp_dir):
                import shutil
                shutil.rmtree(temp_dir)
            raise

        finally:
            if response:
                response.close()
                response.release_conn()
    else:
        # Use default local files
        background_tasks.add_task(load_e_phy_data)
        return {"message": "Loading e-Phy data from local files in the background"}


async def load_e_phy_data_from_files(data_dir: str):
    """Load e-Phy data from specified directory and clean up afterwards"""
    try:
        # Import here to avoid circular imports
        from models.dbm import DatabaseManager
        from modules.pipelines.e_phy.data_loader import DataLoader

        # Get database session
        db_manager = DatabaseManager()
        # await db_manager.drop_db()
        # await db_manager.init_db()
        #
        # async with db_manager.session_scope() as session:
        #     # Load data from the specified directory
        #     loader = DataLoader(session, data_dir)
        #     await loader.load_all_data()
        #     logger.info(f"Successfully loaded e-Phy data from {data_dir}")

        async with db_manager.session_scope() as session:
            # # # Enhance data
            try:
                enhancer = DataEnhancer(session)
                await enhancer.enhance_all_data()
            except Exception as e:
                logger.error(f"Error enhancing data: {e}")

            logger.info("E-Phy data enhancing is already up to date")
        logger.info("E-Phy -------------------- Done")

    except Exception as e:
        logger.error(f"Error loading e-Phy data from {data_dir}: {e}")
        raise
    finally:
        # Clean up temporary directory
        if os.path.exists(data_dir):
            import shutil
            shutil.rmtree(data_dir)
            logger.info(f"Cleaned up temporary directory {data_dir}")



