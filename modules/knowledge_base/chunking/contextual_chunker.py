"""
Contextual chunker implementing Anthropic's contextual retrieval method.
Uses Pydantic-AI with GPT to generate contextual descriptions for better retrieval accuracy.
"""
import re
from dataclasses import dataclass
from typing import List, Dict, Any, Optional

from pydantic import BaseModel
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel

from utils.logger import logger


class ContextualDescription(BaseModel):
    """Pydantic model for contextual description response."""
    context_summary: str
    chunk_type: str
    topics: List[str]
    importance_score: float


@dataclass
class ChunkData:
    """Data structure for a text chunk with metadata."""
    content: str
    chunk_index: int
    start_position: Optional[int] = None
    end_position: Optional[int] = None
    page_number: Optional[int] = None
    context_summary: Optional[str] = None
    chunk_type: str = "paragraph"
    importance_score: float = 0.5


class ContextualChunker:
    """
    Advanced chunker that creates contextually-aware chunks using <PERSON><PERSON><PERSON>'s method.
    Uses Pydantic-AI with GPT to generate contextual descriptions for each chunk.
    """

    def __init__(
            self,
            chunk_size: int = 400,
            overlap_size: int = 50,
            max_chunk_size: int = 800,
            min_chunk_size: int = 100
    ):
        self.chunk_size = chunk_size
        self.overlap_size = overlap_size
        self.max_chunk_size = max_chunk_size
        self.min_chunk_size = min_chunk_size

        # Initialize Pydantic-AI agent for contextual descriptions
        self.model = OpenAIModel('gpt-4o-mini')
        self.context_agent = Agent(
            model=self.model,
            result_type=ContextualDescription,
            system_prompt="""You are an expert agricultural knowledge assistant that provides concise contextual summaries for document chunks.

Your task is to analyze a chunk of text within the context of a larger agricultural document and provide:
1. A brief contextual summary (50-100 tokens) that situates this chunk within the document
2. The content type (header, paragraph, list, table, instructions, etc.)
3. Key agricultural topics mentioned (up to 5)
4. An importance score (0.0-1.0) based on agricultural relevance

Focus on agricultural context: crops, pests, diseases, farming practices, regulations, equipment, etc.

Be concise and specific. The context summary should help retrieve this chunk for relevant agricultural queries."""
        )

    async def chunk_text(
            self,
            text: str,
            document_context: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Create contextual chunks from text using Anthropic's method.
        
        Args:
            text: Input text to chunk
            document_context: Context about the document (title, type, etc.)
            
        Returns:
            List of chunk dictionaries with AI-generated contextual information
        """
        if not text or len(text.strip()) < self.min_chunk_size:
            return []

        try:
            # Step 1: Create base chunks using semantic boundaries
            base_chunks = self._create_semantic_chunks(text)

            # Step 2: Extract page information if available
            base_chunks = self._extract_page_info(base_chunks, text)

            # Step 3: Generate AI-powered contextual summaries
            contextual_chunks = await self._generate_ai_contextual_summaries(
                base_chunks, text, document_context
            )

            return contextual_chunks

        except Exception as e:
            logger.error(f"Error in contextual chunking: {e}")
            # Fallback to basic chunking
            return self._fallback_chunking(text)

    def _create_semantic_chunks(self, text: str) -> List[ChunkData]:
        """Create chunks based on semantic boundaries (paragraphs, sentences)."""
        chunks = []

        # First, try to split by double newlines (paragraphs)
        paragraphs = re.split(r'\n\s*\n', text)

        current_chunk = ""
        chunk_index = 0
        current_position = 0

        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue

            # If adding this paragraph would exceed chunk size, finalize current chunk
            if current_chunk and len(current_chunk) + len(paragraph) > self.chunk_size:
                if len(current_chunk) >= self.min_chunk_size:
                    chunks.append(ChunkData(
                        content=current_chunk.strip(),
                        chunk_index=chunk_index,
                        start_position=current_position - len(current_chunk),
                        end_position=current_position
                    ))
                    chunk_index += 1

                # Start new chunk with overlap
                overlap_text = self._get_overlap_text(current_chunk)
                current_chunk = overlap_text + paragraph
                current_position += len(paragraph)
            else:
                # Add paragraph to current chunk
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph
                current_position += len(paragraph)

        # Add final chunk
        if current_chunk.strip() and len(current_chunk) >= self.min_chunk_size:
            chunks.append(ChunkData(
                content=current_chunk.strip(),
                chunk_index=chunk_index,
                start_position=current_position - len(current_chunk),
                end_position=current_position
            ))

        # If no good paragraph splits, fall back to sentence-based chunking
        if not chunks or len(chunks) == 1 and len(text) > self.max_chunk_size:
            return self._create_sentence_chunks(text)

        return chunks

    def _create_sentence_chunks(self, text: str) -> List[ChunkData]:
        """Create chunks based on sentence boundaries."""
        # Split by sentences
        sentences = re.split(r'(?<=[.!?])\s+', text)

        chunks = []
        current_chunk = ""
        chunk_index = 0
        current_position = 0

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            # Check if adding sentence would exceed chunk size
            if current_chunk and len(current_chunk) + len(sentence) > self.chunk_size:
                if len(current_chunk) >= self.min_chunk_size:
                    chunks.append(ChunkData(
                        content=current_chunk.strip(),
                        chunk_index=chunk_index,
                        start_position=current_position - len(current_chunk),
                        end_position=current_position
                    ))
                    chunk_index += 1

                # Start new chunk with overlap
                overlap_text = self._get_overlap_text(current_chunk)
                current_chunk = overlap_text + sentence
            else:
                # Add sentence to current chunk
                if current_chunk:
                    current_chunk += " " + sentence
                else:
                    current_chunk = sentence

            current_position += len(sentence)

        # Add final chunk
        if current_chunk.strip() and len(current_chunk) >= self.min_chunk_size:
            chunks.append(ChunkData(
                content=current_chunk.strip(),
                chunk_index=chunk_index,
                start_position=current_position - len(current_chunk),
                end_position=current_position
            ))

        return chunks

    def _get_overlap_text(self, text: str) -> str:
        """Get overlap text from the end of previous chunk."""
        if len(text) <= self.overlap_size:
            return ""

        # Try to get overlap at sentence boundary
        sentences = re.split(r'(?<=[.!?])\s+', text)
        if len(sentences) > 1:
            # Take last sentence if it's within overlap size
            last_sentence = sentences[-1]
            if len(last_sentence) <= self.overlap_size:
                return last_sentence + " "

        # Fallback to character-based overlap
        overlap_text = text[-self.overlap_size:]
        # Try to start at word boundary
        space_index = overlap_text.find(' ')
        if space_index > 0:
            overlap_text = overlap_text[space_index + 1:]

        return overlap_text + " "

    def _extract_page_info(self, chunks: List[ChunkData], full_text: str) -> List[ChunkData]:
        """Extract page number information from chunks if available."""
        # Look for page markers in text
        page_pattern = r'(?:^|\n)\s*(?:---\s*)?[Pp]age\s+(\d+)(?:\s*---)?'
        page_matches = list(re.finditer(page_pattern, full_text))

        if not page_matches:
            return chunks

        # Assign page numbers to chunks based on their position
        for chunk in chunks:
            if chunk.start_position is not None:
                # Find which page this chunk belongs to
                chunk_page = 1  # Default to page 1

                for match in page_matches:
                    if match.start() <= chunk.start_position:
                        chunk_page = int(match.group(1))
                    else:
                        break

                chunk.page_number = chunk_page

        return chunks

    async def _generate_ai_contextual_summaries(
            self,
            chunks: List[ChunkData],
            full_text: str,
            document_context: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Generate contextual summaries using Pydantic-AI following Anthropic's method.
        """
        contextual_chunks = []

        # Prepare document context for AI
        doc_context_str = self._prepare_document_context(document_context, full_text)

        for chunk in chunks:
            try:
                # Create the contextual prompt following Anthropic's method
                prompt = f"""<document>
{full_text[:2000]}...  # Truncated for context
</document>

Document context: {doc_context_str}

<chunk>
{chunk.content}
</chunk>

Please provide a contextual summary for this chunk that will help with agricultural information retrieval."""

                # Get AI-generated contextual description
                result = await self.context_agent.run(prompt)

                # Create contextual content by prepending context to original content
                contextual_content = f"{result.data.context_summary}\n\n{chunk.content}"

                # Convert to dictionary format
                chunk_dict = {
                    'content': chunk.content,  # Original content
                    'contextual_content': contextual_content,  # Content with prepended context for embedding
                    'chunk_index': chunk.chunk_index,
                    'start_position': chunk.start_position,
                    'end_position': chunk.end_position,
                    'page_number': chunk.page_number,
                    'context_summary': result.data.context_summary,
                    'chunk_type': result.data.chunk_type,
                    'importance_score': result.data.importance_score,
                    'detected_topics': result.data.topics
                }

                contextual_chunks.append(chunk_dict)

            except Exception as e:
                logger.warning(f"Failed to generate AI context for chunk {chunk.chunk_index}: {e}")
                # Fallback to basic context
                basic_context = self._create_basic_context(chunk, full_text, document_context)
                contextual_content = f"{basic_context}\n\n{chunk.content}"

                chunk_dict = {
                    'content': chunk.content,  # Original content
                    'contextual_content': contextual_content,  # Content with prepended context for embedding
                    'chunk_index': chunk.chunk_index,
                    'start_position': chunk.start_position,
                    'end_position': chunk.end_position,
                    'page_number': chunk.page_number,
                    'context_summary': basic_context,
                    'chunk_type': self._classify_chunk_type(chunk.content),
                    'importance_score': 0.5,
                    'detected_topics': self._extract_basic_topics(chunk.content)
                }
                contextual_chunks.append(chunk_dict)

        return contextual_chunks

    def _prepare_document_context(
            self,
            document_context: Optional[Dict[str, Any]],
            full_text: str
    ) -> str:
        """Prepare document context string for AI processing."""
        context_parts = []

        if document_context:
            if document_context.get('title'):
                context_parts.append(f"Title: {document_context['title']}")
            if document_context.get('document_type'):
                context_parts.append(f"Type: {document_context['document_type']}")
            if document_context.get('file_path'):
                context_parts.append(f"Source: {document_context['file_path']}")

        # Add brief document summary
        first_paragraph = full_text[:500].strip()
        if first_paragraph:
            context_parts.append(f"Document excerpt: {first_paragraph}...")

        return " | ".join(context_parts) if context_parts else "Agricultural document"

    def _create_basic_context(
            self,
            chunk: ChunkData,
            full_text: str,
            document_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create basic contextual information for a chunk (fallback method)."""
        context_parts = []

        # Add document context
        if document_context:
            if document_context.get('title'):
                context_parts.append(f"Document: {document_context['title']}")
            if document_context.get('document_type'):
                context_parts.append(f"Type: {document_context['document_type']}")

        # Add position context
        if chunk.page_number:
            context_parts.append(f"Page {chunk.page_number}")

        # Add content type context
        chunk_type = self._classify_chunk_type(chunk.content)
        if chunk_type != 'paragraph':
            context_parts.append(f"Content type: {chunk_type}")

        # Add topic context
        topics = self._extract_basic_topics(chunk.content)
        if topics:
            context_parts.append(f"Topics: {', '.join(topics[:3])}")

        return " | ".join(context_parts)

    def _classify_chunk_type(self, content: str) -> str:
        """Classify the type of content in a chunk."""
        content_lower = content.lower().strip()

        # Header patterns
        if re.match(r'^[A-Z][^.!?]*$', content.strip()) and len(content) < 100:
            return 'header'

        # List patterns
        if re.search(r'^\s*[\-\*\d]+[\.\)]\s+', content, re.MULTILINE):
            return 'list'

        # Table patterns
        if '|' in content or '\t' in content:
            return 'table'

        # Instruction patterns
        if re.search(r'\b(step|first|second|then|next|finally)\b', content_lower):
            return 'instructions'

        return 'paragraph'

    def _extract_basic_topics(self, content: str) -> List[str]:
        """Extract key agricultural topics from chunk content (fallback method)."""
        content_lower = content.lower()

        # Agricultural keywords
        agricultural_topics = {
            'pest_control': ['pest', 'insect', 'control', 'spray'],
            'disease': ['disease', 'fungus', 'pathogen', 'infection'],
            'fertilizer': ['fertilizer', 'nutrient', 'nitrogen', 'phosphorus'],
            'irrigation': ['water', 'irrigation', 'moisture'],
            'soil': ['soil', 'pH', 'cultivation'],
            'harvest': ['harvest', 'yield', 'picking'],
            'planting': ['plant', 'seed', 'sowing'],
            'weather': ['weather', 'temperature', 'rain']
        }

        found_topics = []
        for topic, keywords in agricultural_topics.items():
            if any(keyword in content_lower for keyword in keywords):
                found_topics.append(topic)

        return found_topics

    def _fallback_chunking(self, text: str) -> List[Dict[str, Any]]:
        """Fallback to simple character-based chunking if semantic chunking fails."""
        chunks = []
        chunk_index = 0

        for i in range(0, len(text), self.chunk_size - self.overlap_size):
            chunk_text = text[i:i + self.chunk_size]

            if len(chunk_text.strip()) >= self.min_chunk_size:
                basic_context = f"Text chunk {chunk_index + 1}"
                contextual_content = f"{basic_context}\n\n{chunk_text.strip()}"

                chunks.append({
                    'content': chunk_text.strip(),  # Original content
                    'contextual_content': contextual_content,  # Content with prepended context for embedding
                    'chunk_index': chunk_index,
                    'start_position': i,
                    'end_position': i + len(chunk_text),
                    'page_number': None,
                    'context_summary': basic_context,
                    'chunk_type': 'paragraph',
                    'importance_score': 0.5,
                    'detected_topics': []
                })
                chunk_index += 1

        return chunks
