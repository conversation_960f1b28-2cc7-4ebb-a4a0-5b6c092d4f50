import async<PERSON>
from typing import List, Optional
from openai import Async<PERSON>penA<PERSON>
from .abc_adapter import EmbeddingAdapter
from .types import Embedding<PERSON><PERSON>ult, EmbeddingConfig


class OpenAIEmbeddingAdapter(EmbeddingAdapter):
    """OpenAI embedding adapter implementation."""
    
    def __init__(self, config: EmbeddingConfig):
        super().__init__(config)
        self.client = AsyncOpenAI(api_key=config.api_key)
        self._dimensions = self._get_model_dimensions()
    
    def _get_model_dimensions(self) -> int:
        """Get dimensions for the model."""
        model_dimensions = {
            "text-embedding-3-small": 1536,
            "text-embedding-3-large": 3072,
            "text-embedding-ada-002": 1536,
        }
        return model_dimensions.get(self.model, 1536)
    
    @property
    def max_batch_size(self) -> int:
        return 100
    
    @property
    def dimensions(self) -> int:
        return self._dimensions
    
    async def _embed_batch(self, texts: List[str], **kwargs) -> EmbeddingResult:
        """Embed a batch of texts using OpenAI API."""
        try:
            response = await self.client.embeddings.create(
                input=texts,
                model=self.model,
                **kwargs
            )
            
            embeddings = [data.embedding for data in response.data]
            usage = {
                "prompt_tokens": response.usage.prompt_tokens,
                "total_tokens": response.usage.total_tokens
            } if response.usage else None
            
            return EmbeddingResult(
                embeddings=embeddings,
                model=self.model,
                dimensions=len(embeddings[0]) if embeddings else self.dimensions,
                usage=usage
            )
        except Exception as e:
            raise Exception(f"OpenAI embedding failed: {str(e)}")
    
    async def embed_texts(self, texts: List[str], **kwargs) -> EmbeddingResult:
        """Embed multiple texts."""
        if len(texts) <= self.max_batch_size:
            return await self._embed_batch(texts, **kwargs)
        
        # Handle batching for large text lists
        all_embeddings = []
        total_usage = {"prompt_tokens": 0, "total_tokens": 0}
        
        for i in range(0, len(texts), self.max_batch_size):
            batch = texts[i:i + self.max_batch_size]
            result = await self._embed_batch(batch, **kwargs)
            all_embeddings.extend(result.embeddings)
            
            if result.usage:
                total_usage["prompt_tokens"] += result.usage.get("prompt_tokens", 0)
                total_usage["total_tokens"] += result.usage.get("total_tokens", 0)
        
        return EmbeddingResult(
            embeddings=all_embeddings,
            model=self.model,
            dimensions=self.dimensions,
            usage=total_usage if total_usage["total_tokens"] > 0 else None
        )
    
    async def embed_query(self, query: str, **kwargs) -> List[float]:
        """Embed a single query."""
        result = await self._embed_batch([query], **kwargs)
        return result.embeddings[0]