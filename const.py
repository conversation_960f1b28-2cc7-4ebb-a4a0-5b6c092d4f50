from enum import Enum


class EChatRole(Enum):
    system = 'system'
    user = 'user'
    assistant = 'assistant'


class EAgent(Enum):
    selective = 'selective'
    general = 'general'
    financial = 'financial'
    soil = 'soil'
    farm = 'farm'
    animal = 'animal'


class EAIStyle(Enum):
    deep = 'deep'
    shallow = 'shallow'
    advanced = 'advanced'


class EConnectionState(Enum):
    connecting = "connecting"
    connected = "connected"
    processing = "processing"
    responding = "responding"
    completed = "completed"
    error = "error"


class EChatEventType(Enum):
    error = "error"
    conversation = "conversation"
    connection = "connection"

    markdown = "markdown"
    metric = "metric"
    chart = "chart"
    table = "table"


# Enums for common parameter values
class SafeSearchLevel(str, Enum):
    OFF = "off"
    MODERATE = "moderate"
    STRICT = "strict"


class FreshnessOption(str, Enum):
    DAY = "day"
    WEEK = "week"
    MONTH = "month"
    YEAR = "year"
