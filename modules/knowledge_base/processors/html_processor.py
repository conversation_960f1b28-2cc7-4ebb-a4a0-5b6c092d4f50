"""
HTML processor for web documents and HTML files.
Extracts text content while preserving structure and removing HTML tags.
"""
from typing import List, Dict, Any, Optional
from uuid import UUID
import re

try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False
    BeautifulSoup = None

from modules.knowledge_base.processors.base_processor import BaseDocumentProcessor, ProcessingResult
from utils.logger import logger


class HTMLProcessor(BaseDocumentProcessor):
    """Processor for HTML documents."""
    
    def __init__(self):
        super().__init__()
        self.supported_extensions = {'.html', '.htm', '.xhtml'}
        
        if not BS4_AVAILABLE:
            logger.warning("BeautifulSoup not available. HTML processing will be limited.")
    
    async def process(self, file_content: bytes, document_id: UUID) -> ProcessingResult:
        """
        Process HTML file and extract text content.
        
        Args:
            file_content: HTML file content as bytes
            document_id: Document ID for reference
            
        Returns:
            ProcessingResult with extracted text and metadata
        """
        try:
            # Decode HTML content
            try:
                html_content = file_content.decode('utf-8')
            except UnicodeDecodeError:
                html_content = file_content.decode('latin-1', errors='replace')
            
            if BS4_AVAILABLE:
                # Use BeautifulSoup for better HTML parsing
                extracted_text = self._extract_with_beautifulsoup(html_content)
            else:
                # Fallback to regex-based extraction
                extracted_text = self._extract_with_regex(html_content)
            
            if not extracted_text.strip():
                return ProcessingResult(
                    success=False,
                    error="No readable text content found in HTML"
                )
            
            # Extract metadata
            title = self._extract_html_title(html_content)
            language = self._detect_language(extracted_text)
            agricultural_metadata = await self._extract_agricultural_metadata(extracted_text)
            
            # Estimate pages based on content length
            estimated_pages = max(1, len(extracted_text) // 2000)
            
            return ProcessingResult(
                success=True,
                content=extracted_text,
                title=title,
                language=language,
                total_pages=estimated_pages,
                crop_categories=agricultural_metadata.get('crop_categories', []),
                topics=agricultural_metadata.get('topics', []),
                geographic_scope=agricultural_metadata.get('geographic_scope', []),
                seasonal_relevance=agricultural_metadata.get('seasonal_relevance', []),
                metadata={
                    'processor': 'HTMLProcessor',
                    'extraction_method': 'BeautifulSoup' if BS4_AVAILABLE else 'Regex',
                    'original_size_bytes': len(file_content),
                    'extracted_size_chars': len(extracted_text)
                }
            )
            
        except Exception as e:
            logger.error(f"Error processing HTML document {document_id}: {e}")
            return ProcessingResult(
                success=False,
                error=f"HTML processing error: {str(e)}"
            )
    
    def _extract_with_beautifulsoup(self, html_content: str) -> str:
        """Extract text using BeautifulSoup for better parsing."""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Remove script and style elements
        for script in soup(["script", "style", "nav", "footer", "header"]):
            script.decompose()
        
        # Get text with preserved structure
        text_parts = []
        
        # Extract main content areas first
        main_content = soup.find('main') or soup.find('article') or soup.find('div', class_=re.compile('content|main|body'))
        if main_content:
            soup = main_content
        
        # Process different HTML elements with structure preservation
        for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'div', 'span', 'li', 'td', 'th']):
            text = element.get_text(strip=True)
            if text:
                # Add structural indicators for headers
                if element.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                    text_parts.append(f"\n{text}\n")
                elif element.name in ['p', 'div']:
                    text_parts.append(f"{text}\n")
                else:
                    text_parts.append(text + " ")
        
        # If no structured content found, get all text
        if not text_parts:
            text_parts = [soup.get_text(separator=' ', strip=True)]
        
        # Clean and join
        full_text = ''.join(text_parts)
        return self._clean_extracted_text(full_text)
    
    def _extract_with_regex(self, html_content: str) -> str:
        """Fallback text extraction using regex."""
        # Remove script and style content
        html_content = re.sub(r'<script.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
        html_content = re.sub(r'<style.*?</style>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
        
        # Remove HTML tags
        html_content = re.sub(r'<[^>]+>', '', html_content)
        
        # Decode HTML entities
        html_entities = {
            '&amp;': '&',
            '&lt;': '<',
            '&gt;': '>',
            '&quot;': '"',
            '&apos;': "'",
            '&nbsp;': ' ',
            '&copy;': '©',
            '&reg;': '®'
        }
        
        for entity, replacement in html_entities.items():
            html_content = html_content.replace(entity, replacement)
        
        return self._clean_extracted_text(html_content)
    
    def _clean_extracted_text(self, text: str) -> str:
        """Clean extracted text from HTML."""
        if not text:
            return ""
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Split into lines and clean
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            if line and len(line) > 2:  # Skip very short lines
                cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    def _extract_html_title(self, html_content: str) -> Optional[str]:
        """Extract title from HTML content."""
        if BS4_AVAILABLE:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Try title tag first
            title_tag = soup.find('title')
            if title_tag and title_tag.get_text(strip=True):
                return title_tag.get_text(strip=True)
            
            # Try h1 tag
            h1_tag = soup.find('h1')
            if h1_tag and h1_tag.get_text(strip=True):
                return h1_tag.get_text(strip=True)
            
        else:
            # Regex fallback
            title_match = re.search(r'<title[^>]*>(.*?)</title>', html_content, re.IGNORECASE | re.DOTALL)
            if title_match:
                title = re.sub(r'<[^>]+>', '', title_match.group(1)).strip()
                if title:
                    return title
            
            # Try h1
            h1_match = re.search(r'<h1[^>]*>(.*?)</h1>', html_content, re.IGNORECASE | re.DOTALL)
            if h1_match:
                title = re.sub(r'<[^>]+>', '', h1_match.group(1)).strip()
                if title:
                    return title
        
        return None