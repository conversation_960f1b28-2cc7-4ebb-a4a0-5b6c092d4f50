"""
Active Substance Intelligence Tool for E-Phy Agricultural Database.
Provides chemical and biological agent information with safety and regulatory insights.
"""
from dataclasses import dataclass
from typing import List, Optional, Dict, Any

from sqlalchemy import select, and_, or_, func, distinct
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from models.e_phy import ActiveSubstance, Product, ProductSubstance, ProductUse, Crop, Target


@dataclass
class SubstanceVariant:
    """Chemical variant information."""
    variant_name: str
    cas_number: Optional[str]
    chemical_type: str  # "salt", "ester", "acid", "base"
    formulation_types: List[str]
    concentration_range: Optional[str]
    stability_notes: List[str]


@dataclass
class ProductContaining:
    """Product containing the substance."""
    product_id: int
    product_name: str
    concentration: Optional[float]
    concentration_unit: Optional[str]
    formulation_type: Optional[str]
    authorization_status: str
    is_primary_substance: bool
    market_share_estimate: Optional[str]


@dataclass
class UsePattern:
    """Usage pattern analysis."""
    crop_name: str
    target_type: str
    usage_frequency: int
    seasonal_pattern: str
    typical_dose_range: str
    application_timing: List[str]
    effectiveness_rating: str


@dataclass
class ResistanceInformation:
    """Resistance management information."""
    resistance_risk_level: str
    documented_resistance: List[str]
    resistance_mechanisms: List[str]
    cross_resistance_groups: List[str]
    management_strategies: List[str]
    monitoring_protocols: List[str]
    alternative_substances: List[str]


@dataclass
class SafetyProfile:
    """Safety and environmental profile."""
    toxicity_classification: Dict[str, str]
    environmental_fate: Dict[str, str]
    exposure_pathways: List[str]
    protective_measures: List[str]
    restricted_uses: List[str]
    withdrawal_considerations: List[str]


@dataclass
class RegulatoryStatus:
    """Regulatory status information."""
    eu_approval_status: str
    approval_expiry_date: Optional[str]
    review_status: str
    restrictions_summary: List[str]
    regional_variations: Dict[str, str]
    pending_changes: List[str]
    compliance_requirements: List[str]


@dataclass
class ChemicalIntelligence:
    """Chemical intelligence analysis."""
    mode_of_action_group: str
    chemical_family: str
    molecular_target: str
    selectivity_profile: str
    persistence_class: str
    bioaccumulation_potential: str
    degradation_pathways: List[str]


@dataclass
class ActiveSubstanceResponse:
    """Complete active substance intelligence response optimized for AI agent decision-making."""
    # Core substance information
    substance_profile: Dict[str, Any]

    # Practical application data
    current_availability: Dict[str, Any]  # New: Current market availability
    authorized_products: List[ProductContaining]  # New: Only currently authorized products
    withdrawn_products: List[ProductContaining]  # New: Recently withdrawn products

    # Usage intelligence
    use_patterns: List[UsePattern]
    application_guidance: Dict[str, Any]  # New: Practical application advice

    # Safety and compliance
    safety_profile: SafetyProfile
    regulatory_status: RegulatoryStatus
    compliance_alerts: List[str]  # New: Critical compliance issues

    # Chemical and resistance intelligence
    chemical_intelligence: ChemicalIntelligence
    resistance_information: ResistanceInformation

    # Decision support
    suitability_assessment: Dict[str, Any]  # New: Overall suitability rating
    recommendations: Dict[str, List[str]]  # Enhanced: Categorized recommendations
    alternatives: List[Dict[str, Any]]  # New: Alternative substances with context

    # Technical details (optional for detailed analysis)
    variants: List[SubstanceVariant]
    related_substances: List[str]

    # Metadata and quality indicators
    data_quality: Dict[str, Any]  # Enhanced: More detailed quality metrics
    search_metadata: Dict[str, Any]
    analysis_timestamp: str  # New: When analysis was performed


async def analyze_active_substances(
        session: AsyncSession,
        substance_query: str,
        include_variants: bool = True,
        focus_on_authorized: bool = True,  # New: Focus on currently available products
        include_alternatives: bool = True,  # New: Include alternative substances
        risk_assessment_level: str = "standard",  # New: "basic", "standard", "detailed"
        intended_use_context: Optional[Dict[str, str]] = None,  # New: Crop/target context for relevance
        include_withdrawn_analysis: bool = False  # New: Include analysis of withdrawn products
) -> ActiveSubstanceResponse:
    """
    Analyze active substances with enhanced intelligence for AI agricultural decision-making.
    
    Args:
        substance_query: Name, CAS number, or partial match
        include_variants: Include chemical variant analysis
        focus_on_authorized: Prioritize currently authorized products (recommended for AI agents)
        include_alternatives: Find alternative substances for rotation/replacement
        risk_assessment_level: Depth of risk analysis ("basic", "standard", "detailed")
        intended_use_context: {"crop": "wheat", "target": "fungus"} for contextual relevance
        include_withdrawn_analysis: Include recently withdrawn products for transition planning
    
    Returns:
        ActiveSubstanceResponse optimized for AI agent decision-making with:
        - Current market availability assessment
        - Practical application guidance
        - Regulatory compliance alerts
        - Suitability ratings for intended use
        - Alternative substance recommendations
    """

    # Find the substance with enhanced search capabilities
    substance = await _find_substance_with_translation(session, substance_query)

    if not substance:
        return await _create_substance_not_found_response(session, substance_query, include_alternatives)

    # Get all products containing this substance with enhanced filtering
    all_products = await _get_all_products_containing_substance(session, substance.id)
    authorized_products = [p for p in all_products if
                           p.authorization_status == "AUTORISE" and "autorise" in p.authorization_status.lower()]
    withdrawn_products = [p for p in all_products if
                          not (p.authorization_status == "AUTORISE" and "autorise" in p.authorization_status.lower())]

    # Determine which products to focus on based on parameters
    primary_products = authorized_products if focus_on_authorized else all_products

    # Analyze current market availability
    availability_assessment = await _assess_current_availability(
        session, substance, authorized_products, withdrawn_products
    )

    # Analyze usage patterns with context awareness
    use_patterns = await _analyze_usage_patterns_enhanced(
        session, substance.id, intended_use_context, focus_on_authorized
    )

    # Generate application guidance for AI decision-making
    application_guidance = await _generate_application_guidance(
        session, substance, primary_products, use_patterns, intended_use_context
    )

    # Enhanced resistance information based on risk assessment level
    resistance_info = await _generate_resistance_information_enhanced(
        session, substance, primary_products, use_patterns, risk_assessment_level
    )

    # Create comprehensive safety profile
    safety_profile = await _create_safety_profile_enhanced(
        session, substance, primary_products
    )

    # Determine regulatory status with compliance alerts
    regulatory_status, compliance_alerts = await _determine_regulatory_status_enhanced(
        session, substance, authorized_products, withdrawn_products
    )

    # Generate chemical intelligence
    chemical_intelligence = await _generate_chemical_intelligence(
        session, substance, [] if not include_variants else await _parse_substance_variants(session, substance)
    )

    # Assess overall suitability for intended use
    suitability_assessment = await _assess_substance_suitability(
        substance, primary_products, use_patterns, safety_profile,
        regulatory_status, intended_use_context
    )

    # Generate categorized recommendations for AI decision-making
    recommendations = await _generate_enhanced_recommendations(
        substance, resistance_info, safety_profile, regulatory_status,
        suitability_assessment, intended_use_context
    )

    # Find alternative substances if requested
    alternatives = []
    if include_alternatives:
        alternatives = await _find_alternative_substances(
            session, substance, chemical_intelligence, intended_use_context
        )

    # Get variants if requested
    variants = []
    if include_variants:
        variants = await _parse_substance_variants(session, substance)

    # Find related substances
    related_substances = await _find_related_substances(
        session, substance, chemical_intelligence
    )

    # Calculate enhanced data quality metrics
    data_quality = await _calculate_data_quality_enhanced(
        substance, variants, primary_products, use_patterns, safety_profile
    )

    from datetime import datetime

    return ActiveSubstanceResponse(
        substance_profile={
            "primary_name": substance.name,
            "cas_number": substance.cas_number,
            "regulatory_status": substance.authorization_status,
            "normalized_name": substance.normalized_name,
            "total_variants": len(variants),
            "total_products": len(all_products),
            "authorized_products_count": len(authorized_products),
            "withdrawn_products_count": len(withdrawn_products)
        },
        current_availability=availability_assessment,
        authorized_products=authorized_products,
        withdrawn_products=withdrawn_products if include_withdrawn_analysis else [],
        use_patterns=use_patterns,
        application_guidance=application_guidance,
        safety_profile=safety_profile,
        regulatory_status=regulatory_status,
        compliance_alerts=compliance_alerts,
        chemical_intelligence=chemical_intelligence,
        resistance_information=resistance_info,
        suitability_assessment=suitability_assessment,
        recommendations=recommendations,
        alternatives=alternatives,
        variants=variants,
        related_substances=related_substances,
        data_quality=data_quality,
        search_metadata={
            "search_query": substance_query,
            "matched_substance": substance.name,
            "search_confidence": _calculate_search_confidence(substance_query, substance.name),
            "analysis_focus": "authorized" if focus_on_authorized else "all",
            "intended_use_context": intended_use_context
        },
        analysis_timestamp=datetime.now().isoformat()
    )


async def _find_substance_with_translation(
        session: AsyncSession,
        query: str
) -> Optional[ActiveSubstance]:
    """Find substance with English-French translation support."""

    # Common substance name translations
    substance_translations = {
        "glyphosate": "glyphosate",
        "copper": "cuivre",
        "sulfur": "soufre",
        "sulphur": "soufre",
        "iron": "fer",
        "zinc": "zinc",
        "boron": "bore",
        "potassium": "potassium",
        "nitrogen": "azote",
        "phosphorus": "phosphore",
        "2,4-d": "2,4-D",
        "mcpa": "MCPA",
        "dicamba": "dicamba",
        "atrazine": "atrazine",
        "simazine": "simazine"
    }

    # Build search terms
    search_terms = [query.strip()]
    query_lower = query.lower().strip()

    # Add translation if available
    if query_lower in substance_translations:
        search_terms.append(substance_translations[query_lower])

    # Try reverse translation
    for english, french in substance_translations.items():
        if query_lower == french.lower():
            search_terms.append(english)

    # Try CAS number format
    if query.replace("-", "").replace(" ", "").isdigit():
        search_terms.append(query.replace(" ", "").replace("-", ""))

    # Build query with multiple search strategies
    conditions = []

    for term in search_terms:
        conditions.extend([
            ActiveSubstance.name.ilike(f"%{term}%"),
            ActiveSubstance.normalized_name.ilike(f"%{term.lower()}%"),
            ActiveSubstance.cas_number.ilike(f"%{term}%"),
            ActiveSubstance.variants.ilike(f"%{term}%")
        ])

    query_obj = select(ActiveSubstance).where(or_(*conditions))

    result = await session.execute(query_obj)
    substances = result.scalars().all()

    if not substances:
        return None

    # Return best match (exact name match preferred)
    for substance in substances:
        if substance.name.lower() == query_lower:
            return substance

    # Return first result if no exact match
    return substances[0]


async def _parse_substance_variants(
        session: AsyncSession,
        substance: ActiveSubstance
) -> List[SubstanceVariant]:
    """Parse and analyze substance variants."""

    variants = []

    if not substance.variants:
        return variants

    # Split variants by pipe separator
    variant_names = [v.strip() for v in substance.variants.split("|") if v.strip()]

    for variant_name in variant_names[:10]:  # Limit to first 10 variants
        # Determine chemical type
        chemical_type = _classify_chemical_type(variant_name)

        # Extract CAS number if present
        cas_number = _extract_cas_from_variant(variant_name)

        # Get formulation types for this variant
        formulation_types = await _get_formulation_types_for_variant(
            session, substance.id, variant_name
        )

        # Generate stability notes
        stability_notes = _generate_stability_notes(variant_name, chemical_type)

        variant = SubstanceVariant(
            variant_name=variant_name,
            cas_number=cas_number,
            chemical_type=chemical_type,
            formulation_types=formulation_types,
            concentration_range=None,  # Would need additional data
            stability_notes=stability_notes
        )

        variants.append(variant)

    return variants


async def _get_all_products_containing_substance(
        session: AsyncSession,
        substance_id: int
) -> List[ProductContaining]:
    """Get products containing the substance."""

    query = select(Product, ProductSubstance).options(
        selectinload(Product.substances)
    ).join(ProductSubstance).where(
        ProductSubstance.substance_id == substance_id
    )

    result = await session.execute(query)
    product_substance_pairs = result.all()

    products_containing = []

    for product, product_substance in product_substance_pairs:
        # Estimate market share (simplified)
        market_share = _estimate_market_share(product)

        product_containing = ProductContaining(
            product_id=product.id,
            product_name=product.product_name,
            concentration=product_substance.concentration,
            concentration_unit=product_substance.concentration_unit,
            formulation_type=product.formulation_type,
            authorization_status=product.authorization_status or "Unknown",
            is_primary_substance=product_substance.primary_substance or False,
            market_share_estimate=market_share
        )

        products_containing.append(product_containing)

    return products_containing


async def _analyze_usage_patterns_enhanced(
        session: AsyncSession,
        substance_id: int,
        intended_use_context: Optional[Dict[str, str]] = None,
        focus_on_authorized: bool = True
) -> List[UsePattern]:
    """Analyze usage patterns for the substance."""

    # Get product uses with enhanced filtering
    query = select(ProductUse, Crop, Target, Product).join(
        Product
    ).join(
        ProductSubstance
    ).join(
        Crop, ProductUse.crop_id == Crop.id
    ).join(
        Target, ProductUse.target_id == Target.id
    ).where(
        ProductSubstance.substance_id == substance_id
    )

    # Filter for authorized products if requested
    if focus_on_authorized:
        query = query.where(ProductUse.is_currently_authorized == True)

    # Filter by intended use context if provided
    if intended_use_context:
        if intended_use_context.get("crop"):
            crop_filter = intended_use_context["crop"].lower()
            query = query.where(
                or_(
                    Crop.crop_name.ilike(f"%{crop_filter}%"),
                    Crop.normalized_name.ilike(f"%{crop_filter}%"),
                    Crop.common_synonyms.ilike(f"%{crop_filter}%")
                )
            )
        if intended_use_context.get("target"):
            target_filter = intended_use_context["target"].lower()
            query = query.where(
                or_(
                    Target.target_name.ilike(f"%{target_filter}%"),
                    Target.normalized_name.ilike(f"%{target_filter}%"),
                    Target.target_type.ilike(f"%{target_filter}%")
                )
            )

    result = await session.execute(query)
    use_data = result.all()

    # Group by crop and target type
    pattern_groups = {}

    for product_use, crop, target, product in use_data:
        key = (crop.crop_name, target.target_type)

        if key not in pattern_groups:
            pattern_groups[key] = {
                "uses": [],
                "crop": crop,
                "target_type": target.target_type
            }

        pattern_groups[key]["uses"].append(product_use)

    # Create use patterns
    use_patterns = []

    for (crop_name, target_type), data in pattern_groups.items():
        uses = data["uses"]

        # Calculate frequency
        usage_frequency = len(uses)

        # Determine seasonal pattern (simplified)
        seasonal_pattern = _determine_seasonal_pattern(uses)

        # Calculate dose range
        dose_range = _calculate_dose_range(uses)

        # Determine application timing
        application_timing = _extract_application_timing(uses)

        # Assess effectiveness (simplified)
        effectiveness = _assess_effectiveness(usage_frequency, uses)

        pattern = UsePattern(
            crop_name=crop_name,
            target_type=target_type or "Unknown",
            usage_frequency=usage_frequency,
            seasonal_pattern=seasonal_pattern,
            typical_dose_range=dose_range,
            application_timing=application_timing,
            effectiveness_rating=effectiveness
        )

        use_patterns.append(pattern)

    # Sort by frequency
    use_patterns.sort(key=lambda x: x.usage_frequency, reverse=True)

    return use_patterns[:10]  # Return top 10 patterns


async def _assess_current_availability(
        session: AsyncSession,
        substance: ActiveSubstance,
        authorized_products: List[ProductContaining],
        withdrawn_products: List[ProductContaining]
) -> Dict[str, Any]:
    """Assess current market availability of the substance."""

    total_products = len(authorized_products) + len(withdrawn_products)

    availability_status = "not_available"
    if len(authorized_products) > 0:
        availability_status = "available"
    elif len(withdrawn_products) > 0:
        availability_status = "withdrawn"

    # Calculate market position
    market_position = "minor"
    if len(authorized_products) >= 10:
        market_position = "major"
    elif len(authorized_products) >= 5:
        market_position = "moderate"

    # Assess product diversity
    formulation_types = set(p.formulation_type for p in authorized_products if p.formulation_type)
    concentration_range = {
        "min": min((p.concentration for p in authorized_products if p.concentration), default=0),
        "max": max((p.concentration for p in authorized_products if p.concentration), default=0)
    }

    return {
        "status": availability_status,
        "authorized_products_count": len(authorized_products),
        "withdrawn_products_count": len(withdrawn_products),
        "total_products_count": total_products,
        "market_position": market_position,
        "formulation_diversity": len(formulation_types),
        "available_formulations": list(formulation_types),
        "concentration_range": concentration_range,
        "regulatory_substance_status": substance.authorization_status,
        "substance_name": substance.name
    }


async def _generate_application_guidance(
        session: AsyncSession,
        substance: ActiveSubstance,
        products: List[ProductContaining],
        use_patterns: List[UsePattern],
        intended_use_context: Optional[Dict[str, str]] = None
) -> Dict[str, Any]:
    """Generate practical application guidance for AI decision-making."""

    if not products:
        return {
            "status": "no_products_available",
            "message": "No authorized products containing this substance are currently available"
        }

    # Calculate typical application parameters
    concentrations = [p.concentration for p in products if p.concentration]
    typical_concentration = {
        "min": min(concentrations) if concentrations else None,
        "max": max(concentrations) if concentrations else None,
        "median": sorted(concentrations)[len(concentrations) // 2] if concentrations else None
    }

    # Analyze usage patterns for guidance
    if use_patterns:
        most_common_use = max(use_patterns, key=lambda x: x.usage_frequency)
        usage_summary = {
            "primary_crop": most_common_use.crop_name,
            "primary_target": most_common_use.target_type,
            "typical_dose_range": most_common_use.typical_dose_range,
            "seasonal_pattern": most_common_use.seasonal_pattern,
            "application_timing": most_common_use.application_timing
        }
    else:
        usage_summary = {"message": "No current usage patterns available"}

    # Context-specific guidance
    contextual_guidance = []
    if intended_use_context:
        crop_context = intended_use_context.get("crop")
        target_context = intended_use_context.get("target")

        # Find relevant use patterns
        relevant_patterns = [
            p for p in use_patterns
            if (not crop_context or crop_context.lower() in p.crop_name.lower()) and
               (not target_context or target_context.lower() in p.target_type.lower())
        ]

        if relevant_patterns:
            contextual_guidance.append(f"Found {len(relevant_patterns)} relevant usage patterns for your intended use")
            best_match = max(relevant_patterns, key=lambda x: x.usage_frequency)
            contextual_guidance.append(
                f"Most common application: {best_match.typical_dose_range} on {best_match.crop_name}")
        else:
            contextual_guidance.append("No specific usage patterns found for your intended crop/target combination")

    return {
        "status": "guidance_available",
        "typical_concentration": typical_concentration,
        "usage_summary": usage_summary,
        "contextual_guidance": contextual_guidance,
        "product_count": len(products),
        "usage_pattern_count": len(use_patterns)
    }


async def _generate_resistance_information_enhanced(
        session: AsyncSession,
        substance: ActiveSubstance,
        products: List[ProductContaining],
        use_patterns: List[UsePattern],
        risk_assessment_level: str = "standard"
) -> ResistanceInformation:
    """Generate resistance information with inference."""

    # Enhanced resistance risk assessment based on level
    resistance_risk = _infer_resistance_risk_enhanced(substance.name, use_patterns, risk_assessment_level)

    # Generate documented resistance with more detail for higher levels
    documented_resistance = _get_documented_resistance_enhanced(substance.name, risk_assessment_level)

    # Infer resistance mechanisms
    resistance_mechanisms = _infer_resistance_mechanisms(substance.name)

    # Determine cross-resistance groups
    cross_resistance = _determine_cross_resistance_groups(substance.name)

    # Generate enhanced management strategies
    management_strategies = _generate_resistance_management_strategies_enhanced(
        resistance_risk, documented_resistance, use_patterns, risk_assessment_level
    )

    # Create monitoring protocols
    monitoring_protocols = _create_resistance_monitoring_protocols(
        resistance_risk, use_patterns
    )

    # Suggest alternative substances (simplified for this function)
    alternative_substances = [f"See alternatives section for {substance.name} alternatives"]

    return ResistanceInformation(
        resistance_risk_level=resistance_risk,
        documented_resistance=documented_resistance,
        resistance_mechanisms=resistance_mechanisms,
        cross_resistance_groups=cross_resistance,
        management_strategies=management_strategies,
        monitoring_protocols=monitoring_protocols,
        alternative_substances=alternative_substances
    )


async def _create_safety_profile_enhanced(
        session: AsyncSession,
        substance: ActiveSubstance,
        products: List[ProductContaining]
) -> SafetyProfile:
    """Create safety profile with available and inferred data."""

    # Get toxicity information from product hazards
    toxicity_info = await _gather_toxicity_information(session, products)

    # Infer environmental fate
    environmental_fate = _infer_environmental_fate(substance.name)

    # Determine exposure pathways
    exposure_pathways = _determine_exposure_pathways(substance.name, toxicity_info)

    # Generate protective measures
    protective_measures = _generate_protective_measures(toxicity_info, substance.name)

    # Identify restricted uses
    restricted_uses = _identify_restricted_uses(products, toxicity_info)

    # Generate withdrawal considerations
    withdrawal_considerations = _generate_withdrawal_considerations(
        substance, toxicity_info
    )

    return SafetyProfile(
        toxicity_classification=toxicity_info,
        environmental_fate=environmental_fate,
        exposure_pathways=exposure_pathways,
        protective_measures=protective_measures,
        restricted_uses=restricted_uses,
        withdrawal_considerations=withdrawal_considerations
    )


async def _determine_regulatory_status_enhanced(
        session: AsyncSession,
        substance: ActiveSubstance,
        authorized_products: List[ProductContaining],
        withdrawn_products: List[ProductContaining]
) -> tuple[RegulatoryStatus, List[str]]:
    """Determine comprehensive regulatory status."""

    # Map authorization status
    eu_status_mapping = {
        "INSCRITE": "Approved",
        "NON_INSCRITE": "Not approved",
        "AUTRE_CAS": "Special case"
    }

    eu_status = eu_status_mapping.get(
        substance.authorization_status,
        substance.authorization_status or "Unknown"
    )

    all_products = authorized_products + withdrawn_products

    # Generate restrictions summary
    restrictions = _summarize_restrictions_enhanced(substance, authorized_products, withdrawn_products)

    # Create compliance requirements
    compliance_requirements = _generate_compliance_requirements_enhanced(substance, authorized_products)

    # Generate pending changes info
    pending_changes = _identify_pending_regulatory_changes(substance)

    # Generate compliance alerts for AI decision-making
    compliance_alerts = []
    if len(authorized_products) == 0:
        compliance_alerts.append("⚠️ CRITICAL: No authorized products available containing this substance")
    if len(withdrawn_products) > len(authorized_products) * 2:
        compliance_alerts.append("⚠️ WARNING: Many products withdrawn - substance may be under review")
    if substance.authorization_status == "NON_INSCRITE":
        compliance_alerts.append("⚠️ REGULATORY: Substance not approved for new product registrations")

    regulatory_status = RegulatoryStatus(
        eu_approval_status=eu_status,
        approval_expiry_date=None,  # Not available in current data
        review_status=_determine_review_status(substance),
        restrictions_summary=restrictions,
        regional_variations={"france": "As per E-Phy database"},
        pending_changes=pending_changes,
        compliance_requirements=compliance_requirements
    )

    return regulatory_status, compliance_alerts


async def _generate_chemical_intelligence(
        session: AsyncSession,
        substance: ActiveSubstance,
        variants: List[SubstanceVariant]
) -> ChemicalIntelligence:
    """Generate chemical intelligence analysis."""

    # Infer mode of action group
    moa_group = _infer_mode_of_action_group(substance.name)

    # Determine chemical family
    chemical_family = _determine_chemical_family(substance.name, variants)

    # Infer molecular target
    molecular_target = _infer_molecular_target(substance.name, moa_group)

    # Assess selectivity
    selectivity = _assess_selectivity_profile(substance.name, moa_group)

    # Infer persistence class
    persistence = _infer_persistence_class(substance.name)

    # Assess bioaccumulation potential
    bioaccumulation = _assess_bioaccumulation_potential(substance.name)

    # Determine degradation pathways
    degradation = _determine_degradation_pathways(substance.name)

    return ChemicalIntelligence(
        mode_of_action_group=moa_group,
        chemical_family=chemical_family,
        molecular_target=molecular_target,
        selectivity_profile=selectivity,
        persistence_class=persistence,
        bioaccumulation_potential=bioaccumulation,
        degradation_pathways=degradation
    )


# Helper functions for chemical analysis

def _classify_chemical_type(variant_name: str) -> str:
    """Classify chemical type of variant."""
    name_lower = variant_name.lower()

    if "sel de" in name_lower or "salt" in name_lower:
        return "salt"
    elif "ester" in name_lower:
        return "ester"
    elif "acide" in name_lower or "acid" in name_lower:
        return "acid"
    elif "base" in name_lower:
        return "base"
    else:
        return "other"


def _extract_cas_from_variant(variant_name: str) -> Optional[str]:
    """Extract CAS number from variant name if present."""
    import re

    # Look for CAS number pattern
    cas_pattern = r'\b\d{2,7}-\d{2}-\d\b'
    match = re.search(cas_pattern, variant_name)

    return match.group() if match else None


async def _get_formulation_types_for_variant(
        session: AsyncSession,
        substance_id: int,
        variant_name: str
) -> List[str]:
    """Get formulation types used with this variant."""

    query = select(distinct(Product.formulation_type)).join(
        ProductSubstance
    ).where(
        and_(
            ProductSubstance.substance_id == substance_id,
            Product.formulation_type.isnot(None)
        )
    )

    result = await session.execute(query)
    formulations = result.scalars().all()

    return [f for f in formulations if f and f.strip()]


def _generate_stability_notes(variant_name: str, chemical_type: str) -> List[str]:
    """Generate stability notes for chemical variant."""
    notes = []

    if chemical_type == "salt":
        notes.append("Generally more water-soluble than acid form")
        notes.append("May have different storage requirements")
    elif chemical_type == "ester":
        notes.append("May hydrolyze under alkaline conditions")
        notes.append("Often more lipophilic than parent compound")
    elif chemical_type == "acid":
        notes.append("pH-dependent solubility")
        notes.append("May be corrosive in concentrated form")

    # Add specific stability considerations
    if "copper" in variant_name.lower() or "cuivre" in variant_name.lower():
        notes.append("Sensitive to reducing conditions")
    elif "iron" in variant_name.lower() or "fer" in variant_name.lower():
        notes.append("May precipitate in alkaline conditions")

    return notes


def _estimate_market_share(product: Product) -> Optional[str]:
    """Estimate market share category for product."""
    if not product.is_currently_authorized:
        return "withdrawn"

    # Simple estimation based on product characteristics
    if product.function_category and "herbicide" in product.function_category.lower():
        if "glyphosate" in product.product_name.lower():
            return "major_market_share"
        else:
            return "moderate_market_share"
    else:
        return "minor_market_share"


def _determine_seasonal_pattern(uses: List) -> str:
    """Determine seasonal usage pattern."""
    if len(uses) > 10:
        return "year_round"
    elif len(uses) > 5:
        return "seasonal_peak"
    else:
        return "limited_use"


def _calculate_dose_range(uses: List) -> str:
    """Calculate typical dose range from uses."""
    doses = []
    units = set()

    for use in uses:
        if hasattr(use, 'min_dose') and use.min_dose is not None:
            doses.append(use.min_dose)
            if hasattr(use, 'dose_unit') and use.dose_unit:
                units.add(use.dose_unit)
        if hasattr(use, 'max_dose') and use.max_dose is not None:
            doses.append(use.max_dose)

    if not doses:
        return "Variable, as per label"

    min_dose = min(doses)
    max_dose = max(doses)
    unit = list(units)[0] if len(units) == 1 else "mixed units"

    if min_dose == max_dose:
        return f"{min_dose} {unit}"
    else:
        return f"{min_dose}-{max_dose} {unit}"


def _extract_application_timing(uses: List) -> List[str]:
    """Extract application timing information."""
    timing = []

    # Look for growth stage information
    growth_stages = []
    for use in uses:
        if hasattr(use, 'min_growth_stage') and use.min_growth_stage:
            growth_stages.append(use.min_growth_stage)
        if hasattr(use, 'max_growth_stage') and use.max_growth_stage:
            growth_stages.append(use.max_growth_stage)

    if growth_stages:
        timing.append(f"BBCH {min(growth_stages)}-{max(growth_stages)}")

    # Look for seasonal information
    seasons = set()
    for use in uses:
        if hasattr(use, 'application_season_min') and use.application_season_min:
            seasons.add(use.application_season_min)
        if hasattr(use, 'application_season_max') and use.application_season_max:
            seasons.add(use.application_season_max)

    if seasons:
        timing.extend(list(seasons))

    return timing if timing else ["As needed"]


def _assess_effectiveness(frequency: int, uses: List) -> str:
    """Assess effectiveness rating based on usage data."""
    if frequency > 20:
        return "widely_used"
    elif frequency > 10:
        return "commonly_used"
    elif frequency > 5:
        return "moderately_used"
    else:
        return "limited_use"


def _infer_resistance_risk(substance_name: str, use_patterns: List) -> str:
    """Infer resistance risk based on substance and usage."""
    name_lower = substance_name.lower()

    # High-risk substances (known resistance issues)
    high_risk_substances = [
        "glyphosate", "atrazine", "trifluralin", "diclofop",
        "clodinafop", "fenoxaprop", "fluazifop"
    ]

    if any(substance in name_lower for substance in high_risk_substances):
        return "high"

    # Check usage intensity
    total_usage = sum(pattern.usage_frequency for pattern in use_patterns)

    if total_usage > 50:
        return "medium"
    elif total_usage > 20:
        return "low_to_medium"
    else:
        return "low"


def _infer_resistance_risk_enhanced(substance_name: str, use_patterns: List, risk_level: str) -> str:
    """Enhanced resistance risk assessment."""
    base_risk = _infer_resistance_risk(substance_name, use_patterns)

    if risk_level == "detailed":
        # More sophisticated analysis for detailed level
        name_lower = substance_name.lower()

        # Mode of action specific risks
        if any(term in name_lower for term in ["azole", "conazole"]):
            return "medium_to_high"  # Fungicide resistance common
        elif "glyphosate" in name_lower:
            return "very_high"  # Well-documented resistance issues

    return base_risk


def _get_documented_resistance_enhanced(substance_name: str, risk_level: str) -> List[str]:
    """Enhanced documented resistance with detail levels."""
    base_cases = _get_documented_resistance(substance_name)

    if risk_level == "detailed" and base_cases:
        # Add more detailed information for detailed level
        enhanced_cases = []
        for case in base_cases:
            enhanced_cases.append(f"{case} (documented resistance)")
        return enhanced_cases

    return base_cases


def _generate_resistance_management_strategies_enhanced(
        risk_level: str,
        documented_cases: List[str],
        use_patterns: List,
        assessment_level: str
) -> List[str]:
    """Enhanced resistance management strategies."""
    strategies = _generate_resistance_management_strategies(risk_level, documented_cases, use_patterns)

    if assessment_level == "detailed":
        # Add more specific strategies for detailed assessment
        if risk_level == "high":
            strategies.extend([
                "🔬 Implement regular bioassays for resistance testing",
                "📊 Document and track efficacy levels systematically",
                "🌾 Consider refuge areas for resistance dilution"
            ])

    return strategies


def _summarize_restrictions_enhanced(
        substance: ActiveSubstance,
        authorized_products: List[ProductContaining],
        withdrawn_products: List[ProductContaining]
) -> List[str]:
    """Enhanced restrictions summary."""
    restrictions = _summarize_restrictions(substance, authorized_products + withdrawn_products)

    # Add specific restrictions based on product status
    if len(authorized_products) == 0:
        restrictions.append("❌ No authorized products available for use")

    if len(withdrawn_products) > len(authorized_products):
        restrictions.append("⚠️ More products withdrawn than currently authorized")

    return restrictions


def _generate_compliance_requirements_enhanced(
        substance: ActiveSubstance,
        authorized_products: List[ProductContaining]
) -> List[str]:
    """Enhanced compliance requirements."""
    requirements = _generate_compliance_requirements(substance, authorized_products)

    # Add AI-specific compliance guidance
    if len(authorized_products) > 0:
        requirements.extend([
            "✅ Verify product authorization status before use",
            "📋 Follow all label instructions exactly",
            "🕒 Check for any recent regulatory updates"
        ])
    else:
        requirements.extend([
            "🚫 Cannot recommend use - no authorized products available",
            "🔍 Consider alternative substances"
        ])

    return requirements


def _get_documented_resistance(substance_name: str) -> List[str]:
    """Get documented resistance cases."""
    name_lower = substance_name.lower()

    resistance_cases = {
        "glyphosate": [
            "Lolium rigidum (ryegrass)",
            "Amaranthus species (pigweed)",
            "Conyza species (horseweed)"
        ],
        "atrazine": [
            "Amaranthus retroflexus",
            "Chenopodium album",
            "Solanum nigrum"
        ],
        "2,4-d": [
            "Commelina benghalensis",
            "Kochia scoparia"
        ]
    }

    for substance, cases in resistance_cases.items():
        if substance in name_lower:
            return cases

    return []


def _infer_resistance_mechanisms(substance_name: str) -> List[str]:
    """Infer resistance mechanisms."""
    name_lower = substance_name.lower()

    if "glyphosate" in name_lower:
        return [
            "Target site mutation (EPSPS gene)",
            "Reduced translocation",
            "Metabolic degradation"
        ]
    elif any(term in name_lower for term in ["azole", "conazole"]):
        return [
            "Target site modification",
            "Enhanced metabolism",
            "Efflux pumps"
        ]
    elif "copper" in name_lower or "cuivre" in name_lower:
        return [
            "Copper efflux systems",
            "Copper sequestration",
            "Biofilm formation"
        ]
    else:
        return [
            "Target site modification",
            "Enhanced metabolism"
        ]


def _determine_cross_resistance_groups(substance_name: str) -> List[str]:
    """Determine cross-resistance groups."""
    name_lower = substance_name.lower()

    if "glyphosate" in name_lower:
        return ["EPSP synthase inhibitors", "Group G herbicides"]
    elif any(term in name_lower for term in ["azole", "conazole"]):
        return ["DMI fungicides", "SBI fungicides", "Group 3 fungicides"]
    elif "2,4-d" in name_lower:
        return ["Auxin mimics", "Phenoxy herbicides", "Group O herbicides"]
    else:
        return ["Same mode of action group"]


def _generate_resistance_management_strategies(
        risk_level: str,
        documented_cases: List[str],
        use_patterns: List
) -> List[str]:
    """Generate resistance management strategies."""
    strategies = []

    if risk_level == "high":
        strategies.extend([
            "Implement strict rotation with different modes of action",
            "Use tank mixes with complementary herbicides",
            "Employ refuge areas when applicable",
            "Monitor for reduced efficacy regularly"
        ])
    elif risk_level == "medium":
        strategies.extend([
            "Rotate with different chemical families",
            "Avoid consecutive applications of same substance",
            "Implement integrated management practices"
        ])
    else:
        strategies.extend([
            "Monitor usage frequency",
            "Consider rotation as preventive measure"
        ])

    # Add specific strategies based on documented resistance
    if documented_cases:
        strategies.append("Pay special attention to documented resistant species")

    return strategies


def _create_resistance_monitoring_protocols(
        risk_level: str,
        use_patterns: List
) -> List[str]:
    """Create resistance monitoring protocols."""
    protocols = [
        "Document application rates and timing",
        "Record efficacy levels after each treatment",
        "Monitor for unusual control failures"
    ]

    if risk_level == "high":
        protocols.extend([
            "Conduct annual efficacy assessments",
            "Test suspected resistant populations",
            "Participate in resistance monitoring networks"
        ])

    return protocols


async def _suggest_alternative_substances(
        session: AsyncSession,
        substance: ActiveSubstance,
        resistance_risk: str
) -> List[str]:
    """Suggest alternative substances for resistance management."""

    alternatives = []

    # Get substances with different modes of action (simplified)
    query = select(ActiveSubstance.name).where(
        and_(
            ActiveSubstance.authorization_status == "INSCRITE",
            ActiveSubstance.id != substance.id
        )
    ).limit(5)

    result = await session.execute(query)
    other_substances = result.scalars().all()

    # Filter based on different chemical families
    for alt_substance in other_substances:
        if _determine_chemical_family(alt_substance, []) != _determine_chemical_family(substance.name, []):
            alternatives.append(alt_substance)

    return alternatives[:5]


async def _gather_toxicity_information(
        session: AsyncSession,
        products: List[ProductContaining]
) -> Dict[str, str]:
    """Gather toxicity information from product hazards."""

    toxicity_info = {}

    # Get hazard information for products containing this substance
    product_ids = [p.product_id for p in products[:10]]  # Limit to first 10

    if product_ids:
        from models.e_phy import ProductHazard

        query = select(ProductHazard).where(
            ProductHazard.product_id.in_(product_ids)
        )

        result = await session.execute(query)
        hazards = result.scalars().all()

        # Analyze hazard patterns
        hazard_codes = set()
        max_severity = 0

        for hazard in hazards:
            if hazard.hazard_code:
                hazard_codes.add(hazard.hazard_code)
            if hazard.hazard_severity:
                max_severity = max(max_severity, hazard.hazard_severity)

        # Categorize toxicity
        if max_severity >= 4:
            toxicity_info["acute_toxicity"] = "high"
        elif max_severity >= 3:
            toxicity_info["acute_toxicity"] = "moderate"
        else:
            toxicity_info["acute_toxicity"] = "low"

        # Check for specific hazard categories
        if any("H3" in code for code in hazard_codes):
            toxicity_info["health_hazard"] = "present"

        if any("H4" in code for code in hazard_codes):
            toxicity_info["environmental_hazard"] = "present"

    return toxicity_info


def _infer_environmental_fate(substance_name: str) -> Dict[str, str]:
    """Infer environmental fate characteristics."""
    name_lower = substance_name.lower()

    fate = {}

    # Water solubility inference
    if any(term in name_lower for term in ["salt", "sel"]):
        fate["water_solubility"] = "high"
    elif "oil" in name_lower or "huile" in name_lower:
        fate["water_solubility"] = "low"
    else:
        fate["water_solubility"] = "moderate"

    # Persistence inference
    if "copper" in name_lower or "cuivre" in name_lower:
        fate["soil_persistence"] = "high"
    elif "glyphosate" in name_lower:
        fate["soil_persistence"] = "moderate"
    else:
        fate["soil_persistence"] = "variable"

    # Mobility inference
    if fate["water_solubility"] == "high":
        fate["soil_mobility"] = "high"
    else:
        fate["soil_mobility"] = "low_to_moderate"

    return fate


def _determine_exposure_pathways(substance_name: str, toxicity_info: Dict) -> List[str]:
    """Determine exposure pathways."""
    pathways = ["Dermal contact", "Inhalation", "Ingestion"]

    name_lower = substance_name.lower()

    if "volatile" in name_lower or "gas" in name_lower:
        pathways.insert(0, "Vapor inhalation")

    if toxicity_info.get("water_solubility") == "high":
        pathways.append("Groundwater contamination")

    return pathways


def _generate_protective_measures(toxicity_info: Dict, substance_name: str) -> List[str]:
    """Generate protective measures."""
    measures = [
        "Use appropriate personal protective equipment",
        "Follow label instructions strictly",
        "Avoid application during windy conditions"
    ]

    if toxicity_info.get("acute_toxicity") == "high":
        measures.extend([
            "Use respiratory protection",
            "Ensure proper ventilation",
            "Have emergency procedures in place"
        ])

    if toxicity_info.get("environmental_hazard") == "present":
        measures.extend([
            "Observe buffer zones around water bodies",
            "Prevent drift to sensitive areas"
        ])

    return measures


def _identify_restricted_uses(
        products: List[ProductContaining],
        toxicity_info: Dict
) -> List[str]:
    """Identify restricted uses."""
    restrictions = []

    # Check for amateur use restrictions
    professional_only = any(
        product.authorization_status and "professional" in product.authorization_status.lower()
        for product in products
    )

    if professional_only:
        restrictions.append("Professional use only")

    # Add toxicity-based restrictions
    if toxicity_info.get("acute_toxicity") == "high":
        restrictions.extend([
            "Restricted application timing",
            "Enhanced protective equipment required"
        ])

    return restrictions


def _generate_withdrawal_considerations(
        substance: ActiveSubstance,
        toxicity_info: Dict
) -> List[str]:
    """Generate withdrawal considerations."""
    considerations = []

    if not substance.is_currently_authorized:
        considerations.extend([
            "Product authorization has been withdrawn",
            "Use only remaining authorized products",
            "Plan transition to alternative substances"
        ])

    if toxicity_info.get("environmental_hazard") == "present":
        considerations.append("Environmental concerns may affect future authorization")

    return considerations


def _summarize_restrictions(
        substance: ActiveSubstance,
        products: List[ProductContaining]
) -> List[str]:
    """Summarize regulatory restrictions."""
    restrictions = []

    if substance.authorization_status == "NON_INSCRITE":
        restrictions.append("Not approved for new products")
    elif substance.authorization_status == "AUTRE_CAS":
        restrictions.append("Special authorization conditions apply")

    # Count withdrawn products
    withdrawn_count = sum(
        1 for p in products
        if p.authorization_status and "retire" in p.authorization_status.lower()
    )

    if withdrawn_count > 0:
        restrictions.append(f"{withdrawn_count} products containing substance withdrawn")

    return restrictions


def _generate_compliance_requirements(
        substance: ActiveSubstance,
        products: List[ProductContaining]
) -> List[str]:
    """Generate compliance requirements."""
    requirements = [
        "Use only authorized products",
        "Follow label application rates",
        "Respect buffer zones and restrictions"
    ]

    if substance.authorization_status == "INSCRITE":
        requirements.append("Comply with EU approval conditions")

    return requirements


def _identify_pending_regulatory_changes(substance: ActiveSubstance) -> List[str]:
    """Identify pending regulatory changes."""
    # This would require external regulatory data
    # For now, provide general guidance

    changes = [
        "Monitor official regulatory announcements",
        "Check for renewal schedule updates"
    ]

    if substance.authorization_status == "NON_INSCRITE":
        changes.append("May be subject to phase-out measures")

    return changes


def _determine_review_status(substance: ActiveSubstance) -> str:
    """Determine review status."""
    if substance.authorization_status == "INSCRITE":
        return "Under periodic review"
    elif substance.authorization_status == "NON_INSCRITE":
        return "Not approved for renewal"
    else:
        return "Special review status"


def _infer_mode_of_action_group(substance_name: str) -> str:
    """Infer mode of action group."""
    name_lower = substance_name.lower()

    # Herbicide MOA groups
    if "glyphosate" in name_lower:
        return "EPSP synthase inhibitors (Group 9)"
    elif "2,4-d" in name_lower or "mcpa" in name_lower:
        return "Auxin mimics (Group 4)"
    elif "atrazine" in name_lower or "simazine" in name_lower:
        return "Photosystem II inhibitors (Group 5)"

    # Fungicide MOA groups
    elif any(term in name_lower for term in ["azole", "conazole"]):
        return "Sterol biosynthesis inhibitors (Group 3)"
    elif "copper" in name_lower or "cuivre" in name_lower:
        return "Multi-site contact activity (Group M)"
    elif "sulfur" in name_lower or "soufre" in name_lower:
        return "Multi-site contact activity (Group M)"

    # Insecticide MOA groups
    elif any(term in name_lower for term in ["pyrethrin", "cypermethrin"]):
        return "Sodium channel modulators (Group 3A)"
    elif "carbamate" in name_lower:
        return "Acetylcholinesterase inhibitors (Group 1A)"

    else:
        return "Mode of action not determined"


def _determine_chemical_family(substance_name: str, variants: List) -> str:
    """Determine chemical family."""
    name_lower = substance_name.lower()

    # Chemical family classification
    if "glyphosate" in name_lower:
        return "Phosphonates"
    elif any(term in name_lower for term in ["azole", "conazole"]):
        return "Triazoles"
    elif "2,4-d" in name_lower:
        return "Phenoxy acids"
    elif "copper" in name_lower or "cuivre" in name_lower:
        return "Inorganic compounds"
    elif "sulfur" in name_lower or "soufre" in name_lower:
        return "Inorganic compounds"
    elif any(term in name_lower for term in ["pyrethrin", "cypermethrin"]):
        return "Pyrethroids"
    elif "atrazine" in name_lower or "simazine" in name_lower:
        return "Triazines"
    else:
        return "Other organic compounds"


def _infer_molecular_target(substance_name: str, moa_group: str) -> str:
    """Infer molecular target."""
    name_lower = substance_name.lower()

    if "glyphosate" in name_lower:
        return "EPSPS enzyme"
    elif "2,4-d" in name_lower:
        return "Auxin receptors"
    elif any(term in name_lower for term in ["azole", "conazole"]):
        return "Sterol 14α-demethylase"
    elif "copper" in name_lower:
        return "Multiple cellular targets"
    else:
        return "Target not specified"


def _assess_selectivity_profile(substance_name: str, moa_group: str) -> str:
    """Assess selectivity profile."""
    name_lower = substance_name.lower()

    if "glyphosate" in name_lower:
        return "Non-selective herbicide"
    elif any(term in name_lower for term in ["azole", "conazole"]):
        return "Selective fungicide"
    elif "copper" in name_lower:
        return "Broad-spectrum fungicide/bactericide"
    else:
        return "Selectivity variable"


def _infer_persistence_class(substance_name: str) -> str:
    """Infer persistence class."""
    name_lower = substance_name.lower()

    if "copper" in name_lower or "cuivre" in name_lower:
        return "Persistent"
    elif "glyphosate" in name_lower:
        return "Moderately persistent"
    elif "2,4-d" in name_lower:
        return "Moderately persistent"
    else:
        return "Persistence variable"


def _assess_bioaccumulation_potential(substance_name: str) -> str:
    """Assess bioaccumulation potential."""
    name_lower = substance_name.lower()

    if "copper" in name_lower:
        return "Moderate bioaccumulation potential"
    elif any(term in name_lower for term in ["oil", "huile", "fat"]):
        return "High bioaccumulation potential"
    else:
        return "Low to moderate bioaccumulation potential"


def _determine_degradation_pathways(substance_name: str) -> List[str]:
    """Determine degradation pathways."""
    name_lower = substance_name.lower()

    pathways = ["Microbial degradation"]

    if "copper" in name_lower:
        pathways.extend(["Chemical precipitation", "Adsorption to soil particles"])
    elif "glyphosate" in name_lower:
        pathways.extend(["Enzymatic breakdown", "Photodegradation"])
    else:
        pathways.extend(["Hydrolysis", "Photodegradation"])

    return pathways


async def _perform_market_analysis(
        session: AsyncSession,
        substance: ActiveSubstance,
        products: List[ProductContaining],
        use_patterns: List[UsePattern]
) -> Dict[str, Any]:
    """Perform market analysis."""

    total_products = len(products)
    authorized_products = sum(1 for p in products if "autorise" in p.authorization_status.lower())

    # Calculate usage distribution
    total_usage = sum(pattern.usage_frequency for pattern in use_patterns)

    # Market position analysis
    market_position = "minor"
    if total_usage > 50:
        market_position = "major"
    elif total_usage > 20:
        market_position = "moderate"

    return {
        "total_products": total_products,
        "authorized_products": authorized_products,
        "market_position": market_position,
        "usage_distribution": {
            pattern.crop_name: pattern.usage_frequency
            for pattern in use_patterns[:5]
        },
        "authorization_rate": f"{(authorized_products / total_products) * 100:.1f}%" if total_products > 0 else "0%"
    }


async def _generate_substance_recommendations(
        substance: ActiveSubstance,
        resistance_info: ResistanceInformation,
        safety_profile: SafetyProfile,
        regulatory_status: RegulatoryStatus
) -> List[str]:
    """Generate substance-specific recommendations."""

    recommendations = []

    # Authorization-based recommendations
    if substance.is_currently_authorized:
        recommendations.append("✅ Substance currently authorized for use")
    else:
        recommendations.append("❌ Substance authorization withdrawn - transition to alternatives")

    # Resistance management recommendations
    if resistance_info.resistance_risk_level == "high":
        recommendations.extend([
            "⚠️ High resistance risk - implement strict rotation protocols",
            "🔍 Monitor efficacy closely and test for resistance"
        ])

    # Safety recommendations
    if safety_profile.toxicity_classification.get("acute_toxicity") == "high":
        recommendations.append("🥽 High toxicity - enhanced protective measures required")

    # Environmental recommendations
    if "environmental_hazard" in safety_profile.toxicity_classification:
        recommendations.append("🌍 Environmental concerns - follow buffer zone requirements strictly")

    # Regulatory recommendations
    if regulatory_status.eu_approval_status == "Not approved":
        recommendations.append("🏛️ EU approval required for new products")

    return recommendations


async def _find_related_substances(
        session: AsyncSession,
        substance: ActiveSubstance,
        chemical_intelligence: ChemicalIntelligence
) -> List[str]:
    """Find chemically related substances."""

    # Find substances in same chemical family
    family_keywords = chemical_intelligence.chemical_family.lower().split()

    related = []

    if family_keywords:
        query = select(ActiveSubstance.name).where(
            and_(
                ActiveSubstance.id != substance.id,
                or_(*[
                    ActiveSubstance.name.ilike(f"%{keyword}%")
                    for keyword in family_keywords[:2]  # Limit to first 2 keywords
                ])
            )
        ).limit(5)

        result = await session.execute(query)
        related_substances = result.scalars().all()
        related.extend(related_substances)

    return related


def _calculate_data_completeness(
        substance: ActiveSubstance,
        variants: List[SubstanceVariant],
        products: List[ProductContaining],
        use_patterns: List[UsePattern]
) -> Dict[str, float]:
    """Calculate data completeness scores."""

    completeness = {}

    # Basic substance data
    basic_score = 0.0
    if substance.name:
        basic_score += 0.3
    if substance.cas_number:
        basic_score += 0.3
    if substance.authorization_status:
        basic_score += 0.2
    if substance.variants:
        basic_score += 0.2

    completeness["basic_information"] = basic_score

    # Variants data
    completeness["variants"] = 1.0 if variants else 0.0

    # Products data
    completeness["products"] = min(len(products) / 10, 1.0)  # Score based on number of products

    # Usage patterns
    completeness["usage_patterns"] = min(len(use_patterns) / 5, 1.0)

    # Safety data (inferred from products)
    safety_score = 0.5 if products else 0.0  # Base score for having products
    completeness["safety_data"] = safety_score

    return completeness


def _calculate_search_confidence(query: str, matched_name: str) -> float:
    """Calculate search confidence."""
    query_lower = query.lower().strip()
    matched_lower = matched_name.lower().strip()

    if query_lower == matched_lower:
        return 1.0
    elif query_lower in matched_lower or matched_lower in query_lower:
        return 0.8
    else:
        return 0.6


async def _create_substance_not_found_response(
        session: AsyncSession,
        query: str,
        include_alternatives: bool = True
) -> ActiveSubstanceResponse:
    """Create enhanced response when substance is not found."""

    # Try to find similar substances for suggestions
    suggestions = []
    if include_alternatives:
        # Search for partial matches
        similar_query = select(ActiveSubstance.name).where(
            or_(
                ActiveSubstance.name.ilike(f"%{query[:5]}%"),
                ActiveSubstance.normalized_name.ilike(f"%{query[:5]}%")
            )
        ).limit(5)

        try:
            result = await session.execute(similar_query)
            suggestions = result.scalars().all()
        except:
            suggestions = []

    from datetime import datetime

    return ActiveSubstanceResponse(
        substance_profile={
            "error": f"Substance '{query}' not found in database",
            "searched_query": query,
            "suggestions": suggestions
        },
        current_availability={"status": "not_found"},
        authorized_products=[],
        withdrawn_products=[],
        use_patterns=[],
        application_guidance={"status": "substance_not_found"},
        safety_profile=SafetyProfile(
            toxicity_classification={},
            environmental_fate={},
            exposure_pathways=[],
            protective_measures=[],
            restricted_uses=[],
            withdrawal_considerations=[]
        ),
        regulatory_status=RegulatoryStatus(
            eu_approval_status="unknown",
            approval_expiry_date=None,
            review_status="unknown",
            restrictions_summary=[],
            regional_variations={},
            pending_changes=[],
            compliance_requirements=[]
        ),
        compliance_alerts=[f"Substance '{query}' not found in database"],
        chemical_intelligence=ChemicalIntelligence(
            mode_of_action_group="unknown",
            chemical_family="unknown",
            molecular_target="unknown",
            selectivity_profile="unknown",
            persistence_class="unknown",
            bioaccumulation_potential="unknown",
            degradation_pathways=[]
        ),
        resistance_information=ResistanceInformation(
            resistance_risk_level="unknown",
            documented_resistance=[],
            resistance_mechanisms=[],
            cross_resistance_groups=[],
            management_strategies=[],
            monitoring_protocols=[],
            alternative_substances=[]
        ),
        suitability_assessment={"rating": "not_found", "overall_score": 0},
        recommendations={
            "immediate_actions": [f"❌ Substance '{query}' not found", "🔍 Check spelling or try alternative names"],
            "safety_precautions": [],
            "regulatory_compliance": [],
            "resistance_management": [],
            "application_guidance": [],
            "alternatives_consideration": ["🧪 Try searching by CAS number if available"] +
                                          ([
                                               f"💡 Similar substances found: {', '.join(suggestions[:3])}"] if suggestions else [])
        },
        alternatives=[],
        variants=[],
        related_substances=[],
        data_quality={"overall": {"score": 0.0, "confidence_level": "none"}},
        search_metadata={
            "search_query": query,
            "matched_substance": None,
            "search_confidence": 0.0,
            "analysis_focus": "not_found"
        },
        analysis_timestamp=datetime.now().isoformat()
    )


# Additional helper functions for enhanced functionality

async def _assess_substance_suitability(
        substance: "ActiveSubstance",
        products: List["ProductContaining"],
        use_patterns: List["UsePattern"],
        safety_profile: "SafetyProfile",
        regulatory_status: "RegulatoryStatus",
        intended_use_context: Optional[Dict[str, str]] = None
) -> Dict[str, Any]:
    """Assess overall suitability of substance for intended use."""

    # Calculate suitability score (0-100)
    score = 0
    factors = []

    # Availability factor (40 points max)
    if len(products) > 0:
        score += min(40, len(products) * 8)  # Up to 5 products = full points
        factors.append(f"Product availability: {len(products)} products (+{min(40, len(products) * 8)})")
    else:
        factors.append("Product availability: No products available (0)")

    # Regulatory factor (25 points max)
    if substance.authorization_status == "INSCRITE":
        score += 25
        factors.append("Regulatory status: EU approved (+25)")
    elif substance.authorization_status == "AUTRE_CAS":
        score += 15
        factors.append("Regulatory status: Special case (+15)")
    else:
        factors.append("Regulatory status: Not approved (0)")

    # Usage experience factor (20 points max)
    total_usage = sum(p.usage_frequency for p in use_patterns)
    usage_score = min(20, total_usage // 5)  # 1 point per 5 usage instances
    score += usage_score
    factors.append(f"Usage experience: {total_usage} patterns (+{usage_score})")

    # Safety factor (15 points max)
    safety_score = 15
    if safety_profile.toxicity_classification.get("acute_toxicity") == "high":
        safety_score -= 10
        factors.append("Safety: High toxicity concern (-10)")
    elif safety_profile.toxicity_classification.get("environmental_hazard") == "present":
        safety_score -= 5
        factors.append("Safety: Environmental concern (-5)")
    score += safety_score

    # Context relevance bonus (up to 10 points)
    if intended_use_context and use_patterns:
        crop_context = intended_use_context.get("crop", "").lower()
        target_context = intended_use_context.get("target", "").lower()

        relevant_patterns = [
            p for p in use_patterns
            if (not crop_context or crop_context in p.crop_name.lower()) and
               (not target_context or target_context in p.target_type.lower())
        ]

        if relevant_patterns:
            context_score = min(10, len(relevant_patterns) * 2)
            score += context_score
            factors.append(f"Context relevance: {len(relevant_patterns)} relevant patterns (+{context_score})")

    # Determine suitability rating
    if score >= 80:
        rating = "highly_suitable"
    elif score >= 60:
        rating = "suitable"
    elif score >= 40:
        rating = "moderately_suitable"
    elif score >= 20:
        rating = "limited_suitability"
    else:
        rating = "not_suitable"

    return {
        "overall_score": score,
        "rating": rating,
        "scoring_factors": factors,
        "product_availability": len(products) > 0,
        "regulatory_approved": substance.authorization_status == "INSCRITE",
        "usage_experience": total_usage,
        "context_relevant": bool(intended_use_context and any(
            intended_use_context.get("crop", "").lower() in p.crop_name.lower() or
            intended_use_context.get("target", "").lower() in p.target_type.lower()
            for p in use_patterns
        ))
    }


async def _generate_enhanced_recommendations(
        substance: "ActiveSubstance",
        resistance_info: "ResistanceInformation",
        safety_profile: "SafetyProfile",
        regulatory_status: "RegulatoryStatus",
        suitability_assessment: Dict[str, Any],
        intended_use_context: Optional[Dict[str, str]] = None
) -> Dict[str, List[str]]:
    """Generate categorized recommendations for AI decision-making."""

    recommendations = {
        "immediate_actions": [],
        "safety_precautions": [],
        "regulatory_compliance": [],
        "resistance_management": [],
        "application_guidance": [],
        "alternatives_consideration": []
    }

    # Immediate actions based on suitability
    if suitability_assessment["rating"] == "highly_suitable":
        recommendations["immediate_actions"].append("✅ Substance highly suitable for intended use")
        recommendations["immediate_actions"].append("👍 Proceed with product selection and application planning")
    elif suitability_assessment["rating"] == "not_suitable":
        recommendations["immediate_actions"].append("❌ Substance not suitable for intended use")
        recommendations["immediate_actions"].append("🔍 Consider alternative substances immediately")
    else:
        recommendations["immediate_actions"].append(
            f"⚠️ Substance shows {suitability_assessment['rating']} - evaluate carefully")

    # Safety precautions
    if safety_profile.toxicity_classification.get("acute_toxicity") == "high":
        recommendations["safety_precautions"].extend([
            "😷 Mandatory respiratory protection required",
            "🧤 Enhanced PPE protocols necessary",
            "🏥 Emergency response procedures must be in place"
        ])

    if "environmental_hazard" in safety_profile.toxicity_classification:
        recommendations["safety_precautions"].extend([
            "🌊 Strict buffer zones around water bodies",
            "🌃 Avoid application during windy conditions",
            "🐟 Monitor for aquatic organism impact"
        ])

    # Regulatory compliance
    if regulatory_status.eu_approval_status == "Not approved":
        recommendations["regulatory_compliance"].append("⚠️ EU approval required for new products")

    if not suitability_assessment["product_availability"]:
        recommendations["regulatory_compliance"].append("❌ No authorized products available - cannot use legally")

    # Resistance management
    if resistance_info.resistance_risk_level == "high":
        recommendations["resistance_management"].extend([
            "🔄 Implement strict rotation with different modes of action",
            "🔍 Monitor for reduced efficacy after each application",
            "🧪 Use tank mixes when possible to reduce resistance pressure"
        ])

    # Application guidance
    if intended_use_context:
        if suitability_assessment["context_relevant"]:
            recommendations["application_guidance"].append("✅ Usage patterns available for your intended crop/target")
        else:
            recommendations["application_guidance"].append(
                "⚠️ Limited usage data for your specific crop/target combination")

    # Alternatives consideration
    if suitability_assessment["overall_score"] < 60:
        recommendations["alternatives_consideration"].append("🔍 Consider alternative active substances")

    if resistance_info.resistance_risk_level in ["high", "medium"]:
        recommendations["alternatives_consideration"].append(
            "🔄 Identify rotation partners with different modes of action")

    return recommendations


async def _find_alternative_substances(
        session: AsyncSession,
        substance: "ActiveSubstance",
        chemical_intelligence: "ChemicalIntelligence",
        intended_use_context: Optional[Dict[str, str]] = None
) -> List[Dict[str, Any]]:
    """Find alternative substances for rotation or replacement."""

    alternatives = []

    # Get substances with different chemical families that are approved
    query = select(ActiveSubstance).where(
        and_(
            ActiveSubstance.authorization_status == "INSCRITE",
            ActiveSubstance.id != substance.id
        )
    ).limit(10)

    result = await session.execute(query)
    candidate_substances = result.scalars().all()

    for candidate in candidate_substances:
        # Check if candidate has authorized products
        products_query = select(func.count(Product.id)).join(ProductSubstance).where(
            and_(
                ProductSubstance.substance_id == candidate.id,
                Product.is_currently_authorized == True
            )
        )
        products_result = await session.execute(products_query)
        product_count = products_result.scalar()

        if product_count > 0:
            alternatives.append({
                "substance_name": candidate.name,
                "cas_number": candidate.cas_number,
                "available_products": product_count,
                "suitability_reason": f"Alternative substance with {product_count} authorized products"
            })

    # Sort by number of available products
    alternatives.sort(key=lambda x: x["available_products"], reverse=True)

    return alternatives[:5]


async def _calculate_data_quality_enhanced(
        substance: "ActiveSubstance",
        variants: List["SubstanceVariant"],
        products: List["ProductContaining"],
        use_patterns: List["UsePattern"],
        safety_profile: "SafetyProfile"
) -> Dict[str, Any]:
    """Calculate enhanced data quality metrics for AI decision confidence."""

    quality_metrics = {}

    # Basic substance data quality
    basic_quality = 0.0
    if substance.name:
        basic_quality += 0.3
    if substance.cas_number:
        basic_quality += 0.3
    if substance.authorization_status:
        basic_quality += 0.2
    if substance.variants:
        basic_quality += 0.2

    quality_metrics["basic_information"] = {
        "score": basic_quality,
        "completeness": "excellent" if basic_quality >= 0.9 else "good" if basic_quality >= 0.7 else "limited"
    }

    # Product availability quality
    product_quality = min(1.0, len(products) / 5)  # Normalize to 5 products = perfect
    quality_metrics["product_availability"] = {
        "score": product_quality,
        "product_count": len(products),
        "reliability": "high" if len(products) >= 3 else "medium" if len(products) >= 1 else "low"
    }

    # Usage patterns quality
    usage_quality = min(1.0, len(use_patterns) / 10)  # Normalize to 10 patterns = perfect
    total_usage_frequency = sum(p.usage_frequency for p in use_patterns)
    quality_metrics["usage_patterns"] = {
        "score": usage_quality,
        "pattern_count": len(use_patterns),
        "total_frequency": total_usage_frequency,
        "reliability": "high" if total_usage_frequency >= 20 else "medium" if total_usage_frequency >= 5 else "low"
    }

    # Safety data quality
    safety_data_points = len(safety_profile.toxicity_classification) + \
                         len(safety_profile.exposure_pathways) + \
                         len(safety_profile.protective_measures)
    safety_quality = min(1.0, safety_data_points / 10)
    quality_metrics["safety_information"] = {
        "score": safety_quality,
        "data_points": safety_data_points,
        "confidence": "high" if safety_data_points >= 8 else "medium" if safety_data_points >= 4 else "low"
    }

    # Overall quality score
    overall_score = (basic_quality + product_quality + usage_quality + safety_quality) / 4
    quality_metrics["overall"] = {
        "score": overall_score,
        "confidence_level": "high" if overall_score >= 0.8 else "medium" if overall_score >= 0.6 else "low",
        "recommendation": "suitable_for_ai_decisions" if overall_score >= 0.7 else "use_with_caution" if overall_score >= 0.5 else "limited_reliability"
    }

    return quality_metrics
