from collections import Counter

import nltk
from nltk.corpus import stopwords
from sqlalchemy import func, select, or_, and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import text as sql_text

from models.e_phy import (
    ProductUse, Target, Crop, ProductSubstance, ActiveSubstance, Product,
    UsagePattern, ProductHazard
)
from utils.timer import Timer

# Download NLTK resources if not already downloaded
try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')


class DataEnhancer:
    def __init__(self, session: AsyncSession):
        """Initialize the data enhancer with proper async session"""
        self.session = session

        # Load stopwords once during initialization
        self.french_stopwords = set(stopwords.words('french'))

        # Additional French agricultural stopwords
        agricultural_stopwords = {
            'culture', 'plante', 'traitement', 'application', 'dose', 'emploi',
            'usage', 'jour', 'mois', 'année', 'utilisable', 'également', 'suivant',
            'différent', 'contre', 'entre', 'autre', 'taux', 'stade', 'niveau'
        }
        self.french_stopwords.update(agricultural_stopwords)

        self.t = Timer("DataEnhancer")

        # Track processed tasks to avoid redundancy
        self._processed_tasks = set()

    async def enhance_all_data(self):
        """
        Main method to enhance all data
        
        This method implements idempotency by tracking which enhancement tasks
        have already been completed, preventing redundant processing even when
        called multiple times.
        """
        self.t.start()
        print("Starting data enhancement process...")

        # Define enhancement tasks as tuples: (task_name, coroutine_function)
        enhancement_tasks = [
            # ("generate_search_vectors", self.generate_search_vectors),
            # ("enhance_hazard_information", self.enhance_hazard_information),
            # ("generate_usage_patterns", self.generate_usage_patterns),
            # ("update_product_summaries", self.update_product_summaries),
            ("extract_crop_synonyms", self.extract_crop_synonyms),
            ("extract_target_synonyms", self.extract_target_synonyms),
            ("generate_seasonal_recommendations", self.generate_seasonal_recommendations),
            ("enhance_dose_recommendations", self.enhance_dose_recommendations),
            ("create_compatibility_insights", self.create_compatibility_insights),
            ("generate_organic_farming_suitability", self.generate_organic_farming_suitability)
        ]

        try:
            # Execute each enhancement task if not already processed
            for task_name, task_func in enhancement_tasks:
                if task_name not in self._processed_tasks:
                    print(f"Executing task: {task_name}")
                    await task_func()
                    self._processed_tasks.add(task_name)
                    self.t.checkpoint(task_name)
                else:
                    print(f"Skipping already processed task: {task_name}")

            print("Data enhancement complete!")

        except Exception as e:
            print(f"Error in enhance_all_data: {e}")
            # We don't close the session here as it should be managed by the caller
            raise
        self.t.stop()

    async def generate_search_vectors(self):
        """Generate TSVECTOR search vectors for products using SQLAlchemy 2.0 syntax"""
        print("Generating search vectors for products...")

        try:
            # PostgreSQL-specific query to update search_vector
            update_query = sql_text("""
                UPDATE ai.products 
                SET search_vector = 
                    setweight(to_tsvector('french', coalesce(product_name, '')), 'A') || 
                    setweight(to_tsvector('french', coalesce(alternative_names, '')), 'B') || 
                    setweight(to_tsvector('french', coalesce(holder, '')), 'C') || 
                    setweight(to_tsvector('french', coalesce(function_category, '')), 'C') || 
                    setweight(to_tsvector('french', coalesce(registration_number, '')), 'A')
                WHERE search_vector IS NULL OR 
                      product_name IS NOT NULL AND 
                      to_tsvector('french', coalesce(product_name, '')) <> ''
            """)

            await self.session.execute(update_query)
            await self.session.commit()
            print("Search vectors generated successfully")

        except Exception as e:
            await self.session.rollback()
            print(f"Error generating search vectors: {e}")
            raise

    # need to retouch
    async def enhance_hazard_information(self):
        """Enhance hazard information with more actionable descriptions"""
        print("Enhancing hazard information...")

        #TODO: add more guidance
        # Define hazard guidance based on hazard categories
        hazard_guidance = {
            'Toxicité aiguë': 'Require l\'utilisation d\'équipements de protection individuelle (EPI) complets.',
            'Toxicité chronique': 'Éviter l\'exposition répétée. Utiliser des EPI adaptés.',
            'Irritation cutanée': 'Porter des gants et des vêtements de protection.',
            'Irritation oculaire': 'Porter des lunettes de protection.',
            'Sensibilisation': 'Éviter tout contact avec la peau et les voies respiratoires.',
            'Cancérogénicité': 'Prendre des précautions maximales lors de la manipulation.',
            'Mutagénicité': 'Éviter strictement toute exposition.',
            'Toxique pour la reproduction': 'Ne pas utiliser ce produit pendant la grossesse.',
            'Toxicité aquatique': 'Prévenir la contamination des eaux de surface et souterraines.',
            'Inflammabilité': 'Tenir à l\'écart de toute source d\'ignition.',
            'Corrosivité': 'Manipuler avec une extrême prudence. Porter des EPI résistants aux produits chimiques.'
        }

        try:
            # Find hazards that need enhancement (without enhanced description)
            result = await self.session.execute(
                select(ProductHazard).where(
                    and_(
                        ProductHazard.hazard_category.isnot(None),
                        ProductHazard.hazard_category != 'Autre',
                        or_(
                            ProductHazard.hazard_description.is_(None),
                            ProductHazard.hazard_description == ''
                        )
                    )
                )
            )
            hazards = result.scalars().all()

            # Process hazards in batches
            for hazard in hazards:
                # Add guidance if available for this category
                for category, guidance in hazard_guidance.items():
                    if category.lower() in hazard.hazard_category.lower():
                        # Only append guidance if not already present
                        if not hazard.hazard_description or guidance not in hazard.hazard_description:
                            current_desc = hazard.hazard_description or ''
                            if current_desc:
                                hazard.hazard_description = f"{current_desc} | {guidance}"
                            else:
                                hazard.hazard_description = guidance
                        break

            if hazards:
                await self.session.commit()
                print(f"Enhanced hazard information for {len(hazards)} hazard records")
            else:
                print("No hazard records needed enhancement")

        except Exception as e:
            await self.session.rollback()
            print(f"Error enhancing hazard information: {e}")
            raise

    async def generate_usage_patterns(self):
        """Generate usage patterns by analyzing existing product uses"""
        print("Generating usage patterns...")

        try:
            # First, identify all unique crop-target combinations with authorized uses
            result = await self.session.execute(
                select(ProductUse.crop_id, ProductUse.target_id)
                .where(
                    ProductUse.crop_id.isnot(None),
                    ProductUse.target_id.isnot(None),
                    ProductUse.is_currently_authorized == True
                )
                .distinct()
            )
            crop_target_pairs = result.fetchall()

            print(f"Found {len(crop_target_pairs)} unique crop-target combinations")

            # For each combination, calculate patterns
            for crop_id, target_id in crop_target_pairs:
                # First check if a pattern already exists for this combination
                result = await self.session.execute(
                    select(UsagePattern)
                    .where(
                        UsagePattern.crop_id == crop_id,
                        UsagePattern.target_id == target_id
                    )
                )
                existing_pattern = result.scalars().first()

                # Get all authorized uses for this crop-target pair
                result = await self.session.execute(
                    select(ProductUse)
                    .where(
                        ProductUse.crop_id == crop_id,
                        ProductUse.target_id == target_id,
                        ProductUse.is_currently_authorized == True
                    )
                )
                uses = result.scalars().all()

                if not uses:
                    continue

                # Calculate average values
                doses = [u.min_dose for u in uses if u.min_dose is not None]
                max_applications = [u.max_applications for u in uses if u.max_applications is not None]
                harvest_intervals = [u.harvest_interval_days for u in uses if u.harvest_interval_days is not None]

                avg_dose = sum(doses) / len(doses) if doses else None
                avg_max_applications = sum(max_applications) / len(max_applications) if max_applications else None
                avg_harvest_interval = sum(harvest_intervals) / len(harvest_intervals) if harvest_intervals else None

                # Determine common application timing
                application_times = []
                for use in uses:
                    if use.application_season_min:
                        application_times.append(use.application_season_min)
                    if use.application_season_max:
                        application_times.append(use.application_season_max)

                common_timing = None
                if application_times:
                    # Use the most common timing
                    timing_counter = Counter(application_times)
                    common_timing = timing_counter.most_common(1)[0][0]

                # Get authorized product count
                authorized_product_count = len(set(u.product_id for u in uses))

                # Calculate common dose unit
                dose_units = [u.dose_unit for u in uses if u.dose_unit]
                common_dose_unit = None
                if dose_units:
                    unit_counter = Counter(dose_units)
                    common_dose_unit = unit_counter.most_common(1)[0][0]

                # Generate more comprehensive insights
                insights = []
                if avg_dose is not None and common_dose_unit:
                    insights.append(f"Dose moyenne: {avg_dose:.2f} {common_dose_unit}")
                if avg_max_applications is not None:
                    insights.append(f"{avg_max_applications:.1f} applications max. recommandées")
                if avg_harvest_interval is not None:
                    insights.append(f"DAR moyen: {avg_harvest_interval:.1f} jours")
                if common_timing:
                    insights.append(f"Application en {common_timing}")

                # Collect common application methods
                application_parts = [u.application_part for u in uses if u.application_part]
                if application_parts:
                    part_counter = Counter(application_parts)
                    common_part = part_counter.most_common(1)[0][0]
                    insights.append(f"Appliqué sur {common_part}")

                # Add buffer zone information if available
                buffer_zones = [u.max_buffer_zone for u in uses if u.max_buffer_zone is not None]
                if buffer_zones:
                    avg_buffer = sum(buffer_zones) / len(buffer_zones)
                    insights.append(f"ZNT moyenne: {avg_buffer:.1f}m")

                # Add product information
                if authorized_product_count > 1:
                    insights.append(f"{authorized_product_count} produits autorisés")

                pattern_insights = " | ".join(insights)

                if existing_pattern:
                    # Update existing pattern
                    existing_pattern.avg_dose = avg_dose
                    existing_pattern.avg_max_applications = avg_max_applications
                    existing_pattern.common_application_timing = common_timing
                    existing_pattern.avg_harvest_interval = avg_harvest_interval
                    existing_pattern.authorized_product_count = authorized_product_count
                    existing_pattern.pattern_insights = pattern_insights
                else:
                    # Create new pattern
                    pattern = UsagePattern(
                        crop_id=crop_id,
                        target_id=target_id,
                        avg_dose=avg_dose,
                        avg_max_applications=avg_max_applications,
                        common_application_timing=common_timing,
                        avg_harvest_interval=avg_harvest_interval,
                        authorized_product_count=authorized_product_count,
                        pattern_insights=pattern_insights
                    )
                    self.session.add(pattern)

            await self.session.commit()

            # Count generated patterns
            result = await self.session.execute(select(func.count()).select_from(UsagePattern))
            pattern_count = result.scalar()
            print(f"Generated/updated {pattern_count} usage patterns")

        except Exception as e:
            await self.session.rollback()
            print(f"Error generating usage patterns: {e}")
            raise

    # Need to rethink weather this is necessary
    async def update_product_summaries(self):
        """Update product summaries with more comprehensive information"""
        print("Updating product summaries...")

        try:
            # Get products that need summary updates
            result = await self.session.execute(
                select(Product)
                # .where(
                #     or_(
                #         Product.product_summary.is_(None),
                #         Product.product_summary == ''
                #     )
                # )
            )
            products = result.scalars().all()

            # Process products in batches
            for product in products:
                # Get active substances for this product
                result = await self.session.execute(
                    select(ActiveSubstance)
                    .join(ProductSubstance)
                    .where(ProductSubstance.product_id == product.id)
                )
                substances = result.scalars().all()
                substance_names = [s.name for s in substances]

                # Get usage count
                result = await self.session.execute(
                    select(func.count(ProductUse.id))
                    .where(ProductUse.product_id == product.id)
                )
                usage_count = result.scalar()

                # Get authorized usage count
                result = await self.session.execute(
                    select(func.count(ProductUse.id))
                    .where(
                        ProductUse.product_id == product.id,
                        ProductUse.is_currently_authorized == True
                    )
                )
                authorized_usage_count = result.scalar()

                # Get top crops this product is used on
                result = await self.session.execute(
                    select(Crop.crop_name)
                    .join(ProductUse)
                    .where(ProductUse.product_id == product.id)
                    .group_by(Crop.id, Crop.crop_name)
                    .order_by(func.count(ProductUse.id).desc())
                    .limit(3)
                )
                top_crops = result.fetchall()
                top_crop_names = [crop[0] for crop in top_crops]

                # Get top targets
                result = await self.session.execute(
                    select(Target.target_name)
                    .join(ProductUse)
                    .where(ProductUse.product_id == product.id)
                    .group_by(Target.id, Target.target_name)
                    .order_by(func.count(ProductUse.id).desc())
                    .limit(2)
                )
                top_targets = result.fetchall()
                top_target_names = [target[0] for target in top_targets]

                # Generate a more comprehensive summary
                summary_parts = []

                # Basic product info
                summary_parts.append(f"{product.product_name} ({product.registration_number})")

                # Add holder
                if product.holder:
                    summary_parts.append(f"Titulaire: {product.holder}")

                # Add function/type
                if product.function_category:
                    summary_parts.append(f"Type: {product.function_category}")

                # Add formulation
                if product.formulation_type:
                    summary_parts.append(f"Formulation: {product.formulation_type}")

                # Add substances
                if substance_names:
                    if len(substance_names) == 1:
                        summary_parts.append(f"Contient: {substance_names[0]}")
                    else:
                        summary_parts.append(f"Contient: {', '.join(substance_names[:3])}")
                        if len(substance_names) > 3:
                            summary_parts[-1] += f" et {len(substance_names) - 3} autres"

                # Add main crops if available
                if top_crop_names:
                    summary_parts.append(f"Cultures: {', '.join(top_crop_names)}")

                # Add main targets if available
                if top_target_names:
                    summary_parts.append(f"Cibles: {', '.join(top_target_names)}")

                # Add usage info
                if usage_count:
                    if authorized_usage_count > 0:
                        summary_parts.append(f"{authorized_usage_count}/{usage_count} usages autorisés")
                    else:
                        summary_parts.append(f"{usage_count} usages")

                # Add authorization status
                if product.is_currently_authorized:
                    summary_parts.append("Actuellement autorisé")
                else:
                    if product.withdrawal_date:
                        withdrawal_date_str = product.withdrawal_date.strftime("%d/%m/%Y")
                        summary_parts.append(f"Retiré le {withdrawal_date_str}")
                    else:
                        summary_parts.append("Non autorisé")

                # Combine all parts
                product.product_summary = " | ".join(summary_parts)

            if products:
                await self.session.commit()
                print(f"Updated summaries for {len(products)} products")
            else:
                print("No products needed summary updates")

        except Exception as e:
            await self.session.rollback()
            print(f"Error updating product summaries: {e}")
            raise

    async def extract_crop_synonyms(self):
        """Extract common synonyms for crops from usage data"""
        print("Extracting crop synonyms...")

        try:
            # Get crops without synonyms
            result = await self.session.execute(
                select(Crop).where(
                    or_(
                        Crop.common_synonyms.is_(None),
                        Crop.common_synonyms == ''
                    )
                )
            )
            crops = result.scalars().all()

            # Process in batches for better performance
            for crop in crops:
                # Get all uses for this crop
                result = await self.session.execute(
                    select(ProductUse).where(ProductUse.crop_id == crop.id)
                )
                uses = result.scalars().all()

                if not uses:
                    continue

                # Extract potential synonyms from descriptions
                potential_synonyms = set()
                for use in uses:
                    if use.usage_description:
                        # Extract words/phrases that might be synonyms
                        for phrase_length in [1, 2, 3]:  # Try 1, 2, and 3-word phrases
                            words = use.usage_description.split()
                            for i in range(len(words) - phrase_length + 1):
                                phrase = " ".join(words[i:i + phrase_length])
                                # Only consider phrases without stopwords and with sufficient length
                                if all(word.lower() not in self.french_stopwords for word in phrase.split()) and len(
                                        phrase) > 3:
                                    potential_synonyms.add(phrase)

                    if use.application_comments:
                        for phrase_length in [1, 2, 3]:
                            comment_words = use.application_comments.split()
                            for i in range(len(comment_words) - phrase_length + 1):
                                phrase = " ".join(comment_words[i:i + phrase_length])
                                if all(word.lower() not in self.french_stopwords for word in phrase.split()) and len(
                                        phrase) > 3:
                                    potential_synonyms.add(phrase)

                # Count frequency and keep only common ones
                synonym_counter = Counter()
                for syn in potential_synonyms:
                    count = 0
                    for use in uses:
                        if use.usage_description and syn in use.usage_description:
                            count += 1
                        if use.application_comments and syn in use.application_comments:
                            count += 1
                    # Add the crop name itself to potential synonyms to find variations
                    crop_name_parts = crop.crop_name.lower().split()
                    for part in crop_name_parts:
                        if part.lower() in syn.lower() and part.lower() not in self.french_stopwords and len(part) > 3:
                            count += 1
                    synonym_counter[syn] = count

                # Keep only synonyms that appear multiple times and filter obvious matches with crop name
                common_synonyms = []
                for syn, count in synonym_counter.most_common(10):
                    if count > 1 and syn.lower() != crop.crop_name.lower() and len(syn) > 3:
                        # Check that the synonym isn't just part of the crop name
                        crop_name_lower = crop.crop_name.lower()
                        if not (syn.lower() in crop_name_lower or crop_name_lower in syn.lower()):
                            common_synonyms.append(syn)

                # Limit to top 5 synonyms
                common_synonyms = common_synonyms[:5]

                if common_synonyms:
                    crop.common_synonyms = ", ".join(common_synonyms)

            await self.session.commit()
            print(f"Extracted synonyms for {len(crops)} crops")

        except Exception as e:
            await self.session.rollback()
            print(f"Error extracting crop synonyms: {e}")
            raise

    async def extract_target_synonyms(self):
        """Extract common synonyms for targets from usage data"""
        print("Extracting target synonyms...")

        try:
            # Get targets without synonyms
            result = await self.session.execute(
                select(Target).where(
                    or_(
                        Target.common_synonyms.is_(None),
                        Target.common_synonyms == ''
                    )
                )
            )
            targets = result.scalars().all()

            # Process in batches for better performance
            for target in targets:
                # Get all uses for this target
                result = await self.session.execute(
                    select(ProductUse).where(ProductUse.target_id == target.id)
                )
                uses = result.scalars().all()

                if not uses:
                    continue

                # Extract potential synonyms from descriptions
                potential_synonyms = set()
                for use in uses:
                    if use.usage_description:
                        # Extract words/phrases that might be synonyms
                        for phrase_length in [1, 2, 3]:  # Try 1, 2, and 3-word phrases
                            words = use.usage_description.split()
                            for i in range(len(words) - phrase_length + 1):
                                phrase = " ".join(words[i:i + phrase_length])
                                # Only consider phrases without stopwords and with sufficient length
                                if all(word.lower() not in self.french_stopwords for word in phrase.split()) and len(
                                        phrase) > 3:
                                    potential_synonyms.add(phrase)

                    if use.application_comments:
                        for phrase_length in [1, 2, 3]:
                            comment_words = use.application_comments.split()
                            for i in range(len(comment_words) - phrase_length + 1):
                                phrase = " ".join(comment_words[i:i + phrase_length])
                                if all(word.lower() not in self.french_stopwords for word in phrase.split()) and len(
                                        phrase) > 3:
                                    potential_synonyms.add(phrase)

                # Count frequency and keep only common ones
                synonym_counter = Counter()
                for syn in potential_synonyms:
                    count = 0
                    for use in uses:
                        if use.usage_description and syn in use.usage_description:
                            count += 1
                        if use.application_comments and syn in use.application_comments:
                            count += 1
                    # Add the target name itself to potential synonyms to find variations
                    target_name_parts = target.target_name.lower().split()
                    for part in target_name_parts:
                        if part.lower() in syn.lower() and part.lower() not in self.french_stopwords and len(part) > 3:
                            count += 1
                    synonym_counter[syn] = count

                # Keep only synonyms that appear multiple times and filter obvious matches with target name
                common_synonyms = []
                for syn, count in synonym_counter.most_common(10):
                    if count > 1 and syn.lower() != target.target_name.lower() and len(syn) > 3:
                        # Check that the synonym isn't just part of the target name
                        target_name_lower = target.target_name.lower()
                        if not (syn.lower() in target_name_lower or target_name_lower in syn.lower()):
                            common_synonyms.append(syn)

                # Limit to top 5 synonyms
                common_synonyms = common_synonyms[:5]

                if common_synonyms:
                    target.common_synonyms = ", ".join(common_synonyms)

            await self.session.commit()
            print(f"Extracted synonyms for {len(targets)} targets")

        except Exception as e:
            await self.session.rollback()
            print(f"Error extracting target synonyms: {e}")
            raise

    async def generate_seasonal_recommendations(self):
        """Generate seasonal application recommendations for crop-target combinations"""
        print("Generating seasonal recommendations...")

        # Map of French seasons
        season_map = {
            'printemps': 'mars,avril,mai',
            'été': 'juin,juillet,août',
            'automne': 'septembre,octobre,novembre',
            'hiver': 'décembre,janvier,février'
        }

        # Map of crop growth stages
        growth_stage_map = {
            (0, 9): 'germination/levée',
            (10, 19): 'développement des feuilles',
            (20, 29): 'tallage/formation de pousses',
            (30, 39): 'élongation de la tige',
            (40, 49): 'développement des parties végétatives de récolte',
            (50, 59): 'émergence de l\'inflorescence/épiaison',
            (60, 69): 'floraison',
            (70, 79): 'développement du fruit',
            (80, 89): 'maturation',
            (90, 99): 'sénescence'
        }

        try:
            # Get all usage patterns that need recommendations
            result = await self.session.execute(
                select(UsagePattern).where(
                    or_(
                        UsagePattern.pattern_insights.is_(None),
                        ~UsagePattern.pattern_insights.contains("Stades recommandés"),
                        ~UsagePattern.pattern_insights.contains("Saisons optimales")
                    )
                )
            )
            patterns = result.scalars().all()

            for pattern in patterns:
                # Get all uses for this crop-target pair
                result = await self.session.execute(
                    select(ProductUse).where(
                        ProductUse.crop_id == pattern.crop_id,
                        ProductUse.target_id == pattern.target_id,
                        ProductUse.is_currently_authorized == True
                    )
                )
                uses = result.scalars().all()

                if not uses:
                    continue

                # Count growth stages used
                growth_stages = []
                for use in uses:
                    if use.min_growth_stage is not None and use.max_growth_stage is not None:
                        growth_stages.extend(range(use.min_growth_stage, use.max_growth_stage + 1))

                # Get seasonal information
                seasons = []
                for use in uses:
                    if use.application_season_min:
                        seasons.append(use.application_season_min)
                    if use.application_season_max:
                        seasons.append(use.application_season_max)

                # Determine recommended growth stages
                recommended_stages = []
                if growth_stages:
                    stage_counter = Counter(growth_stages)
                    most_common_stages = stage_counter.most_common(3)

                    for stage_num, _ in most_common_stages:
                        for (stage_min, stage_max), stage_desc in growth_stage_map.items():
                            if stage_min <= stage_num <= stage_max:
                                recommended_stages.append(f"BBCH {stage_min}-{stage_max} ({stage_desc})")
                                break

                # Determine recommended seasons
                recommended_seasons = []
                if seasons:
                    season_counter = Counter(seasons)
                    most_common_seasons = season_counter.most_common(2)

                    for season, _ in most_common_seasons:
                        season_lower = season.lower()
                        for season_name, months in season_map.items():
                            if season_name in season_lower:
                                recommended_seasons.append(f"{season_name.capitalize()} ({months})")
                                break
                        else:
                            # If not found in season map, just add the season name
                            recommended_seasons.append(season)

                # Add recommendations to insights if available
                if recommended_stages or recommended_seasons:
                    new_insights = []

                    if recommended_stages:
                        new_insights.append(f"Stades recommandés: {', '.join(recommended_stages[:2])}")

                    if recommended_seasons:
                        new_insights.append(f"Saisons optimales: {', '.join(recommended_seasons[:2])}")

                    # Combine with existing insights
                    current_insights = pattern.pattern_insights or ""
                    if current_insights:
                        pattern.pattern_insights = f"{current_insights} | {' | '.join(new_insights)}"
                    else:
                        pattern.pattern_insights = " | ".join(new_insights)

            if patterns:
                await self.session.commit()
                print(f"Added seasonal recommendations to {len(patterns)} usage patterns")
            else:
                print("No patterns needed seasonal recommendations")

        except Exception as e:
            await self.session.rollback()
            print(f"Error generating seasonal recommendations: {e}")
            raise

    async def enhance_dose_recommendations(self):
        """Enhance dose recommendations with considerations for soil type and climate"""
        print("Enhancing dose recommendations...")

        # Define soil type and climate adjustments (simplified model)
        soil_adjustments = {
            'sable': 0.9,  # Sandy soils might need lower doses
            'argile': 1.2,  # Clay soils might need higher doses
            'limon': 1.0,  # Loamy soils are standard
            'calcaire': 1.1,  # Calcareous soils might need slightly higher doses
            'tourbe': 1.3  # Peaty soils might need higher doses
        }

        climate_adjustments = {
            'sec': 0.9,  # Dry conditions might need lower doses
            'humide': 1.1,  # Humid conditions might need higher doses
            'froid': 0.9,  # Cold conditions might need lower doses (slower degradation)
            'chaud': 1.1  # Hot conditions might need higher doses (faster degradation)
        }

        try:
            # Get all usage patterns that need dose recommendations
            result = await self.session.execute(
                select(UsagePattern).where(
                    UsagePattern.avg_dose.isnot(None),
                    or_(
                        UsagePattern.pattern_insights.is_(None),
                        ~UsagePattern.pattern_insights.contains("Doses/sol"),
                        ~UsagePattern.pattern_insights.contains("Doses/climat")
                    )
                )
            )
            patterns = result.scalars().all()

            for pattern in patterns:
                # Verify crop and target exist
                result = await self.session.execute(select(Crop).where(Crop.id == pattern.crop_id))
                crop = result.scalars().first()

                result = await self.session.execute(select(Target).where(Target.id == pattern.target_id))
                target = result.scalars().first()

                if not crop or not target:
                    continue

                # Generate soil-specific and climate-specific recommendations
                soil_recommendations = []
                climate_recommendations = []

                # Add soil-specific recommendations
                for soil_type, adjustment in soil_adjustments.items():
                    adjusted_dose = pattern.avg_dose * adjustment
                    soil_recommendations.append(f"{soil_type}: {adjusted_dose:.2f}")

                # Add climate-specific recommendations
                for climate_type, adjustment in climate_adjustments.items():
                    adjusted_dose = pattern.avg_dose * adjustment
                    climate_recommendations.append(f"{climate_type}: {adjusted_dose:.2f}")

                # Prepare new insights
                new_insights = []

                # Add soil recommendations (limit to 3)
                if len(soil_recommendations) > 0:
                    new_insights.append(f"Doses/sol: {', '.join(soil_recommendations[:3])}")

                # Add climate recommendations (limit to 3)
                if len(climate_recommendations) > 0:
                    new_insights.append(f"Doses/climat: {', '.join(climate_recommendations[:3])}")

                # Combine with existing insights
                if new_insights:
                    current_insights = pattern.pattern_insights or ""
                    if current_insights:
                        pattern.pattern_insights = f"{current_insights} | {' | '.join(new_insights)}"
                    else:
                        pattern.pattern_insights = " | ".join(new_insights)

            if patterns:
                await self.session.commit()
                print(f"Enhanced dose recommendations for {len(patterns)} usage patterns")
            else:
                print("No patterns needed dose recommendation enhancements")

        except Exception as e:
            await self.session.rollback()
            print(f"Error enhancing dose recommendations: {e}")
            raise

    async def create_compatibility_insights(self):
        """Generate insights about product compatibility and tank mixing"""
        print("Creating compatibility insights...")

        # Define compatibility matrix between different product types
        compatibility_matrix = {
            'Herbicide': {'Herbicide': 'variable', 'Fongicide': 'bonne', 'Insecticide': 'bonne',
                          'Régulateur': 'variable', 'Engrais': 'bonne'},
            'Fongicide': {'Herbicide': 'bonne', 'Fongicide': 'bonne', 'Insecticide': 'bonne', 'Régulateur': 'bonne',
                          'Engrais': 'bonne'},
            'Insecticide': {'Herbicide': 'bonne', 'Fongicide': 'bonne', 'Insecticide': 'variable',
                            'Régulateur': 'bonne', 'Engrais': 'bonne'},
            'Régulateur': {'Herbicide': 'variable', 'Fongicide': 'bonne', 'Insecticide': 'bonne',
                           'Régulateur': 'déconseillée', 'Engrais': 'bonne'},
            'Engrais': {'Herbicide': 'bonne', 'Fongicide': 'bonne', 'Insecticide': 'bonne', 'Régulateur': 'bonne',
                        'Engrais': 'bonne'}
        }

        # Map of formulation types and their mixing properties
        formulation_mixing = {
            'SC': 'Bonnes propriétés de mélange. Ajouter en premier dans la cuve.',
            'EC': 'Bonnes propriétés de mélange. Ajouter après les SC.',
            'WP': 'Peut former des grumeaux. Pré-mélanger avant d\'ajouter au réservoir.',
            'WG': 'Bonne dispersion. Ajouter après les SC et avant les EC.',
            'SL': 'Excellent pour les mélanges. Ajouter après les WG/WP.',
            'OD': 'Vérifier compatibilité. Risque de séparation avec certains produits.',
            'CS': 'Bonne compatibilité générale. Ajouter après les WG et avant les EC.'
        }

        try:
            # Get products that need compatibility insights
            result = await self.session.execute(
                select(Product).where(
                    Product.function_category.isnot(None),
                    or_(
                        Product.product_summary.is_(None),
                        ~Product.product_summary.contains("Compatible avec")
                    )
                )
            )
            products = result.scalars().all()

            for product in products:
                # Find primary function (Herbicide, Fongicide, etc.)
                primary_function = None
                for function in ['Herbicide', 'Fongicide', 'Insecticide', 'Régulateur']:
                    if function.lower() in product.function_category.lower():
                        primary_function = function
                        break

                if not primary_function:
                    continue

                # Generate compatibility insights
                compatibility_insights = []

                if primary_function in compatibility_matrix:
                    # Add compatibility with other product types
                    compatible_types = []
                    variable_types = []
                    incompatible_types = []

                    for other_type, compatibility in compatibility_matrix[primary_function].items():
                        if compatibility == 'bonne':
                            compatible_types.append(other_type)
                        elif compatibility == 'variable':
                            variable_types.append(other_type)
                        else:
                            incompatible_types.append(other_type)

                    if compatible_types:
                        compatibility_insights.append(f"Compatible avec: {', '.join(compatible_types)}")
                    if variable_types:
                        compatibility_insights.append(f"Compatibilité variable: {', '.join(variable_types)}")
                    if incompatible_types:
                        compatibility_insights.append(f"Déconseillé avec: {', '.join(incompatible_types)}")

                # Add formulation-specific mixing advice
                if product.formulation_type:
                    for formulation_code, advice in formulation_mixing.items():
                        if formulation_code in product.formulation_type:
                            compatibility_insights.append(f"Formulation {formulation_code}: {advice}")
                            break

                # Add to product summary if insights are available
                if compatibility_insights:
                    compatibility_text = " | ".join(compatibility_insights)
                    current_summary = product.product_summary or ""

                    # Only add if not already present
                    if "Compatible avec" not in current_summary:
                        if current_summary:
                            product.product_summary = f"{current_summary} | {compatibility_text}"
                        else:
                            product.product_summary = compatibility_text

            if products:
                await self.session.commit()
                print(f"Added compatibility insights to {len(products)} products")
            else:
                print("No products needed compatibility insights")

        except Exception as e:
            await self.session.rollback()
            print(f"Error creating compatibility insights: {e}")
            raise

    async def generate_organic_farming_suitability(self):
        """Determine suitability for organic farming based on composition and uses"""
        print("Generating organic farming suitability data...")

        # Define list of substances commonly allowed in organic farming
        organic_allowed_substances = [
            'cuivre', 'soufre', 'bacillus', 'spinosad', 'azadirachtine', 'pyrèthre',
            'huile', 'argile', 'bicarbonate', 'phosphate', 'potassium', 'calcium',
            'magnésium', 'fer', 'zinc', 'manganèse', 'bore', 'silice'
        ]

        # Define keywords that indicate non-organic products
        non_organic_keywords = [
            'glyphosate', 'pendiméthaline', 's-métolachlore', 'bentazone', 'bromoxynil',
            'mésotrione', 'métazachlore', 'diflufénican', 'prosulfocarbe', 'nicosulfuron'
        ]

        try:
            # Get products without organic farming suitability info
            result = await self.session.execute(
                select(Product).where(
                    or_(
                        Product.product_summary.is_(None),
                        ~Product.product_summary.contains("agriculture biologique")
                    )
                )
            )
            products = result.scalars().all()

            for product in products:
                # Get active substances for this product
                result = await self.session.execute(
                    select(ActiveSubstance)
                    .join(ProductSubstance)
                    .where(ProductSubstance.product_id == product.id)
                )
                substances = result.scalars().all()
                substance_names = [s.name.lower() for s in substances]

                # Determine if potentially suitable for organic farming
                is_potentially_organic = False

                # Check if contains allowed substances
                if substance_names:
                    # Check if any substance is in the allowed list
                    organic_matches = sum(
                        1 for allowed in organic_allowed_substances if any(allowed in s for s in substance_names))

                    # Check if any substance is in the non-organic list
                    non_organic_matches = sum(
                        1 for non_organic in non_organic_keywords if any(non_organic in s for s in substance_names))

                    # If has organic substances and no non-organic substances, consider potentially organic
                    if organic_matches > 0 and non_organic_matches == 0:
                        is_potentially_organic = True

                # Update product summary with organic suitability
                if is_potentially_organic:
                    current_summary = product.product_summary or ""
                    organic_text = "Potentiellement utilisable en agriculture biologique (à vérifier)"

                    # Only add if not already present
                    if "agriculture biologique" not in current_summary:
                        if current_summary:
                            product.product_summary = f"{current_summary} | {organic_text}"
                        else:
                            product.product_summary = organic_text

            if products:
                await self.session.commit()
                print(f"Added organic farming suitability information to {len(products)} products")
            else:
                print("No products needed organic farming suitability information")

        except Exception as e:
            await self.session.rollback()
            print(f"Error generating organic farming suitability: {e}")
            raise
