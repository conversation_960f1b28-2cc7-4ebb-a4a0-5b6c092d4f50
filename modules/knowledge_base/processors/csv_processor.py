"""
CSV processor for structured agricultural data files.
Handles CSV files with proper header detection and data interpretation.
"""
import csv
import io
from typing import List, Dict, Any, Optional
from uuid import UUID

from modules.knowledge_base.processors.base_processor import BaseDocumentProcessor, ProcessingResult
from utils.logger import logger


class CSVProcessor(BaseDocumentProcessor):
    """Processor for CSV files containing agricultural data."""
    
    def __init__(self):
        super().__init__()
        self.supported_extensions = {'.csv'}
    
    async def process(self, file_content: bytes, document_id: UUID) -> ProcessingResult:
        """
        Process CSV file and convert to readable text format.
        
        Args:
            file_content: CSV file content as bytes
            document_id: Document ID for reference
            
        Returns:
            ProcessingResult with structured text representation
        """
        try:
            # Decode CSV content
            try:
                text_content = file_content.decode('utf-8')
            except UnicodeDecodeError:
                text_content = file_content.decode('latin-1', errors='replace')
            
            # Parse CSV
            csv_reader = csv.reader(io.StringIO(text_content))
            rows = list(csv_reader)
            
            if not rows:
                return ProcessingResult(
                    success=False,
                    error="CSV file is empty"
                )
            
            # Process CSV data
            headers = rows[0] if rows else []
            data_rows = rows[1:] if len(rows) > 1 else []
            
            # Convert to readable text format
            readable_text = self._convert_csv_to_text(headers, data_rows)
            
            # Extract metadata from CSV structure
            title = self._extract_csv_title(headers)
            agricultural_metadata = await self._extract_agricultural_metadata(readable_text)
            
            # Calculate estimated pages (every 50 rows = 1 page)
            estimated_pages = max(1, len(data_rows) // 50)
            
            return ProcessingResult(
                success=True,
                content=readable_text,
                title=title,
                language='en',  # Assume English for CSV data
                total_pages=estimated_pages,
                crop_categories=agricultural_metadata.get('crop_categories', []),
                topics=agricultural_metadata.get('topics', []),
                geographic_scope=agricultural_metadata.get('geographic_scope', []),
                seasonal_relevance=agricultural_metadata.get('seasonal_relevance', []),
                metadata={
                    'processor': 'CSVProcessor',
                    'total_rows': len(rows),
                    'total_columns': len(headers),
                    'headers': headers,
                    'data_preview': data_rows[:5] if data_rows else []
                }
            )
            
        except Exception as e:
            logger.error(f"Error processing CSV document {document_id}: {e}")
            return ProcessingResult(
                success=False,
                error=f"CSV processing error: {str(e)}"
            )
    
    def _convert_csv_to_text(self, headers: List[str], data_rows: List[List[str]]) -> str:
        """Convert CSV data to readable text format."""
        text_parts = []
        
        # Add headers description
        if headers:
            text_parts.append(f"Data columns: {', '.join(headers)}")
            text_parts.append("")
        
        # Add sample data description
        text_parts.append("Data summary:")
        
        # Create summary for each column
        for i, header in enumerate(headers):
            if i < len(data_rows[0]) if data_rows else False:
                # Sample values from this column
                column_values = [row[i] for row in data_rows[:10] if i < len(row)]
                unique_values = list(set(column_values))[:5]  # First 5 unique values
                
                text_parts.append(f"{header}: {', '.join(unique_values)}")
        
        text_parts.append("")
        
        # Add full data in readable format (limited to first 100 rows for performance)
        text_parts.append("Data records:")
        for i, row in enumerate(data_rows[:100]):
            if len(row) >= len(headers):
                record_text = []
                for j, header in enumerate(headers):
                    if j < len(row):
                        record_text.append(f"{header}: {row[j]}")
                
                text_parts.append(f"Record {i+1}: {' | '.join(record_text)}")
        
        if len(data_rows) > 100:
            text_parts.append(f"... and {len(data_rows) - 100} more records")
        
        return '\n'.join(text_parts)
    
    def _extract_csv_title(self, headers: List[str]) -> Optional[str]:
        """Extract a meaningful title from CSV headers."""
        if not headers:
            return None
        
        # Try to create a descriptive title from headers
        agricultural_terms = ['crop', 'yield', 'farm', 'field', 'harvest', 'plant', 'soil', 'weather']
        
        relevant_headers = []
        for header in headers[:5]:  # Look at first 5 headers
            header_lower = header.lower()
            if any(term in header_lower for term in agricultural_terms):
                relevant_headers.append(header)
        
        if relevant_headers:
            return f"Agricultural Data: {', '.join(relevant_headers)}"
        
        return f"Data Table: {', '.join(headers[:3])}"  # First 3 headers