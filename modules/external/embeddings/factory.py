from typing import Dict, Type, Optional, Any, List
from .abc_adapter import EmbeddingA<PERSON>pter
from .types import EmbeddingConfig


class EmbeddingAdapterFactory:
    """Factory for creating embedding adapters with lazy loading."""
    
    # Store module paths instead of classes for lazy loading
    _adapter_modules: Dict[str, str] = {
        'openai': 'modules.external.embeddings.openai_adapter:OpenAIEmbeddingAdapter',
        'huggingface': 'modules.external.embeddings.huggingface_adapter:HuggingFaceEmbeddingAdapter',
        'hf': 'modules.external.embeddings.huggingface_adapter:HuggingFaceEmbeddingAdapter',  # Alias
    }
    
    # Cache loaded classes
    _loaded_adapters: Dict[str, Type[EmbeddingAdapter]] = {}
    
    @classmethod
    def _load_adapter_class(cls, provider: str) -> Type[EmbeddingAdapter]:
        """Lazy load adapter class."""
        if provider in cls._loaded_adapters:
            return cls._loaded_adapters[provider]
        
        if provider not in cls._adapter_modules:
            raise ValueError(f"Unknown provider: {provider}")
        
        module_path = cls._adapter_modules[provider]
        module_name, class_name = module_path.split(':')
        
        try:
            # Import module dynamically
            import importlib
            module = importlib.import_module(module_name)
            adapter_class = getattr(module, class_name)
            
            # Cache the loaded class only if successful
            cls._loaded_adapters[provider] = adapter_class
            return adapter_class
        except ImportError as e:
            # Don't cache failed imports - let them be retried
            raise ImportError(f"Failed to load {provider} adapter: {e}")
        except Exception as e:
            # Don't cache other failures either
            raise Exception(f"Failed to load {provider} adapter: {e}")
    
    @classmethod
    def register_adapter(cls, name: str, module_path: str) -> None:
        """Register a new adapter type with module path."""
        cls._adapter_modules[name] = module_path
    
    @classmethod
    def get_available_providers(cls) -> List[str]:
        """Get list of available provider names."""
        return list(cls._adapter_modules.keys())
    
    @classmethod
    def create_adapter(
        cls, 
        provider: str, 
        model: str,
        api_key: Optional[str] = None,
        **kwargs
    ) -> EmbeddingAdapter:
        """
        Create an embedding adapter.
        
        Args:
            provider: Provider name (openai, huggingface, etc.)
            model: Model name/path
            api_key: API key if required
            **kwargs: Additional configuration
        
        Returns:
            EmbeddingAdapter instance
        
        Raises:
            ValueError: If provider is not supported
        """
        provider = provider.lower()
        
        # Lazy load the adapter class
        adapter_class = cls._load_adapter_class(provider)
        
        config = EmbeddingConfig(
            model=model,
            api_key=api_key,
            extra_config=kwargs if kwargs else None
        )
        
        return adapter_class(config)


# Pre-configured model configurations  
POPULAR_MODELS = {
    "openai": {
        "small": "text-embedding-3-small",
        "large": "text-embedding-3-large", 
        "ada": "text-embedding-ada-002"
    },
    "huggingface": {
        "mini": "sentence-transformers/all-MiniLM-L6-v2",
        "small": "sentence-transformers/all-mpnet-base-v2",
        "large": "sentence-transformers/all-roberta-large-v1"
    }
}


# Convenience function for getting model names from presets
def get_model_name(provider: str, size: str = "small") -> str:
    """
    Get model name from preset configurations.
    
    Args:
        provider: Provider name (openai, huggingface)
        size: Model size (small, large, etc.)
    
    Returns:
        Model name string
    """
    if provider not in POPULAR_MODELS:
        raise ValueError(f"Unknown provider preset: {provider}")
    
    if size not in POPULAR_MODELS[provider]:
        available = ', '.join(POPULAR_MODELS[provider].keys())
        raise ValueError(f"Unknown size for {provider}: {size}. Available: {available}")
    
    return POPULAR_MODELS[provider][size]