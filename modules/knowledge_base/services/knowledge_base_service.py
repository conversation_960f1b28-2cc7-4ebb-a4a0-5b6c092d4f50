"""
Knowledge Base Service for comprehensive RAG system.
Handles document processing, chunking, embedding, and retrieval with access control.
"""
import asyncio
import hashlib
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from uuid import UUID

from sqlalchemy import select, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession

from config import CONFIG
from models.dbm import DatabaseManager
from models.knowledge_base import (
    Document, DocumentChunk, SearchStats,
    AccessLevel, DocumentType, ProcessingStatus
)
from modules.external.embeddings.factory import EmbeddingAdapterFactory
from modules.external.minio_adapter import MinIOAdapter
from modules.knowledge_base.chunking.contextual_chunker import ContextualChunker
from modules.knowledge_base.processors.document_processor_factory import DocumentProcessorFactory
from modules.knowledge_base.search.bm25_searcher import ContextualBM25Searcher
from schemas.assistant import SFileInput
from utils.logger import logger


class KnowledgeBaseService:
    """
    Main service for knowledge base operations.
    Coordinates document processing, embedding, and retrieval.
    """

    def __init__(self):
        self.db_manager = DatabaseManager()
        self.minio_adapter = MinIOAdapter()
        self.processor_factory = DocumentProcessorFactory()
        self.chunker = ContextualChunker()

        # Initialize embedding adapter
        self.embedding_adapter = EmbeddingAdapterFactory.create_adapter(
            provider="openai",
            model="text-embedding-3-small",
            api_key=CONFIG.OPENAI_API_KEY
        )

        # Initialize BM25 searcher for hybrid retrieval
        self.bm25_searcher = ContextualBM25Searcher()

    async def process_document(
            self,
            file_input: SFileInput,
            user_id: Optional[str] = None,
            enterprise_id: Optional[str] = None,
            access_level: AccessLevel = AccessLevel.PRIVATE,
            metadata: Optional[Dict[str, Any]] = None
    ) -> Tuple[bool, Optional[UUID], Optional[str]]:
        """
        Process a document from MinIO and store in knowledge base.
        
        Args:
            file_input: SFileInput object with file details
            user_id: User ID from external system
            enterprise_id: Enterprise ID from external system
            access_level: Document access level
            metadata: Additional metadata
            
        Returns:
            Tuple of (success, document_id, error_message)
        """
        try:
            # Extract file path and bucket from SFileInput
            file_path = file_input.object_name
            bucket_name = file_input.bucket_name

            # Get file info from MinIO
            file_info = await self.minio_adapter.get_file_info(file_path, bucket_name)
            if not file_info:
                return False, None, f"File not found in MinIO: {file_path}"

            # Download file content
            file_content = await self.minio_adapter.download_file(file_path, bucket_name)
            if not file_content:
                return False, None, f"Failed to download file: {file_path}"

            # Determine document type
            file_extension = Path(file_path).suffix.lower()
            document_type = self._get_document_type(file_extension)
            await self.db_manager.init_db()
            # Use a single transaction with proper rollback handling
            content_hash = hashlib.sha256(file_content).hexdigest()

            async with self.db_manager.session_scope() as session:
                # Check if document already exists
                existing_doc = await session.execute(
                    select(Document).where(
                        Document.filename == file_path,  # Now filename is the MinIO path

                    )
                )
                existing_doc = existing_doc.scalar_one_or_none()

                if existing_doc and existing_doc.processing_status == ProcessingStatus.COMPLETED.value:
                    logger.info(f"Document already exists: {file_path}")
                    return True, existing_doc.id, None

                if not existing_doc:
                    # Create document record with PENDING status first
                    document = Document(
                        filename=file_path,  # MinIO object path as primary identifier
                        display_name=file_info.get('metadata', {}).get('original_name') or Path(file_path).name,
                        file_size=file_info['size'],
                        content_type=file_info['content_type'],
                        document_type=document_type.value,
                        access_level=access_level.value,
                        user_id=user_id,
                        enterprise_id=enterprise_id,
                        processing_status=ProcessingStatus.PENDING.value,  # Start as PENDING
                        extra_metadata={
                            'content_hash': content_hash,
                            'processing_version': '1.0',
                            **(metadata or {})
                        }
                    )

                    session.add(document)
                    await session.flush()  # Get document ID
                else:
                    document = existing_doc
                try:
                    # Update status to PROCESSING
                    document.processing_status = ProcessingStatus.PROCESSING.value
                    await session.flush()

                    # Process document content (with intelligent AI processor routing)
                    # Check if file qualifies for AI processing
                    if file_content and self.processor_factory.should_use_ai_processor(file_content, document_type):
                        logger.info(f"Routing {document_type} to AI processor (small file detected)")
                        processor =  self.processor_factory.get_ai_processor()
                    else:
                        processor = self.processor_factory.get_processor(document_type)

                    # processor = self.processor_factory.get_ai_processor()
                    #
                    processing_result = await processor.process(file_content, document.id)
                    #
                    # processor = self.processor_factory.get_processor(document_type)
                    #
                    # processing_result1 = await processor.process(file_content, document.id)
                    # print(processing_result1)
                    # print(processing_result)

                    if not processing_result.success:
                        # Mark as failed and set error message
                        document.processing_status = ProcessingStatus.FAILED.value
                        document.error_message = processing_result.error
                        await session.commit()  # Save the failure status
                        return False, None, f"Processing failed: {processing_result.error}"

                    # Update document with processing results
                    document.title = processing_result.title
                    document.language = processing_result.language
                    document.total_pages = processing_result.total_pages
                    document.crop_categories = processing_result.crop_categories
                    document.topics = processing_result.topics
                    document.geographic_scope = processing_result.geographic_scope
                    document.seasonal_relevance = processing_result.seasonal_relevance

                    # Create chunks with contextual enhancement
                    chunks_data = await self._create_contextual_chunks(
                        processing_result.content,
                        document,
                        processing_result
                    )

                    # Generate embeddings and save chunks
                    await self._process_chunks(session, document, chunks_data)

                    # Update document stats and status
                    document.total_chunks = len(chunks_data)
                    document.processing_status = ProcessingStatus.COMPLETED.value

                    await session.commit()

                    logger.info(f"Successfully processed document: {file_path} (ID: {document.id})")
                    return True, document.id, None

                except Exception as processing_error:
                    # If anything fails during processing, mark as failed
                    document.processing_status = ProcessingStatus.FAILED.value
                    document.error_message = str(processing_error)
                    await session.commit()  # Save the failure status
                    raise processing_error

        except Exception as e:
            logger.error(f"Error processing document {file_path if 'file_path' in locals() else 'unknown'}: {e}")
            return False, None, str(e)

    async def batch_process_documents(
            self,
            file_inputs: List[SFileInput],
            user_id: Optional[str] = None,
            enterprise_id: Optional[str] = None,
            access_level: AccessLevel = AccessLevel.PRIVATE,
            batch_size: int = 5
    ) -> Dict[str, Dict[str, Any]]:
        """
        Process multiple documents in batches.
        
        Args:
            file_inputs: List of SFileInput objects
            user_id: User ID from external system
            enterprise_id: Enterprise ID from external system
            access_level: Document access level
            batch_size: Number of documents to process concurrently
            
        Returns:
            Dictionary with processing results for each file
        """
        results = {}

        # Process in batches to avoid overwhelming the system
        for i in range(0, len(file_inputs), batch_size):
            batch = file_inputs[i:i + batch_size]

            # Process batch concurrently
            tasks = [
                self.process_document(
                    file_input=file_input,
                    user_id=user_id,
                    enterprise_id=enterprise_id,
                    access_level=access_level
                )
                for file_input in batch
            ]

            batch_results = await asyncio.gather(*tasks, return_exceptions=True)

            # Store results
            for file_input, result in zip(batch, batch_results):
                # Use object_name as key for results dict
                key = file_input.object_name

                if isinstance(result, Exception):
                    results[key] = {
                        'success': False,
                        'document_id': None,
                        'error': str(result)
                    }
                else:
                    success, doc_id, error = result
                    results[key] = {
                        'success': success,
                        'document_id': str(doc_id) if doc_id else None,
                        'error': error
                    }

            # Small delay between batches
            await asyncio.sleep(0.1)

        return results

    async def search_documents(
            self,
            query: str,
            user_id: Optional[str] = None,
            enterprise_id: Optional[str] = None,
            access_levels: Optional[List[AccessLevel]] = None,
            filters: Optional[Dict[str, Any]] = None,
            limit: int = 10,
            include_chunks: bool = True,
            search_method: str = "hybrid"  # "hybrid", "semantic", "bm25"
    ) -> Dict[str, Any]:
        """
        Search documents using hybrid contextual retrieval
        Combines contextual embeddings with contextual BM25 for improved accuracy.
        
        Args:
            query: Search query
            user_id: User ID for access control
            enterprise_id: Enterprise ID for access control  
            access_levels: Allowed access levels
            filters: Additional filters (crop_categories, topics, etc.)
            limit: Maximum results to return
            include_chunks: Whether to include chunk details
            search_method: Search method ("hybrid", "semantic", "bm25")
            
        Returns:
            Search results with documents and chunks
        """
        start_time = datetime.utcnow()

        try:
            async with self.db_manager.session_scope() as session:
                # Ensure BM25 index is built
                await self._ensure_bm25_index(session)

                # Build access control filters
                access_filters = self._build_access_filters(user_id, enterprise_id, access_levels)

                search_results = []

                if search_method == "hybrid":
                    # Hybrid search combining semantic and BM25
                    search_results = await self._hybrid_search(
                        session, query, access_filters, filters, limit, include_chunks
                    )
                elif search_method == "semantic":
                    # Pure semantic search
                    search_results = await self._semantic_search(
                        session, query, access_filters, filters, limit, include_chunks
                    )
                elif search_method == "bm25":
                    # Pure BM25 search
                    search_results = await self._bm25_search(
                        session, query, access_filters, filters, limit, include_chunks
                    )
                else:
                    raise ValueError(f"Unknown search method: {search_method}")

                # Record search stats
                await self._record_search_stats(
                    session=session,
                    query=query,
                    user_id=user_id,
                    enterprise_id=enterprise_id,
                    total_results=len(search_results),
                    returned_results=len(search_results),
                    retrieval_time=(datetime.utcnow() - start_time).total_seconds() * 1000,
                    filters=filters
                )

                await session.commit()

                return {
                    'query': query,
                    'search_method': search_method,
                    'total_results': len(search_results),
                    'results': search_results,
                    'retrieval_time_ms': (datetime.utcnow() - start_time).total_seconds() * 1000
                }

        except Exception as e:
            logger.error(f"Error searching documents: {e}")
            return {
                'query': query,
                'search_method': search_method,
                'total_results': 0,
                'results': [],
                'error': str(e)
            }

    async def get_document_chunks(
            self,
            document_id: UUID,
            user_id: Optional[str] = None,
            enterprise_id: Optional[str] = None
    ) -> Optional[List[Dict[str, Any]]]:
        """
        Get all chunks for a specific document.
        
        Args:
            document_id: Document ID
            user_id: User ID for access control
            enterprise_id: Enterprise ID for access control
            
        Returns:
            List of document chunks or None if not accessible
        """
        try:
            async with self.db_manager.session_scope() as session:
                # Check document access
                access_filters = self._build_access_filters(user_id, enterprise_id)

                document_query = select(Document).where(
                    and_(
                        Document.id == document_id,
                        *access_filters
                    )
                )
                result = await session.execute(document_query)
                document = result.scalar_one_or_none()

                if not document:
                    return None

                # Get chunks
                chunks_query = (
                    select(DocumentChunk)
                    .where(DocumentChunk.document_id == document_id)
                    .order_by(DocumentChunk.chunk_index)
                )

                chunks_result = await session.execute(chunks_query)
                chunks = chunks_result.scalars().all()

                return [
                    {
                        'chunk_id': str(chunk.id),
                        'chunk_index': chunk.chunk_index,
                        'content': chunk.content,
                        'content_preview': chunk.content_preview,
                        'page_number': chunk.page_number,
                        'context_summary': chunk.context_summary,
                        'detected_crops': chunk.detected_crops,
                        'detected_topics': chunk.detected_topics,
                        'importance_score': chunk.importance_score
                    }
                    for chunk in chunks
                ]

        except Exception as e:
            logger.error(f"Error getting document chunks: {e}")
            return None

    async def delete_document(
            self,
            document_id: UUID,
            user_id: Optional[str] = None,
            enterprise_id: Optional[str] = None,
            force: bool = False
    ) -> Tuple[bool, Optional[str]]:
        """
        Delete a document and all its chunks from the knowledge base.
        
        Args:
            document_id: Document ID to delete
            user_id: User ID for access control
            enterprise_id: Enterprise ID for access control
            force: If True, bypass access control (admin delete)
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            async with self.db_manager.session_scope() as session:
                # Build access control filters if not force delete
                if not force:
                    access_filters = self._build_access_filters(user_id, enterprise_id)
                    
                    # Check document access
                    document_query = select(Document).where(
                        and_(
                            Document.id == document_id,
                            *access_filters
                        )
                    )
                else:
                    # Force delete - no access control
                    document_query = select(Document).where(Document.id == document_id)

                result = await session.execute(document_query)
                document = result.scalar_one_or_none()

                if not document:
                    return False, "Document not found or access denied"

                # Delete all chunks first (due to foreign key constraints)
                chunks_query = select(DocumentChunk).where(DocumentChunk.document_id == document_id)
                chunks_result = await session.execute(chunks_query)
                chunks = chunks_result.scalars().all()
                
                chunk_count = len(chunks)
                for chunk in chunks:
                    await session.delete(chunk)

                # Delete the document
                await session.delete(document)
                
                # Commit the transaction
                await session.commit()

                logger.info(f"Successfully deleted document {document_id} and {chunk_count} chunks")
                return True, None

        except Exception as e:
            logger.error(f"Error deleting document {document_id}: {e}")
            return False, str(e)

    async def delete_documents_by_user(
            self,
            user_id: str,
            enterprise_id: Optional[str] = None
    ) -> Tuple[bool, int, Optional[str]]:
        """
        Delete all documents for a specific user.
        
        Args:
            user_id: User ID whose documents to delete
            enterprise_id: Enterprise ID for additional filtering
            
        Returns:
            Tuple of (success, deleted_count, error_message)
        """
        try:
            async with self.db_manager.session_scope() as session:
                # Build query for user's documents
                query_conditions = [Document.user_id == user_id]
                if enterprise_id:
                    query_conditions.append(Document.enterprise_id == enterprise_id)

                documents_query = select(Document).where(and_(*query_conditions))
                result = await session.execute(documents_query)
                documents = result.scalars().all()

                deleted_count = 0
                total_chunks_deleted = 0

                for document in documents:
                    # Delete chunks first
                    chunks_query = select(DocumentChunk).where(DocumentChunk.document_id == document.id)
                    chunks_result = await session.execute(chunks_query)
                    chunks = chunks_result.scalars().all()
                    
                    for chunk in chunks:
                        await session.delete(chunk)
                        total_chunks_deleted += 1

                    # Delete document
                    await session.delete(document)
                    deleted_count += 1

                await session.commit()

                logger.info(f"Deleted {deleted_count} documents and {total_chunks_deleted} chunks for user {user_id}")
                return True, deleted_count, None

        except Exception as e:
            logger.error(f"Error deleting documents for user {user_id}: {e}")
            return False, 0, str(e)

    # Private helper methods

    def _get_document_type(self, file_extension: str) -> DocumentType:
        """Map file extension to document type."""
        type_mapping = {
            '.pdf': DocumentType.PDF,
            '.docx': DocumentType.DOCX,
            '.doc': DocumentType.DOCX,
            '.txt': DocumentType.TXT,
            '.csv': DocumentType.CSV,
            '.json': DocumentType.JSON,
            '.html': DocumentType.HTML,
            '.htm': DocumentType.HTML,
            '.jpg': DocumentType.IMAGE,
            '.jpeg': DocumentType.IMAGE,
            '.png': DocumentType.IMAGE,
            '.gif': DocumentType.IMAGE,
            '.bmp': DocumentType.IMAGE,
            '.tiff': DocumentType.IMAGE,
            '.webp': DocumentType.IMAGE
        }
        return type_mapping.get(file_extension, DocumentType.OTHER)

    async def _create_contextual_chunks(
            self,
            content: str,
            document: Document,
            processing_result: Any
    ) -> List[Dict[str, Any]]:
        """Create chunks with contextual enhancement."""
        # Basic chunking
        base_chunks = await self.chunker.chunk_text(
            text=content,
            document_context={
                'title': document.title,
                'document_type': document.document_type,
                'file_path': document.filename  # Now filename contains the MinIO path
            }
        )

        # Enhance with contextual information
        contextual_chunks = []
        for i, chunk_data in enumerate(base_chunks):
            enhanced_chunk = {
                'chunk_index': i,
                'content': chunk_data['content'],
                'contextual_content': chunk_data.get('contextual_content'),  # Include contextual content
                'content_preview': chunk_data['content'][:200],
                'content_length': len(chunk_data['content']),
                'chunk_hash': hashlib.sha256(chunk_data['content'].encode()).hexdigest(),
                'page_number': chunk_data.get('page_number'),
                'start_position': chunk_data.get('start_position'),
                'end_position': chunk_data.get('end_position'),
                'context_summary': chunk_data.get('context_summary'),
                'chunk_type': chunk_data.get('chunk_type', 'paragraph'),
                'importance_score': chunk_data.get('importance_score', 0.5)
            }
            contextual_chunks.append(enhanced_chunk)

        return contextual_chunks

    async def _process_chunks(
            self,
            session: AsyncSession,
            document: Document,
            chunks_data: List[Dict[str, Any]]
    ):
        """Process chunks: generate embeddings and save to database."""
        # Extract contextual content for embedding (Anthropic's method)
        chunk_texts = [chunk['contextual_content'] for chunk in chunks_data]

        # Generate embeddings in batch on contextual content
        embedding_result = await self.embedding_adapter.embed_texts(chunk_texts)
        embeddings = embedding_result.embeddings

        # Create chunk records
        for chunk_data, embedding in zip(chunks_data, embeddings):
            chunk = DocumentChunk(
                document_id=document.id,
                chunk_index=chunk_data['chunk_index'],
                chunk_hash=chunk_data['chunk_hash'],
                content=chunk_data['content'],
                contextual_content=chunk_data.get('contextual_content'),  # Store contextual content
                content_preview=chunk_data['content_preview'],
                content_length=chunk_data['content_length'],
                page_number=chunk_data.get('page_number'),
                start_position=chunk_data.get('start_position'),
                end_position=chunk_data.get('end_position'),
                context_summary=chunk_data.get('context_summary'),
                embedding=embedding,
                embedding_model=embedding_result.model,
                embedding_created_at=datetime.utcnow(),
                chunk_type=chunk_data.get('chunk_type'),
                importance_score=chunk_data.get('importance_score')
            )

            session.add(chunk)

    def _build_access_filters(
            self,
            user_id: Optional[str],
            enterprise_id: Optional[str],
            access_levels: Optional[List[AccessLevel]] = None
    ) -> List:
        """Build access control filters for queries."""
        filters = []

        # Default access levels if not specified
        if not access_levels:
            access_levels = [AccessLevel.PUBLIC]
            if user_id:
                access_levels.append(AccessLevel.PRIVATE)
            if enterprise_id:
                access_levels.append(AccessLevel.ENTERPRISE)

        # Access level filter
        access_conditions = [Document.access_level == level.value for level in access_levels]

        # Additional conditions for private/enterprise access
        if AccessLevel.PRIVATE in access_levels and user_id:
            access_conditions.append(
                and_(
                    Document.access_level == AccessLevel.PRIVATE.value,
                    Document.user_id == user_id
                )
            )

        if AccessLevel.ENTERPRISE in access_levels and enterprise_id:
            access_conditions.append(
                and_(
                    Document.access_level == AccessLevel.ENTERPRISE.value,
                    Document.enterprise_id == enterprise_id
                )
            )

        filters.append(or_(*access_conditions))

        return filters

    def _apply_search_filters(self, query, filters: Dict[str, Any]):
        """Apply additional search filters to query."""
        if 'crop_categories' in filters:
            query = query.where(Document.crop_categories.overlap(filters['crop_categories']))

        if 'topics' in filters:
            query = query.where(Document.topics.overlap(filters['topics']))

        if 'document_types' in filters:
            query = query.where(Document.document_type.in_(filters['document_types']))

        if 'date_range' in filters:
            date_range = filters['date_range']
            if 'start' in date_range:
                query = query.where(Document.created_at >= date_range['start'])
            if 'end' in date_range:
                query = query.where(Document.created_at <= date_range['end'])

        return query

    async def _record_search_stats(
            self,
            session: AsyncSession,
            query: str,
            user_id: Optional[str],
            enterprise_id: Optional[str],
            total_results: int,
            returned_results: int,
            retrieval_time: float,
            filters: Optional[Dict[str, Any]]
    ):
        """Record search statistics for analytics."""
        query_hash = hashlib.sha256(query.encode()).hexdigest()

        stats = SearchStats(
            query_text=query,
            query_hash=query_hash,
            user_id=user_id,
            enterprise_id=enterprise_id,
            search_filters=filters,
            total_results_found=total_results,
            results_returned=returned_results,
            retrieval_time_ms=int(retrieval_time)
        )

        session.add(stats)

    # Hybrid search methods for Anthropic's contextual retrieval

    async def _ensure_bm25_index(self, session: AsyncSession):
        """Ensure BM25 index is built and ready."""
        try:
            if not await self.bm25_searcher.build_index(session):
                logger.warning("BM25 index build failed, hybrid search will fall back to semantic only")
        except Exception as e:
            logger.error(f"Error ensuring BM25 index: {e}")

    async def _hybrid_search(
            self,
            session: AsyncSession,
            query: str,
            access_filters: List,
            filters: Optional[Dict[str, Any]],
            limit: int,
            include_chunks: bool
    ) -> List[Dict[str, Any]]:
        """
        Hybrid search combining contextual embeddings and contextual BM25.
        Following Anthropic's method for improved retrieval accuracy.
        """
        try:
            # Get semantic search results
            semantic_results = await self._semantic_search(
                session, query, access_filters, filters, limit * 2, include_chunks
            )

            # Get BM25 search results
            bm25_results = await self._bm25_search(
                session, query, access_filters, filters, limit * 2, include_chunks
            )

            # Combine and rerank results
            combined_results = self._combine_search_results(
                semantic_results, bm25_results, limit
            )

            return combined_results

        except Exception as e:
            logger.error(f"Error in hybrid search: {e}")
            # Fallback to semantic search only
            return await self._semantic_search(
                session, query, access_filters, filters, limit, include_chunks
            )

    async def _semantic_search(
            self,
            session: AsyncSession,
            query: str,
            access_filters: List,
            filters: Optional[Dict[str, Any]],
            limit: int,
            include_chunks: bool
    ) -> List[Dict[str, Any]]:
        """Pure semantic search using contextual embeddings."""
        try:
            # Generate query embedding
            embedding_result = await self.embedding_adapter.embed_texts([query])
            query_embedding = embedding_result.embeddings[0]

            # Semantic search on chunks using vector distance
            semantic_query = (
                select(
                    DocumentChunk,
                    Document,
                    DocumentChunk.embedding.l2_distance(query_embedding).label('distance')
                )
                .join(Document, DocumentChunk.document_id == Document.id)
                .where(
                    and_(
                        DocumentChunk.embedding.isnot(None),
                        *access_filters
                    )
                )
                .order_by('distance')
                .limit(limit)
            )

            # Apply additional filters
            if filters:
                semantic_query = self._apply_search_filters(semantic_query, filters)

            results = await session.execute(semantic_query)
            chunk_results = results.all()

            # Process results
            search_results = []
            for chunk, document, distance in chunk_results:
                result_item = {
                    'document_id': str(document.id),
                    'document_title': document.display_name or document.title,  # Use display_name if available
                    'document_type': document.document_type,
                    'relevance_score': 1.0 - min(distance, 1.0),  # Convert distance to similarity
                    'search_method': 'semantic',
                    'access_level': document.access_level
                }

                if include_chunks:
                    result_item.update({
                        'chunk_id': str(chunk.id),
                        'chunk_content': chunk.content,
                        'chunk_preview': chunk.content_preview,
                        'page_number': chunk.page_number,
                        'context_summary': chunk.context_summary
                    })

                search_results.append(result_item)

            return search_results

        except Exception as e:
            logger.error(f"Error in semantic search: {e}")
            return []

    async def _bm25_search(
            self,
            session: AsyncSession,
            query: str,
            access_filters: List,
            filters: Optional[Dict[str, Any]],
            limit: int,
            include_chunks: bool
    ) -> List[Dict[str, Any]]:
        """Pure BM25 search using contextual content."""
        try:
            # Get BM25 results
            bm25_results = self.bm25_searcher.search(query, limit=limit)

            if not bm25_results:
                return []

            # Convert BM25 results to chunk IDs and get document info
            chunk_ids = [result.chunk_id for result in bm25_results]

            # Get chunks and documents from database with access control
            chunks_query = (
                select(DocumentChunk, Document)
                .join(Document, DocumentChunk.document_id == Document.id)
                .where(
                    and_(
                        DocumentChunk.id.in_(chunk_ids),
                        *access_filters
                    )
                )
            )

            # Apply additional filters
            if filters:
                chunks_query = self._apply_search_filters(chunks_query, filters)

            db_results = await session.execute(chunks_query)
            chunk_data = {str(chunk.id): (chunk, document) for chunk, document in db_results.all()}

            # Build search results maintaining BM25 order and scores
            search_results = []
            for bm25_result in bm25_results:
                if bm25_result.chunk_id in chunk_data:
                    chunk, document = chunk_data[bm25_result.chunk_id]

                    result_item = {
                        'document_id': str(document.id),
                        'document_title': document.display_name or document.title,  # Use display_name if available
                        'document_type': document.document_type,
                        'relevance_score': min(bm25_result.score / 10.0, 1.0),  # Normalize BM25 score
                        'search_method': 'bm25',
                        'access_level': document.access_level
                    }

                    if include_chunks:
                        result_item.update({
                            'chunk_id': str(chunk.id),
                            'chunk_content': chunk.content,
                            'chunk_preview': chunk.content_preview,
                            'page_number': chunk.page_number,
                            'context_summary': chunk.context_summary
                        })

                    search_results.append(result_item)

            return search_results

        except Exception as e:
            logger.error(f"Error in BM25 search: {e}")
            return []

    def _combine_search_results(
            self,
            semantic_results: List[Dict[str, Any]],
            bm25_results: List[Dict[str, Any]],
            limit: int
    ) -> List[Dict[str, Any]]:
        """
        Combine semantic and BM25 results using reciprocal rank fusion (RRF).
        This is a common method for combining different ranking systems.
        """
        try:
            # Create mapping of chunk_id to results
            all_results = {}

            # Add semantic results with their ranks
            for rank, result in enumerate(semantic_results):
                chunk_id = result.get('chunk_id')
                if chunk_id:
                    all_results[chunk_id] = {
                        **result,
                        'semantic_rank': rank + 1,
                        'semantic_score': result['relevance_score'],
                        'bm25_rank': None,
                        'bm25_score': 0.0
                    }

            # Add BM25 results and update existing entries
            for rank, result in enumerate(bm25_results):
                chunk_id = result.get('chunk_id')
                if chunk_id:
                    if chunk_id in all_results:
                        # Update existing result with BM25 info
                        all_results[chunk_id]['bm25_rank'] = rank + 1
                        all_results[chunk_id]['bm25_score'] = result['relevance_score']
                    else:
                        # Add new result from BM25 only
                        all_results[chunk_id] = {
                            **result,
                            'semantic_rank': None,
                            'semantic_score': 0.0,
                            'bm25_rank': rank + 1,
                            'bm25_score': result['relevance_score']
                        }

            # Calculate RRF scores (Reciprocal Rank Fusion)
            k = 60  # RRF parameter (common value)
            for chunk_id, result in all_results.items():
                rrf_score = 0.0

                # Add semantic contribution
                if result['semantic_rank'] is not None:
                    rrf_score += 1.0 / (k + result['semantic_rank'])

                # Add BM25 contribution
                if result['bm25_rank'] is not None:
                    rrf_score += 1.0 / (k + result['bm25_rank'])

                # Store combined score
                result['relevance_score'] = rrf_score
                result['search_method'] = 'hybrid'

            # Sort by combined RRF score and return top results
            combined_results = sorted(
                all_results.values(),
                key=lambda x: x['relevance_score'],
                reverse=True
            )

            return combined_results[:limit]

        except Exception as e:
            logger.error(f"Error combining search results: {e}")
            # Fallback to semantic results only
            return semantic_results[:limit]
