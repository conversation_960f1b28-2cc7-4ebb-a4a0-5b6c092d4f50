"""
Example usage of the embedding adapter system.
"""
import asyncio
import os

# Import the embedding system
from . import (
    EmbeddingAdapterFactory,
    create_adapter_from_preset,
    EmbeddingConfig,
    POPULAR_MODELS
)


async def basic_usage_example():
    """Basic usage example with OpenAI."""
    print("=== Basic Usage Example ===")
    
    # Create adapter using factory
    adapter = EmbeddingAdapterFactory.create_adapter(
        provider="openai",
        model="text-embedding-3-small",
        api_key=os.getenv("OPENAI_API_KEY")
    )
    
    # Embed some texts
    texts = [
        "Hello world",
        "How are you?", 
        "Python is great",
        "Machine learning is fascinating"
    ]
    
    try:
        result = await adapter.embed_texts(texts)
        print(f"Embedded {len(texts)} texts")
        print(f"Embedding dimensions: {result.dimensions}")
        print(f"Usage: {result.usage}")
        
        # Embed a single query
        query_embedding = await adapter.embed_query("What is Python?")
        print(f"Query embedding dimensions: {len(query_embedding)}")
        
    except Exception as e:
        print(f"Error (likely missing API key): {e}")


async def factory_usage_example():
    """Usage with factory pattern."""
    print("\n=== Factory Usage Example ===")
    
    # Get available providers
    providers = EmbeddingAdapterFactory.get_available_providers()
    print(f"Available providers: {providers}")
    
    # Create using factory
    adapter = EmbeddingAdapterFactory.create_adapter(
        provider="openai",
        model="text-embedding-3-small",
        api_key="fake-key-for-demo"  # Won't work for actual embedding
    )
    
    print(f"Adapter model: {adapter.config.model}")
    print(f"Max batch size: {adapter.max_batch_size}")
    print(f"Dimensions: {adapter.dimensions}")


async def preset_example():
    """Preset model usage example."""
    print("\n=== Preset Example ===")
    
    # Show available presets
    print(f"Popular models: {POPULAR_MODELS}")
    
    # Create from preset
    adapter = create_adapter_from_preset(
        provider="openai",
        size="small",
        api_key=os.getenv("OPENAI_API_KEY")
    )
    
    print(f"Preset adapter model: {adapter.config.model}")


async def config_example():
    """Configuration example."""
    print("\n=== Configuration Example ===")
    
    # Create custom config
    config = EmbeddingConfig(
        model="text-embedding-3-large",
        api_key=os.getenv("OPENAI_API_KEY"),
        max_retries=5,
        timeout=60,
        extra_config={"custom_param": "value"}
    )
    
    adapter = EmbeddingAdapterFactory._adapters["openai"](config)
    print(f"Custom config model: {adapter.config.model}")
    print(f"Extra config: {adapter.config.extra_config}")


async def batch_example():
    """Batch processing example."""
    print("\n=== Batch Processing Example ===")
    
    adapter = EmbeddingAdapterFactory.create_adapter(
        provider="openai",
        model="text-embedding-3-small",
        api_key=os.getenv("OPENAI_API_KEY")
    )
    
    # Generate many texts (would be processed in batches automatically)
    texts = [f"Sample text number {i}" for i in range(150)]
    
    print(f"Would embed {len(texts)} texts in batches of {adapter.max_batch_size}")
    
    # Note: Actual embedding would require valid API key
    # result = await adapter.embed_texts(texts)


async def main():
    """Run all examples."""
    try:
        await basic_usage_example()
        await factory_usage_example()
        await preset_example()
        await config_example()
        await batch_example()
        
    except Exception as e:
        print(f"Error: {e}")
        print("Note: Set OPENAI_API_KEY environment variable for actual embedding calls")


if __name__ == "__main__":
    asyncio.run(main())