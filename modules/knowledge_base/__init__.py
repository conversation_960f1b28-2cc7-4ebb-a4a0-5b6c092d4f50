"""
Knowledge Base module for RAG system.
Provides document processing, chunking, embedding, and retrieval capabilities.
"""

from .services.knowledge_base_service import KnowledgeBaseService
from .processors.base_processor import BaseDocumentProcessor, ProcessingResult
from .processors.document_processor_factory import DocumentProcessorFactory
from .chunking.contextual_chunker import ContextualChunker

__all__ = [
    'KnowledgeBaseService',
    'BaseDocumentProcessor',
    'ProcessingResult', 
    'DocumentProcessorFactory',
    'ContextualChunker'
]