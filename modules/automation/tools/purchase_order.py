from datetime import datetime, date
from typing import Dict, List, Optional, Union, Any

from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext
from pydantic_ai.settings import ModelSettings

from modules.agentic.prompts import PromptBaseModel
from utils.prompt_template import PromptTemplate

purchase_order_prompt = PromptTemplate(
    """
    You are an agricultural purchase order generation specialist. You will analyze advisor notes from farm consultations and generate formal purchase orders for agricultural inputs.

    <RoleDefinition>
    As an agricultural purchase order specialist, you understand:
    - Seasonal farming cycles and appropriate timing for different agricultural inputs
    - Common product combinations and application rates for different crops
    - Standard delivery windows based on growing seasons
    - Typical payment terms in the agricultural sector
    - Technical language used in farm advisor notes
    </RoleDefinition>

    <TaskDescription>
    You will convert informal advisor notes into structured purchase orders by:
    1. Identifying all products mentioned in the notes with their quantities
    2. Retrieving accurate product details and pricing from the ERP system
    3. Retrieving client and advisor information from the CRM system
    4. Calculating all costs including applicable discounts and taxes
    5. Setting appropriate delivery timeframes based on the agricultural context
    6. Applying any client-specific payment terms or preferences
    7. Including relevant technical notes about seed treatments, follow-up services, etc.
    </TaskDescription>

    <AdvisorNoteInterpretation>
    Advisor notes typically contain:
    - Crop types and hectares to be planted/treated
    - Product recommendations with quantities (often in units/ha)
    - Specific treatment requests
    - Delivery timing preferences
    - Any special conditions or follow-up services
    - Price negotiations or discount discussions

    You must extract all relevant information from these sometimes informal notes, making reasonable inferences based on agricultural context when details are implicit.
    </AdvisorNoteInterpretation>

    <CalculationRules>
    - Calculate quantities based on application rates × area when appropriate
    - Apply any mentioned discounts to individual line items
    - Always calculate line totals as: quantity × unit price × (1 - discount%)
    - Round all monetary values to 2 decimal places
    - Calculate VAT based on the applicable rate for each product category
    - Ensure the total TTC (tax included) is the sum of subtotal HT plus VAT
    </CalculationRules>

    <OutputFormat>
    {
      "purchase_order": {
        "campaign_year": null,
        "client_info": {
          "farmer_name": "",
          "farm_name": "",
          "farm_address": "",
          "phone": "",
          "email": "",
          "siret_number": "",
          "client_code": ""
        },
        "advisor_info": {
          "name": "",
          "position": "",
          "company": "",
          "email": "",
          "phone": ""
        },
        "order_items": [
          {
            "product_ref": "",
            "description": "",
            "quantity": 0,
            "unit": "",
            "unit_price_ht": 0.0,
            "discount": "",
            "line_total_ht": 0.0
          }
        ],
        "totals": {
          "subtotal_ht": 0.0,
          "vat_rate": "",
          "vat_amount": 0.0,
          "total_ttc": 0.0
        },
        "delivery": {
          "delivery_address": "",
          "expected_date_range": "",
          "carrier": "",
          "delivery_conditions": ""
        },
        "payment_terms": {
          "payment_type": "",
          "payment_method": "",
          "rib_provided": false,
          "specific_discounts": ""
        },
        "additional_notes": {
          "seed_treatment": "",
          "crop_follow_up": "",
          "order_valid_until": ""
        },
        "signatures": {
          "signed_in": "",
          "signed_on": "",
          "client_signature": false,
          "advisor_signature": {
            "name": "",
            "company": ""
          }
        }
      }
    }
    </OutputFormat>

    <ProductCodeInterpretation>
    Agricultural product codes typically follow these patterns:
    - SEM-*: Seeds (e.g., SEM-TR240 for sunflower variety TR240)
    - ENG-*: Fertilizers (e.g., ENG-SOP20 for 20-10-10 fertilizer)
    - PHY-*: Crop protection products (fungicides, herbicides, insecticides)
    - BIO-*: Biological products
    - MAT-*: Equipment or materials

    When product codes aren't explicitly mentioned, identify the product type and search for matching products in the ERP system.
    </ProductCodeInterpretation>

    <DeliveryGuidelines>
    - Set delivery windows based on the application timing for the products
    - For seeds: typically 2-4 weeks before expected planting date
    - For fertilizers: typically 1-2 weeks before application
    - For crop protection: can be just-in-time or with fertilizer delivery
    - Consider weather conditions and seasonal farm operations in your region
    </DeliveryGuidelines>

    <OrderValidity>
    Purchase orders should generally be valid for:
    - Seeds: 30 days from issue or until planting season starts
    - Fertilizers: 30-45 days from issue
    - Crop protection: 15-30 days from issue (prices fluctuate more)
    - Set appropriate validity periods based on the products and seasonal context
    </OrderValidity>

    <Instructions>
    - Always use the provided advisor notes as your primary source of information
    - Query the ERP system for each product to get accurate pricing and details
    - Query the CRM system to get correct client and advisor information
    - Calculate all financial totals with precision
    - Format dates consistently as YYYY-MM-DD
    - Use the current year for campaign_year if not otherwise specified
    - Make reasonable agricultural inferences when information is incomplete
    - Include all relevant technical details about treatments and follow-ups
    </Instructions>

    Campaign year: {{campaign_year}}
    Client ID: {{client_id}}
    Advisor ID: {{advisor_id}}
    
    Advisor notes: <<{{advisor_notes}}>>
    """
)


class PurchaseOrderInputs(PromptBaseModel):
    advisor_notes: str
    client_id: Optional[str] = None
    advisor_id: Optional[str] = None
    campaign_year: Optional[int] = Field(default_factory=lambda: datetime.now().year)


class ClientInfo(BaseModel):
    farmer_name: str = ""
    farm_name: str = ""
    farm_address: str = ""
    phone: str = ""
    email: str = ""
    siret_number: str = ""
    client_code: str = ""


class AdvisorInfo(BaseModel):
    name: str = ""
    position: str = ""
    company: str = ""
    email: str = ""
    phone: str = ""


class OrderItem(BaseModel):
    product_ref: str = ""
    description: str = ""
    quantity: float = 0  # Changed to float to handle partial units
    unit: str = ""
    unit_price_ht: float = 0.0
    discount: Optional[str] = None
    line_total_ht: float = 0.0


class Totals(BaseModel):
    subtotal_ht: float = 0.0
    vat_rate: str = ""
    vat_amount: float = 0.0
    total_ttc: float = 0.0


class Delivery(BaseModel):
    delivery_address: str = ""
    expected_date_range: str = ""
    carrier: str = ""
    delivery_conditions: str = ""


class PaymentTerms(BaseModel):
    payment_type: str = ""
    payment_method: str = ""
    rib_provided: bool = False
    payment_due_date: Optional[str] = None  # Added payment due date
    specific_discounts: str = ""


class AdditionalNotes(BaseModel):
    seed_treatment: str = ""
    crop_follow_up: str = ""
    order_valid_until: str = ""
    technical_recommendations: str = ""  # Added field for technical advice


class AdvisorSignature(BaseModel):
    name: str = ""
    company: str = ""


class Signatures(BaseModel):
    signed_in: str = ""
    signed_on: str = ""
    client_signature: bool = False
    advisor_signature: AdvisorSignature = Field(default_factory=AdvisorSignature)


class PurchaseOrder(BaseModel):
    campaign_year: Optional[int] = None
    order_date: str = Field(default_factory=lambda: date.today().isoformat())  # Added order date
    order_number: str = ""  # Added order number
    client_info: ClientInfo = Field(default_factory=ClientInfo)
    advisor_info: AdvisorInfo = Field(default_factory=AdvisorInfo)
    order_items: List[OrderItem] = Field(default_factory=list)
    totals: Totals = Field(default_factory=Totals)
    delivery: Delivery = Field(default_factory=Delivery)
    payment_terms: PaymentTerms = Field(default_factory=PaymentTerms)
    additional_notes: AdditionalNotes = Field(default_factory=AdditionalNotes)
    signatures: Signatures = Field(default_factory=Signatures)


class ProductInfo(BaseModel):
    product_ref: str
    description: str
    unit: str
    unit_price_ht: float
    available_stock: float
    vat_rate: float
    recommended_rate: Optional[str] = None  # Added recommended application rate
    typical_discount: Optional[str] = None
    category: str  # Added product category


class ClientData(BaseModel):
    client_info: ClientInfo
    advisor_info: AdvisorInfo
    payment_preferences: Optional[Dict[str, Any]] = None
    delivery_preferences: Optional[Dict[str, Any]] = None
    purchase_history: Optional[List[Dict[str, Any]]] = None
    farm_details: Optional[Dict[str, Any]] = None  # Added farm details like size, crops, etc.


purchase_order = Agent(
    "mistral:mistral-large-latest",
    deps_type=PurchaseOrderInputs,
    output_type=PurchaseOrder,
    model_settings=ModelSettings(parallel_tool_calls=True)
)


@purchase_order.system_prompt
def system_prompt(ctx: RunContext[PurchaseOrderInputs]) -> str:
    sp = purchase_order_prompt.format(PurchaseOrderInputs(client_id=ctx.deps.client_id, advisor_id=ctx.deps.advisor_id,
                                                          campaign_year=ctx.deps.campaign_year,
                                                          advisor_notes=ctx.deps.advisor_notes))

    return sp


@purchase_order.tool
async def ERP_system(ctx: RunContext[PurchaseOrderInputs], product_ref: Optional[str] = None,
                     search_term: Optional[str] = None, category: Optional[str] = None) -> Dict[
    str, Union[List[ProductInfo], ProductInfo]]:
    """
    Get product list and pricing data from ERP system.

    Args:
        product_ref: Optional specific product reference to look up
        search_term: Optional search term to find matching products
        category: Optional category to filter products (seeds, fertilizers, crop_protection, etc.)

    Returns:
        Dictionary containing product information for specific product or list of matching products
    """
    # Extended realistic product catalog
    sample_products = {
        # Seeds
        "SEM-TR240": ProductInfo(
            product_ref="SEM-TR240",
            description="Sunflower seeds TR240 (high oleic)",
            unit="bags of 80,000 seeds",
            unit_price_ht=125.00,
            available_stock=150,
            vat_rate=10.0,
            typical_discount="2%",
            recommended_rate="70,000 seeds/ha",
            category="seeds"
        ),
        "SEM-BL320": ProductInfo(
            product_ref="SEM-BL320",
            description="Winter wheat BL320 (certified)",
            unit="kg",
            unit_price_ht=0.95,
            available_stock=8000,
            vat_rate=10.0,
            typical_discount="3% above 1000kg",
            recommended_rate="180-200 kg/ha",
            category="seeds"
        ),
        "SEM-MZ189": ProductInfo(
            product_ref="SEM-MZ189",
            description="Corn seeds MZ189 (treated)",
            unit="bags of 50,000 seeds",
            unit_price_ht=110.00,
            available_stock=200,
            vat_rate=10.0,
            typical_discount="2%",
            recommended_rate="80,000-90,000 seeds/ha",
            category="seeds"
        ),

        # Fertilizers
        "ENG-SOP20": ProductInfo(
            product_ref="ENG-SOP20",
            description="Fertilizer SOP 20-10-10",
            unit="pallets (600 kg/pallet)",
            unit_price_ht=520.00,
            available_stock=25,
            vat_rate=10.0,
            typical_discount="2%",
            recommended_rate="300-500 kg/ha",
            category="fertilizers"
        ),
        "ENG-NPK15": ProductInfo(
            product_ref="ENG-NPK15",
            description="Fertilizer NPK 15-15-15",
            unit="kg",
            unit_price_ht=0.82,
            available_stock=12000,
            vat_rate=10.0,
            typical_discount="3% above 2000kg",
            recommended_rate="250-400 kg/ha",
            category="fertilizers"
        ),
        "ENG-UREE46": ProductInfo(
            product_ref="ENG-UREE46",
            description="Nitrogen fertilizer UREE 46%",
            unit="ton",
            unit_price_ht=450.00,
            available_stock=40,
            vat_rate=10.0,
            typical_discount="2%",
            recommended_rate="150-250 kg/ha",
            category="fertilizers"
        ),

        # Crop protection
        "PHY-FOLIAX": ProductInfo(
            product_ref="PHY-FOLIAX",
            description="Fungicide Foliax Duo",
            unit="L",
            unit_price_ht=23.50,
            available_stock=200,
            vat_rate=20.0,
            recommended_rate="0.8-1.2 L/ha",
            category="crop_protection"
        ),
        "PHY-HERBMAX": ProductInfo(
            product_ref="PHY-HERBMAX",
            description="Herbicide HerbMax Plus",
            unit="L",
            unit_price_ht=18.75,
            available_stock=350,
            vat_rate=20.0,
            recommended_rate="2-3 L/ha",
            category="crop_protection"
        ),
        "PHY-INSECTX": ProductInfo(
            product_ref="PHY-INSECTX",
            description="Insecticide InsectX",
            unit="kg",
            unit_price_ht=45.20,
            available_stock=120,
            vat_rate=20.0,
            recommended_rate="0.25 kg/ha",
            category="crop_protection"
        ),

        # Biological products
        "BIO-MYCO": ProductInfo(
            product_ref="BIO-MYCO",
            description="Mycorrhizal fungi soil improver",
            unit="kg",
            unit_price_ht=18.50,
            available_stock=200,
            vat_rate=10.0,
            recommended_rate="10 kg/ha",
            category="biological"
        )
    }

    if product_ref and product_ref in sample_products:
        return {"product": sample_products[product_ref]}
    elif search_term:
        results = [product for ref, product in sample_products.items()
                   if search_term.lower() in ref.lower() or search_term.lower() in product.description.lower()]
        return {"products": results}
    elif category:
        results = [product for ref, product in sample_products.items()
                   if product.category.lower() == category.lower()]
        return {"products": results}
    else:
        return {"products": list(sample_products.values())}


@purchase_order.tool
async def CRM_system(ctx: RunContext[PurchaseOrderInputs], client_id: Optional[str] = None,
                     advisor_id: Optional[str] = None) -> Dict[str, ClientData]:
    """
    Get agricultural information from CRM system.

    Args:
        client_id: Optional client ID to look up specific client information
        advisor_id: Optional advisor ID to look up specific advisor information

    Returns:
        Dictionary containing client and associated advisor information
    """
    # Sample client data with more realistic farm information
    sample_clients = {
        "CLI-78452": ClientData(
            client_info=ClientInfo(
                farmer_name="Jean Dupont",
                farm_name="GAEC des Champs Fleuris",
                farm_address="124 Route des Moissons, 31450 Montesquieu",
                phone="06 12 34 56 78",
                email="<EMAIL>",
                siret_number="**************",
                client_code="CLI-78452"
            ),
            advisor_info=AdvisorInfo(
                name="Claire Martin",
                position="Technical Sales Advisor",
                company="AgriConseil Sud-Ouest",
                email="<EMAIL>",
                phone="07 89 12 34 56"
            ),
            payment_preferences={
                "preferred_method": "Bank transfer",
                "rib_provided": True,
                "payment_terms": "30 days",
                "credit_limit": 10000.00
            },
            delivery_preferences={
                "preferred_carrier": "LogiAgri",
                "delivery_conditions": "Appointment required, heavy vehicle access OK",
                "alternative_address": None
            },
            farm_details={
                "total_area": 120,  # hectares
                "main_crops": ["wheat", "sunflower", "corn"],
                "soil_type": "Clay-loam",
                "irrigation": True,
                "organic": False
            }
        ),
        "CLI-65873": ClientData(
            client_info=ClientInfo(
                farmer_name="Marie Lefèvre",
                farm_name="EARL Lefèvre",
                farm_address="35 Chemin des Vignes, 33420 Rauzan",
                phone="06 23 45 67 89",
                email="<EMAIL>",
                siret_number="**************",
                client_code="CLI-65873"
            ),
            advisor_info=AdvisorInfo(
                name="Thomas Bernard",
                position="Agricultural Consultant",
                company="AgriConseil Sud-Ouest",
                email="<EMAIL>",
                phone="07 78 91 23 45"
            ),
            payment_preferences={
                "preferred_method": "Check",
                "rib_provided": False,
                "payment_terms": "45 days",
                "credit_limit": 8000.00
            },
            delivery_preferences={
                "preferred_carrier": "FarmExpress",
                "delivery_conditions": "Delivery before noon preferred",
                "alternative_address": "Hangar Sud, Route D14, 33420 Rauzan"
            },
            farm_details={
                "total_area": 85,  # hectares
                "main_crops": ["vineyard", "wheat"],
                "soil_type": "Sandy-loam",
                "irrigation": False,
                "organic": True
            }
        )
    }

    # Default to the first client if none specified
    client_data = sample_clients.get(client_id) if client_id else next(iter(sample_clients.values()))

    return {"client_data": client_data}


@purchase_order.tool
async def calculate_order_totals(ctx: RunContext[PurchaseOrderInputs],
                                 items: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Calculate the totals for a purchase order.

    Args:
        items: List of order items with quantities, prices, and discounts

    Returns:
        Dictionary with subtotal, VAT, and total amounts
    """
    subtotal_ht = 0.0
    vat_amounts = {}  # Track VAT by rate for multiple VAT rates

    # Process each item and calculate line totals
    processed_items = []
    for item in items:
        quantity = float(item.get("quantity", 0))
        unit_price = float(item.get("unit_price_ht", 0.0))
        discount = item.get("discount", None)
        vat_rate = float(item.get("vat_rate", 10.0))  # Default to 10% if not specified

        # Calculate discount percentage if provided
        discount_pct = 0.0
        if discount is not None:
            if isinstance(discount, str) and "%" in discount:
                try:
                    discount_pct = float(discount.replace("%", "").strip()) / 100
                except ValueError:
                    discount_pct = 0.0
            elif isinstance(discount, (int, float)):
                # Assume it's already a decimal percentage
                discount_pct = float(discount)
                if discount_pct > 1:  # If entered as a whole number percentage
                    discount_pct = discount_pct / 100

        # Calculate line total with discount
        line_total = quantity * unit_price * (1 - discount_pct)
        line_total = round(line_total, 2)  # Round to 2 decimal places

        processed_item = item.copy()
        processed_item["line_total_ht"] = line_total
        processed_items.append(processed_item)

        subtotal_ht += line_total

        # Track VAT by rate
        vat_key = f"{vat_rate}%"
        if vat_key not in vat_amounts:
            vat_amounts[vat_key] = 0
        vat_amounts[vat_key] += line_total * (vat_rate / 100)

    # Calculate total VAT and determine the predominant VAT rate
    total_vat = sum(vat_amounts.values())
    predominant_vat_rate = max(vat_amounts.items(), key=lambda x: x[1])[0] if vat_amounts else "10%"

    # Round values
    subtotal_ht = round(subtotal_ht, 2)
    total_vat = round(total_vat, 2)
    total_ttc = round(subtotal_ht + total_vat, 2)

    return {
        "processed_items": processed_items,
        "totals": {
            "subtotal_ht": subtotal_ht,
            "vat_rate": predominant_vat_rate,  # Use the predominant VAT rate for display
            "vat_amount": total_vat,
            "total_ttc": total_ttc
        },
        "vat_breakdown": {rate: round(amount, 2) for rate, amount in vat_amounts.items()}
    }


@purchase_order.tool
async def generate_order_number(ctx: RunContext[PurchaseOrderInputs],
                                client_code: str,
                                campaign_year: int) -> str:
    """
    Generate a unique purchase order number based on client code and date.

    Args:
        client_code: Client's unique identifier code
        campaign_year: The campaign year for the order

    Returns:
        A formatted order number string
    """
    today = datetime.now()
    # Format: PO-[ClientCode]-[Year][Month][Day]-[Random 3 digits]
    import random
    random_suffix = f"{random.randint(0, 999):03d}"
    order_number = f"PO-{client_code}-{today.year}{today.month:02d}{today.day:02d}-{random_suffix}"

    return order_number
