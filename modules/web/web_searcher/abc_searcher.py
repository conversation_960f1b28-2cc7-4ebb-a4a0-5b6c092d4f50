from abc import ABC, abstractmethod
from enum import Enum
from typing import Optional, Union, List, Dict, Any, Literal

from const import SafeSearchLevel, FreshnessOption
from schemas import BaseModel



class ResultType(str, Enum):
    WEBPAGE = "webpage"
    NEWS = "news"
    VIDEO = "video"
    IMAGE = "image"
    DISCUSSION = "discussion"
    LOCATION = "location"
    INFOBOX = "infobox"
    COMPUTATION = "computation"
    ENTITY = "entity"
    RELATED_SEARCH = "relatedSearch"
    SPELL_SUGGESTION = "spellSuggestion"
    TIMEZONE = "timezone"


class TextFormat(str, Enum):
    RAW = "Raw"
    HTML = "Html"


class MeasurementUnit(str, Enum):
    METRIC = "metric"
    IMPERIAL = "imperial"


# Define result structure types
class SearchResult(BaseModel):
    """Base model for all search results"""
    type: ResultType
    title: str
    url: Optional[str] = None


class WebpageResult(SearchResult):
    """Model for webpage search results"""
    type: Literal[ResultType.WEBPAGE]
    snippet: Optional[str] = None
    display_url: Optional[str] = None
    date_published: Optional[str] = None


class NewsResult(SearchResult):
    """Model for news search results"""
    type: Literal[ResultType.NEWS]
    description: Optional[str] = None
    publisher: Optional[str] = None
    date_published: Optional[str] = None


class VideoResult(SearchResult):
    """Model for video search results"""
    type: Literal[ResultType.VIDEO]
    description: Optional[str] = None
    thumbnail: Optional[str] = None
    publisher: Optional[str] = None
    duration: Optional[str] = None


class ImageResult(SearchResult):
    """Model for image search results"""
    type: Literal[ResultType.IMAGE]
    thumbnail: Optional[str] = None
    source_page: Optional[str] = None


class DiscussionResult(SearchResult):
    """Model for discussion search results"""
    type: Literal[ResultType.DISCUSSION]
    snippet: Optional[str] = None
    source: Optional[str] = None
    date_published: Optional[str] = None


class LocationResult(SearchResult):
    """Model for location search results"""
    type: Literal[ResultType.LOCATION]
    address: Optional[str] = None
    phone: Optional[str] = None
    id: Optional[str] = None


class InfoboxResult(SearchResult):
    """Model for infobox search results"""
    type: Literal[ResultType.INFOBOX]
    description: Optional[str] = None
    image: Optional[str] = None


# Union type for all result types
SearchResultType = Union[
    WebpageResult, NewsResult, VideoResult, ImageResult,
    DiscussionResult, LocationResult, InfoboxResult
]


class AbstractSearcher(ABC):
    """
    Abstract base class for web search implementations.
    Defines a common interface for all search services.
    """

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the searcher with an API key.

        Args:
            api_key: Authentication key for the search service
        """
        self.api_key = api_key

    @abstractmethod
    async def search(
            self,
            query: str,
            count: Optional[int] = 10,
            offset: Optional[int] = 0,
            safe_search: Optional[SafeSearchLevel] = SafeSearchLevel.MODERATE,
            language: Optional[str] = "en",
            country: Optional[str] = "US",
            freshness: Optional[Union[FreshnessOption, str]] = None,
            timeout: Optional[float] = 10.0,
            **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Perform a web search using the specific search API.

        Args:
            query: The search query term
            count: Number of results to return
            offset: Number of results to skip (for pagination)
            safe_search: Content filtering level (off, moderate, strict)
            language: Language code for search results
            country: Country code for localized results
            freshness: Filter for result recency
            timeout: Request timeout in seconds
            **kwargs: Additional service-specific parameters

        Returns:
            List of search result dictionaries
        """
        pass

    @abstractmethod
    def _process_response(self, response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Process the API response into a standardized format.

        Args:
            response: The raw API response

        Returns:
            List of standardized search result dictionaries
        """
        pass

    def format_for_llm(self, results: List[Dict[str, Any]], max_results: int = 5) -> str:
        """
        Format search results for consumption by language models.

        Args:
            results: List of search result dictionaries
            max_results: Maximum number of results to include

        Returns:
            Formatted string with search results
        """
        if not results:
            return "No search results found."

        results = results[:max_results]  # Limit to max_results
        formatted = ""

        for i, result in enumerate(results, 1):
            result_type = result.get("type", "unknown")
            title = result.get("title", "No title")

            formatted += f"{i}. {title}\n"

            # Common fields across all result types
            if "url" in result:
                formatted += f"   URL: {result.get('url', 'N/A')}\n"

            # Type-specific fields
            if result_type == ResultType.WEBPAGE:
                formatted += f"   Snippet: {result.get('snippet', 'N/A')}\n"
            elif result_type == ResultType.NEWS:
                formatted += f"   Description: {result.get('description', 'N/A')}\n"
                formatted += f"   Publisher: {result.get('publisher', 'N/A')}\n"
            elif result_type == ResultType.VIDEO:
                formatted += f"   Description: {result.get('description', 'N/A')}\n"
                formatted += f"   Publisher: {result.get('publisher', 'N/A')}\n"
            elif result_type == ResultType.IMAGE:
                formatted += f"   Source page: {result.get('source_page', 'N/A')}\n"
            elif result_type == ResultType.DISCUSSION:
                formatted += f"   Snippet: {result.get('snippet', 'N/A')}\n"
                formatted += f"   Source: {result.get('source', 'N/A')}\n"
            elif result_type == ResultType.LOCATION:
                formatted += f"   Address: {result.get('address', 'N/A')}\n"
                formatted += f"   Phone: {result.get('phone', 'N/A')}\n"
            elif result_type == ResultType.INFOBOX:
                formatted += f"   Description: {result.get('description', 'N/A')}\n"

            formatted += "\n"

        return f"<WebSearchResults>\n<{formatted}\n</WebSearchResults>\n\n"
