# Ekumen-AI

## Vision
**Vision Statement:** To empower every farmer with the tools, knowledge, and support they need to transform their practices sustainably, ensuring agricultural resilience, environmental stewardship, and economic prosperity for generations to come.

**Our Belief:** We believe that farmers are the cornerstone of global food systems and environmental sustainability. By placing farmers at the center of agricultural transformation and equipping them with accessible, adaptive, and AI-driven tools, we can accelerate the adoption of sustainable practices, improve livelihoods, and create a positive impact on the planet.

## Project Overview
Ekumen-AI is an AI-powered agricultural assistant platform built with FastAPI and Pydantic-AI. It provides intelligent tools for farmers to adopt sustainable practices through context-aware recommendations, weather analysis, and agricultural product information.

## MVP Features

### 🤖 AI Assistant System
- **Multi-agent architecture** with selective query routing
- **Streaming chat responses** via Server-Sent Events (SSE)
- **Agricultural data** processing and analysis
- **Context-aware recommendations** for sustainable farming practices
- **Smart response processing** with structured JSON formatting

### 🌾 Agricultural Tools
- **E-Phy Data Integration**: Comprehensive access to French agricultural product database
  - Search agricultural products with intelligent fuzzy matching
  - Retrieve detailed product information with enriched context
  - Find crop protection solutions with context-aware recommendations
  - Validate product usage for specific crop-pest combinations
  - Get substance product information with usage context
  - Retrieve actionable safety information for agricultural product handling

- **Weather Integration**: Current, forecast, and historical weather data for agricultural planning
- **Personal Data Management**: Farmer profile and field management
- **Multi-provider Search**: Enhanced web search capabilities

### 🏭 Automation System
- **Purchase Order Generation**: Automatically generate purchase orders from advisor notes
- **File Processing**: Support for multiple file formats with MinIO object storage
- **Multi-format Support**: UTF-8, Latin-1, CP1252 encoding support for French text

### 📊 Data Pipeline
- **E-Phy Data Loading**: Automated loading of French agricultural product data
- **Batch Processing**: Efficient processing of large CSV datasets
- **Data Enhancement**: Automatic data enrichment and normalization

## API Endpoints

### Chat & AI Assistant
- `POST /chat/stream` - Streaming chat with Server-Sent Events
- `POST /chat` - Non-streaming chat responses  

### Automation
- `POST /api/automations/purchase-order` - Generate purchase orders from advisor notes

### Data Pipeline
- `POST /api/pipelines/e-phy` - Load E-Phy agricultural data (local files or MinIO)

### Health Check
- `GET /health` - Application health status and timestamp

## Requirements
- Python 3.12
- pip3
- virtualenv: [virtualenv on PyPI](https://pypi.org/project/virtualenv/)

## Installation
All commands provided are for terminal use on Linux or Mac platforms. For Windows, please refer to the official Python [venv documentation](https://docs.python.org/3/library/venv.html).

1. **Clone the project**
    ```bash
    git clone https://github.com/yourusername/ekumen-ai.git
    cd ekumen-ai
    ```

2. **Create a virtual environment**
    ```bash
    python3 -m venv venv
    ```

3. **Activate the virtual environment**
    ```bash
    source venv/bin/activate
    ```

4. **Install dependencies**
    ```bash
    pip3 install -r requirements.txt
    ```

## Setting up Environment Variables
Set environment variables for configuration. Refer to [FASTAPI settings guide](https://fastapi.tiangolo.com/advanced/settings/#reading-a-env-file) for detailed instructions. You can create a `.env` file and use it with environment variables required by your application.

## Execution
Run the application using `uvicorn`:
```bash
uvicorn main:app --reload
```
Access the application at [http://127.0.0.1:8000/](http://127.0.0.1:8000/).

For available routes, check `main.py` in the project root folder.

## Docker Setup
The project includes Docker configuration for easy setup and deployment.

### Using Docker Compose (Recommended)
1. **Copy the environment variables example file:**
   ```bash
   cp env.example .env
   ```

2. **Edit the `.env` file with your API keys and settings**

3. **Build and run with Docker Compose:**
   ```bash
   docker-compose up -d
   ```

4. Access the application at [http://localhost:8000/](http://localhost:8000/)

### Manual Docker Setup
- **Build the Docker image:**
  ```bash
  docker build -t ekumen-ai .
  ```
- **Run the Docker container:**
  ```bash
  docker run -p 8000:8000 --env-file .env ekumen-ai
  ```

## Contributing
Contributions from the community are welcome. To contribute:
- Fork the repository
- Create a new branch: `git checkout -b feature-branch`
- Make changes
- Commit and push: `git push origin feature-branch`
- Open a Pull Request

## License
This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.